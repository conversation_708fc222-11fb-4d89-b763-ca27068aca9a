# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Apolus is a Laravel-based order processing and marketplace integration system that automates order assignment across 30+ European e-commerce platforms (Fnac, Cdiscount, DocMorris, ManoMano, Mirakl-based platforms). It acts as middleware between marketplace APIs and BigHub internal order management, providing intelligent seller matching, commission calculation, and order lifecycle management.

## Critical Project Structure

The project has **two Laravel applications** with different purposes:

### 1. Main Service: `service.srv/laravel/app/`
- **Primary order processing application**
- Contains the core business logic for marketplace integrations
- All development commands should be run from this directory
- Uses Docker via docker-compose for deployment

### 2. Cloud Application: `cloud/`
- Separate Laravel app for cloud-related features
- Has its own dependencies and configuration

**IMPORTANT**: Always clarify which Laravel app is being referenced. Default to `service.srv/laravel/app/` for order processing work.

## Common Commands

### Development Setup (service.srv)
```bash
cd service.srv/laravel/app
composer install
cp .env.example .env
php artisan key:generate
touch database/database.sqlite
php artisan migrate
```

### Running the Application
```bash
# Start development server with all services (from service.srv/laravel/app/)
composer run dev
# This runs: server, queue worker, log viewer, and vite concurrently

# Or start just the server
php artisan serve

# Using Docker (from project root)
docker-compose up -d
# Service available at http://localhost:8082
```

### Testing
```bash
# Run all tests
php artisan test

# Run specific test suite
php artisan test --testsuite=Unit
php artisan test --testsuite=Feature

# Run with coverage
php artisan test --coverage

# Run single test file
php artisan test tests/Feature/OrderControllerTest.php

# Run specific test
php artisan test --filter=test_seller_matching
```

### Code Quality
```bash
# Format code with Laravel Pint
./vendor/bin/pint

# Check formatting without changes
./vendor/bin/pint --test

# View logs in real-time
php artisan pail
```

### Database Operations
```bash
# Reset and migrate database
php artisan migrate:fresh

# Reset and seed
php artisan migrate:fresh --seed

# Rollback migrations
php artisan migrate:rollback
```

## Architecture Overview

### Layered Architecture Pattern

**Controllers** → **Services** → **Models (Flow Classes)** → **External APIs**

- **Controllers** (`app/Http/Controllers/`): HTTP request handling
  - `OrderController`: Main order processing logic
  - `LinkController`: Token validation for landing pages

- **Services** (`app/Services/`): Core business logic
  - `OrderFlowService`: EAN extraction, country identification, commission calculation

- **Models/Flow Classes** (`app/Models/`): Marketplace-specific integration logic
  - `FlowBighub`: BigHub API integration
  - `FlowFnac`, `FlowCdiscount`, `FlowDocMorris`, `FlowManoMano`, `FlowStockly`: Marketplace-specific processors
  - Each Flow class handles marketplace-specific payload generation and API calls

- **Domain Models**:
  - `ProductChannel`: Product-marketplace-seller relationships
  - `FlowOrders`: Order state tracking
  - `User`/`UserClient`: User authentication and seller management

### Order Processing Flow

1. **Order Ingestion**: Receive order from marketplace webhook
2. **EAN Extraction**: Extract product EAN codes from SKUs
3. **Seller Matching**: Find active seller with matching EAN, channel, and price
4. **Price Validation**: Exact match or ≤5% tolerance
5. **Commission Calculation**: Validate BigHub commission ≥ marketplace commission
6. **Payload Generation**: Create marketplace-specific payload
7. **Order Assignment**: Update BigHub with assigned seller
8. **Notification**: Send email to seller

### Key Business Rules

- **Watch Products**: All products with "relógio" in title → assigned to seller ID 843
- **Multi-Product Orders**: All products must match same seller for assignment
- **Commission Rule**: BigHub commission must be ≥ marketplace commission
- **Price Matching Priority**:
  1. Exact price match
  2. Price ≤ order price
  3. Price ≤ order price × 1.05 (5% tolerance)
  4. Forward to support if no match

### Marketplace Types

**Mirakl Marketplaces** (25+ platforms): Standardized processing via `processMarketplaceMirakls()`
- pixmania, shopapotheke, planetahuerto, alternate, bestmarine, etc.

**Special Marketplaces**: Custom processing via specific Flow classes
- fnac, cdiscount, docmorris, manomano, stockly

## Known Issues and Error Handling

The codebase has critical error handling issues documented in `news/REFERENCIA_PROJETO.md`:

### Critical Areas Needing Error Handling
- **OrderController**: Multiple foreach loops without try-catch (lines 169, 182, 419, 430, 728, 740, 864, 876)
- **Property Access**: Unsafe access to nested properties like `$order->marketplace->name`, `$order->channel->code`
- **Flow Classes**: foreach loops in FlowFnac (line 48), FlowCdiscount (line 40), FlowDocMorris (lines 47-48)
- **Die/Exit statements**: FlowBighub line 208, FlowFnac line 131 - these stop execution

When modifying order processing code, always add:
- Null checks before accessing nested properties
- Try-catch blocks around foreach loops
- Validation of API response structure
- Proper error logging instead of die/exit

## Environment Configuration

### Development (.env)
```bash
APP_ENV=local
APP_DEBUG=true
DB_CONNECTION=sqlite
DB_DATABASE=/path/to/database/database.sqlite
CACHE_DRIVER=file
SESSION_DRIVER=file
QUEUE_CONNECTION=sync
MAIL_MAILER=log

# Required API keys
BIGHUB_API_KEY=your-api-key
BIGHUB_BEARER_TOKEN=your-bearer-token
BIGHUB_BASE_URL=https://app.bighub.store/
```

### Docker Deployment
- Main service runs in Docker container with PHP-FPM
- Nginx reverse proxy on port 8082
- Uses external network `wonpey-net`
- Service directory mounted at `/srv`

## External API Integrations

### BigHub API (app.bighub.store)
- **Authentication**: `x-api-key` header
- **Key Endpoints**:
  - `GET /api/orders/user/{id}` - Fetch orders for processing
  - `PUT /api/orders/{id}/update-user/{user_id}` - Assign seller
  - `POST /api/v2/sales/orders` - Insert new orders
  - `POST /api/v2/communications/messages` - Send notifications

### Marketplace APIs
- Authentication varies by marketplace (Bearer tokens, API keys)
- All responses expected as JSON
- Each Flow class handles marketplace-specific payload structure

## Testing Conventions

- **Unit Tests**: Test individual methods in isolation with mocked dependencies
- **Feature Tests**: Test controller endpoints with real database (RefreshDatabase trait)
- **Factories**: Use factories for test data (`User`, `ProductChannel`, `UserClient`)
- **HTTP Mocking**: Use `Http::fake()` for external API calls
- **Coverage Target**: 85%+ overall, 95%+ for critical order processing paths

## Commission Calculation Logic

Located in `OrderFlowService::porcentagemaCobrar()`:
```php
if ($percentage <= 13) {
    return 15; // Minimum 15% commission
} else {
    return $percentage + 3; // Add 3% margin
}
```

Commission validation in `OrderFlowService::comparaComissoes()` ensures BigHub commission meets minimum requirements.

## Code Style

- Follow Laravel conventions
- Use Laravel Pint for formatting (PSR-12 style)
- Service layer pattern for business logic
- Strategy pattern for marketplace-specific processing
- Repository pattern via Eloquent models

## Important Notes

- This is NOT a git repository (no version control initialized)
- Documentation exists in both Portuguese (`service.srv/doc_pt/`) and English (`service.srv/docs/`)
- The `news/` directory contains project reference files and modification notes
- Always verify which Laravel app (service.srv vs cloud) before running commands

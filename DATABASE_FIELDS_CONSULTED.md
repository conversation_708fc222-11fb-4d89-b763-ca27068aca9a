# Campos Consultados - Sistema de Repasse de Orders

## Conexão: MySQL
- **Host**: **************
- **Database**: `db_accounts`

---

## 1. Ta<PERSON><PERSON>: `tbl_products_channels`

### Campos da Tabela

| Campo | Tipo | Null | Descrição |
|-------|------|------|-----------|
| `id` | BIGINT UNSIGNED | NO | ID único |
| `ean` | VARCHAR(255) | NO | Código EAN do produto |
| `channel` | VARCHAR(255) | NO | Canal de venda (marketplace) |
| `user_id` | BIGINT UNSIGNED | NO | ID do vendedor |
| `is_active` | BOOLEAN | NO | Produto ativo |
| `stock` | INTEGER | YES | Quantidade em estoque |
| `price` | DECIMAL(10,2) | NO | Preço do produto |
| `shipping_price` | DECIMAL(10,2) | YES | Preço de envio |

---

## 2. Tabela: `tbl_user_clients`

### <PERSON><PERSON> da Tabela

| Campo | Tipo | Null | Descrição |
|-------|------|------|-----------|
| `id` | BIGINT UNSIGNED | NO | ID único do vendedor |
| `name` | VARCHAR(255) | NO | Nome do vendedor |
| `email` | VARCHAR(255) | YES | Email do vendedor |
| `delivery_by` | VARCHAR(255) | YES | Método de entrega |
| `user_active` | BOOLEAN | NO | Vendedor ativo |

---

## 3. Tabela: `tbl_user_products`

### Campos da Tabela

| Campo | Tipo | Null | Descrição |
|-------|------|------|-----------|
| `id` | BIGINT UNSIGNED | NO | ID único |
| `user_id` | BIGINT UNSIGNED | NO | ID do vendedor |
| `barCode` | VARCHAR(255) | NO | Código EAN |
| `images` | TEXT | YES | URLs de imagens (JSON) |
| `created_at` | TIMESTAMP | YES | Data de criação |
| `updated_at` | TIMESTAMP | YES | Data de atualização |

---

**Total de Tabelas**: 3
**Total de Campos**: 19

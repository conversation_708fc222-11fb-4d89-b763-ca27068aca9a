# Database Schema - Apolus Project

## Conexões de Banco de Dados

### <PERSON><PERSON><PERSON> Principal (MySQL)
- **Driver**: MySQL
- **Host**: **************
- **Port**: 3306
- **Database**: `db_accounts`
- **Username**: hublord
- **Connection**: mysql (padrão)

### Conex<PERSON><PERSON> Disponíveis (Configuradas)
- **sqlite**: SQLite local (desenvolvimento)
- **mysql**: MySQL/MariaDB
- **mariadb**: MariaDB
- **pgsql**: PostgreSQL
- **sqlsrv**: SQL Server

### Sistemas de Cache/Fila
- **Redis**: Configurado para cache e sessões
  - Host: 127.0.0.1
  - Port: 6379
  - Databases: 0 (default), 1 (cache)

---

## Tabelas da Aplicação Local

### 1. `users`
Tabela de autenticação de usuários do sistema Laravel.

| Coluna | Tipo | Descrição | Constraints |
|--------|------|-----------|-------------|
| `id` | BIGINT UNSIGNED | ID único do usuário | PRIMARY KEY, AUTO_INCREMENT |
| `name` | VARCHAR(255) | Nome do usuário | NOT NULL |
| `email` | VARCHAR(255) | Email do usuário | NOT NULL, UNIQUE |
| `email_verified_at` | TIMESTAMP | Data de verificação do email | NULLABLE |
| `password` | VARCHAR(255) | Senha criptografada | NOT NULL |
| `remember_token` | VARCHAR(100) | Token para "lembrar-me" | NULLABLE |
| `created_at` | TIMESTAMP | Data de criação | NULLABLE |
| `updated_at` | TIMESTAMP | Data de atualização | NULLABLE |

**Modelo**: `App\Models\User`

**Relacionamentos**: Nenhum definido explicitamente

---

### 2. `password_reset_tokens`
Tabela para tokens de reset de senha.

| Coluna | Tipo | Descrição | Constraints |
|--------|------|-----------|-------------|
| `email` | VARCHAR(255) | Email do usuário | PRIMARY KEY |
| `token` | VARCHAR(255) | Token de reset | NOT NULL |
| `created_at` | TIMESTAMP | Data de criação | NULLABLE |

**Modelo**: Nenhum (gerenciado pelo Laravel Auth)

---

### 3. `sessions`
Tabela de sessões do Laravel.

| Coluna | Tipo | Descrição | Constraints |
|--------|------|-----------|-------------|
| `id` | VARCHAR(255) | ID da sessão | PRIMARY KEY |
| `user_id` | BIGINT UNSIGNED | ID do usuário | NULLABLE, INDEX, FK -> users.id |
| `ip_address` | VARCHAR(45) | Endereço IP | NULLABLE |
| `user_agent` | TEXT | User Agent do navegador | NULLABLE |
| `payload` | LONGTEXT | Dados da sessão | NOT NULL |
| `last_activity` | INTEGER | Timestamp da última atividade | NOT NULL, INDEX |

**Modelo**: Nenhum (gerenciado pelo Laravel Session)

---

### 4. `cache`
Tabela de cache do Laravel.

| Coluna | Tipo | Descrição | Constraints |
|--------|------|-----------|-------------|
| `key` | VARCHAR(255) | Chave do cache | PRIMARY KEY |
| `value` | MEDIUMTEXT | Valor cacheado | NOT NULL |
| `expiration` | INTEGER | Timestamp de expiração | NOT NULL |

**Modelo**: Nenhum (gerenciado pelo Laravel Cache)

---

### 5. `cache_locks`
Tabela de locks do cache.

| Coluna | Tipo | Descrição | Constraints |
|--------|------|-----------|-------------|
| `key` | VARCHAR(255) | Chave do lock | PRIMARY KEY |
| `owner` | VARCHAR(255) | Proprietário do lock | NOT NULL |
| `expiration` | INTEGER | Timestamp de expiração | NOT NULL |

**Modelo**: Nenhum (gerenciado pelo Laravel Cache)

---

### 6. `jobs`
Tabela de filas de trabalho (queue).

| Coluna | Tipo | Descrição | Constraints |
|--------|------|-----------|-------------|
| `id` | BIGINT UNSIGNED | ID do job | PRIMARY KEY, AUTO_INCREMENT |
| `queue` | VARCHAR(255) | Nome da fila | NOT NULL, INDEX |
| `payload` | LONGTEXT | Dados do job | NOT NULL |
| `attempts` | TINYINT UNSIGNED | Número de tentativas | NOT NULL |
| `reserved_at` | INTEGER UNSIGNED | Timestamp de reserva | NULLABLE |
| `available_at` | INTEGER UNSIGNED | Timestamp de disponibilidade | NOT NULL |
| `created_at` | INTEGER UNSIGNED | Timestamp de criação | NOT NULL |

**Modelo**: Nenhum (gerenciado pelo Laravel Queue)

---

### 7. `job_batches`
Tabela de lotes de jobs.

| Coluna | Tipo | Descrição | Constraints |
|--------|------|-----------|-------------|
| `id` | VARCHAR(255) | ID do lote | PRIMARY KEY |
| `name` | VARCHAR(255) | Nome do lote | NOT NULL |
| `total_jobs` | INTEGER | Total de jobs | NOT NULL |
| `pending_jobs` | INTEGER | Jobs pendentes | NOT NULL |
| `failed_jobs` | INTEGER | Jobs falhados | NOT NULL |
| `failed_job_ids` | LONGTEXT | IDs de jobs falhados | NOT NULL |
| `options` | MEDIUMTEXT | Opções do lote | NULLABLE |
| `cancelled_at` | INTEGER | Timestamp de cancelamento | NULLABLE |
| `created_at` | INTEGER | Timestamp de criação | NOT NULL |
| `finished_at` | INTEGER | Timestamp de conclusão | NULLABLE |

**Modelo**: Nenhum (gerenciado pelo Laravel Queue)

---

### 8. `failed_jobs`
Tabela de jobs falhados.

| Coluna | Tipo | Descrição | Constraints |
|--------|------|-----------|-------------|
| `id` | BIGINT UNSIGNED | ID do job falhado | PRIMARY KEY, AUTO_INCREMENT |
| `uuid` | VARCHAR(255) | UUID único | UNIQUE |
| `connection` | TEXT | Conexão utilizada | NOT NULL |
| `queue` | TEXT | Fila utilizada | NOT NULL |
| `payload` | LONGTEXT | Dados do job | NOT NULL |
| `exception` | LONGTEXT | Exceção capturada | NOT NULL |
| `failed_at` | TIMESTAMP | Data de falha | NOT NULL, DEFAULT CURRENT_TIMESTAMP |

**Modelo**: Nenhum (gerenciado pelo Laravel Queue)

---

### 9. `links`
Tabela de links/tokens para landing pages.

| Coluna | Tipo | Descrição | Constraints |
|--------|------|-----------|-------------|
| `id` | BIGINT UNSIGNED | ID do link | PRIMARY KEY, AUTO_INCREMENT |
| `token` | VARCHAR(255) | Token único | NULLABLE |
| `user_token` | VARCHAR(255) | Token do usuário | NULLABLE |
| `token_link` | VARCHAR(255) | Token do link | NULLABLE |
| `created_at` | TIMESTAMP | Data de criação | NULLABLE |
| `updated_at` | TIMESTAMP | Data de atualização | NULLABLE |

**Modelo**: `App\Models\Link`

**Usado em**: `LinkController::checkToken()` - validação de tokens para landing pages

**Relacionamentos**:
- JOIN com `users` via `user_token`
- JOIN com `agent_landing_page` via `user_token`

---

## Tabelas Remotas (via BigHub API)

As seguintes tabelas existem no banco de dados remoto do BigHub e são acessadas via API REST:

### 10. `tbl_products_channels`
Tabela de relacionamento entre produtos (EAN), canais de venda e vendedores.

| Coluna | Tipo | Descrição | Constraints |
|--------|------|-----------|-------------|
| `id` | BIGINT UNSIGNED | ID do registro | PRIMARY KEY, AUTO_INCREMENT |
| `ean` | VARCHAR(255) | Código EAN do produto | NOT NULL |
| `channel` | VARCHAR(255) | Canal de venda (marketplace) | NOT NULL |
| `user_id` | BIGINT UNSIGNED | ID do vendedor | NOT NULL |
| `is_active` | BOOLEAN | Se o produto está ativo | NOT NULL |
| `stock` | INTEGER | Quantidade em estoque | NULLABLE |
| `price` | DECIMAL(10,2) | Preço do produto | NOT NULL |
| `shipping_price` | DECIMAL(10,2) | Preço de envio | NULLABLE |

**Modelo**: `App\Models\ProductChannel`

**Uso**:
- Matching de produtos com vendedores em `OrderController::getSeller()`
- Validação de EAN, canal e preço para atribuição de pedidos

**Queries Comuns**:
```php
ProductChannel::where('ean', $ean)
    ->where('channel', $channel)
    ->where('is_active', true)
    ->get();
```

---

### 11. `tbl_user_clients`
Tabela de clientes/vendedores cadastrados.

| Coluna | Tipo | Descrição | Constraints |
|--------|------|-----------|-------------|
| `id` | BIGINT UNSIGNED | ID do cliente | PRIMARY KEY, AUTO_INCREMENT |
| `name` | VARCHAR(255) | Nome do cliente/vendedor | NOT NULL |
| `email` | VARCHAR(255) | Email do cliente | NULLABLE |
| `delivery_by` | VARCHAR(255) | Método de entrega | NULLABLE |
| `user_active` | BOOLEAN | Se o usuário está ativo | NOT NULL |

**Modelo**: `App\Models\UserClient`

**Uso**:
- Identificação de vendedores ativos
- Envio de notificações de pedidos atribuídos

**Queries Comuns**:
```php
UserClient::where('id', $user_id)
    ->where('user_active', true)
    ->first();
```

---

### 12. `tbl_user_products`
Tabela de produtos associados a usuários/vendedores.

| Coluna | Tipo | Descrição | Constraints |
|--------|------|-----------|-------------|
| `id` | BIGINT UNSIGNED | ID do registro | PRIMARY KEY, AUTO_INCREMENT |
| `user_id` | BIGINT UNSIGNED | ID do vendedor | NOT NULL |
| `barCode` | VARCHAR(255) | Código de barras (EAN) | NOT NULL |
| `images` | TEXT | URLs de imagens do produto (JSON) | NULLABLE |
| `created_at` | TIMESTAMP | Data de criação | NULLABLE |
| `updated_at` | TIMESTAMP | Data de atualização | NULLABLE |

**Modelo**: `App\Models\UserProduct`

**Uso**:
- Busca de imagens de produtos via `FlowBighub::getImgProduct()`
- Associação de produtos com vendedores

**Queries Comuns**:
```php
UserProduct::where('barCode', $ean)
    ->where('user_id', $user_id)
    ->first();
```

---

### 13. `flow_orders`
Tabela de rastreamento de pedidos processados pelo Apolus.

| Coluna | Tipo | Descrição | Constraints |
|--------|------|-----------|-------------|
| `id` | BIGINT UNSIGNED | ID do registro | PRIMARY KEY, AUTO_INCREMENT |
| `order_id` | VARCHAR(255) | ID do pedido no marketplace | NOT NULL |
| `marketplace` | VARCHAR(255) | Nome do marketplace | NOT NULL |
| `channel` | VARCHAR(255) | Canal de venda | NOT NULL |
| `user_id` | BIGINT UNSIGNED | ID do vendedor atribuído | NULLABLE |
| `status` | VARCHAR(255) | Status do pedido | NOT NULL |
| `response` | LONGTEXT | Resposta completa da API (JSON) | NULLABLE |
| `response_first` | LONGTEXT | Primeira resposta do pedido (JSON) | NULLABLE |
| `created_at` | TIMESTAMP | Data de criação | NULLABLE |
| `updated_at` | TIMESTAMP | Data de atualização | NULLABLE |

**Modelo**: `App\Models\FlowOrders`

**Uso**:
- Rastreamento de pedidos processados
- Armazenamento de payloads de marketplaces
- Histórico de atribuições

---

### 14. `agent_landing_page`
Tabela de configuração de landing pages para agentes.

| Coluna | Tipo | Descrição | Constraints |
|--------|------|-----------|-------------|
| `id` | BIGINT UNSIGNED | ID do registro | PRIMARY KEY, AUTO_INCREMENT |
| `agent_token` | VARCHAR(255) | Token do agente | NOT NULL |
| `properties` | TEXT | Propriedades em JSON | NULLABLE |
| `created_at` | TIMESTAMP | Data de criação | NULLABLE |
| `updated_at` | TIMESTAMP | Data de atualização | NULLABLE |

**Modelo**: Nenhum (acessado via query raw)

**Uso**:
- Validação de tokens em `LinkController::checkToken()`
- JOIN com `links` e `users`

---

### 15. `crawler_properties`
Tabela de propriedades coletadas por crawler.

| Coluna | Tipo | Descrição | Constraints |
|--------|------|-----------|-------------|
| `id` | BIGINT UNSIGNED | ID da propriedade | PRIMARY KEY, AUTO_INCREMENT |
| `token` | VARCHAR(255) | Token da propriedade | NOT NULL |
| `data` | LONGTEXT | Dados coletados (JSON) | NULLABLE |
| `created_at` | TIMESTAMP | Data de criação | NULLABLE |
| `updated_at` | TIMESTAMP | Data de atualização | NULLABLE |

**Modelo**: Nenhum (acessado via query raw)

**Uso**:
- Busca de propriedades em `LinkController::checkToken()`

---

## APIs Externas (BigHub)

### Endpoints Principais

#### 1. GET `/api/orders/user/{id}`
- **Descrição**: Busca pedidos não atribuídos de um usuário
- **Autenticação**: Bearer Token
- **Uso**: `FlowBighub::curl_get_orders_no_user()`

#### 2. PUT `/api/orders/{id}/update-user/{user_id}`
- **Descrição**: Atribui um pedido a um vendedor
- **Autenticação**: Bearer Token
- **Uso**: `FlowBighub::curl_put_update_order_user()`

#### 3. GET `/api/orders/table/{tableId}`
- **Descrição**: Busca pedido pelo ID da tabela
- **Autenticação**: Bearer Token
- **Uso**: `FlowBighub::getOrdersByTableId()`

#### 4. POST `/api/v2/sales/orders`
- **Descrição**: Insere novo pedido no sistema BigHub
- **Autenticação**: x-api-key header
- **Uso**: `FlowBighub::insert_tbl_order()`

#### 5. POST `/api/v2/communications/messages`
- **Descrição**: Envia mensagens/notificações
- **Autenticação**: x-api-key header
- **Uso**: Notificações de pedidos atribuídos

---

## Tabela de Migrações

### `migrations`
Tabela padrão do Laravel para controle de migrações.

| Coluna | Tipo | Descrição | Constraints |
|--------|------|-----------|-------------|
| `id` | INTEGER UNSIGNED | ID da migração | PRIMARY KEY, AUTO_INCREMENT |
| `migration` | VARCHAR(255) | Nome do arquivo de migração | NOT NULL |
| `batch` | INTEGER | Número do batch | NOT NULL |

---

## Relacionamentos Entre Tabelas

### Local
- `sessions.user_id` → `users.id`
- `links.user_token` → `users.token` (via JOIN)

### Remotas (via API/Models)
- `tbl_products_channels.user_id` → `tbl_user_clients.id`
- `tbl_user_products.user_id` → `tbl_user_clients.id`
- `tbl_user_products.barCode` → `tbl_products_channels.ean`
- `flow_orders.user_id` → `tbl_user_clients.id`

---

## Canais de Marketplace Suportados

### Marketplaces Mirakl (30+ plataformas)
- pixmania, shopapotheke, planetahuerto, rueducommerce, elcorteingles
- phonehouse, leroymerlin, macway, tiendanimal, eprice, bulevip
- empik, clubefashion, carrefour, leclerc, quirumedes, pccomponentes
- conrad, bigbang, ventunique, worten, conforama, ubaldi, mediamarkt
- bricodepot, zooplus, truffaut, alltricks, castorama, conforamaiberia
- culturafr, tdmp, alternate, bestmarine

### Marketplaces Especiais (processamento customizado)
- fnac, cdiscount, docmorris, manomano, stockly, mktbighub

### Marketplaces Em Migração
- castorama, xxxlgroup, axelspringerde, iberia, wook
- miravia, aliexpress, makro, kaufland

---

## Índices e Performance

### Índices Recomendados (Remotas)
```sql
-- tbl_products_channels
CREATE INDEX idx_ean_channel ON tbl_products_channels(ean, channel);
CREATE INDEX idx_user_active ON tbl_products_channels(user_id, is_active);

-- tbl_user_products
CREATE INDEX idx_barcode_user ON tbl_user_products(barCode, user_id);

-- flow_orders
CREATE INDEX idx_order_marketplace ON flow_orders(order_id, marketplace);
CREATE INDEX idx_status ON flow_orders(status);
```

### Índices Existentes (Local)
- `users.email` (UNIQUE)
- `sessions.user_id` (INDEX)
- `sessions.last_activity` (INDEX)
- `jobs.queue` (INDEX)
- `failed_jobs.uuid` (UNIQUE)

---

## Notas Importantes

1. **Conexão Principal**: O sistema usa MySQL remoto (`db_accounts`) como conexão padrão
2. **Sem Migrations Customizadas**: Apenas migrations padrão do Laravel foram criadas localmente
3. **Tabelas Remotas**: Maioria das tabelas de negócio (`tbl_*`) estão no banco remoto do BigHub
4. **Acesso Via API**: Operações de insert/update em tabelas remotas são feitas via API REST
5. **Models Sem Timestamps**: `ProductChannel` e `UserClient` têm `$timestamps = false`
6. **Queue Database**: Sistema configurado para usar `database` como driver de fila
7. **Cache**: Sistema usa arquivo para cache, mas está configurado para usar Redis se necessário
8. **FTP Integration**: Sistema integra com FTP da Stockly (bigsales.pt) para tracking

---

## Ambiente de Desenvolvimento

### Banco de Dados Local (SQLite)
- Pode ser usado para desenvolvimento com: `DB_CONNECTION=sqlite`
- Arquivo: `database/database.sqlite`

### Banco de Dados Produção (MySQL)
- Host: **************
- Database: db_accounts
- Acesso via credenciais em `.env`

---

## Segurança

### Credenciais Sensíveis
⚠️ **ATENÇÃO**: As seguintes credenciais estão hardcoded no código:

1. **BigHub API**:
   - API Key: `7e0913c6-015d-4c60-9c49-3e4837922e53`
   - Bearer Token: `16|Mou2rwjjJMLc3BGRO4KaMgMlu3xDCvJ1ciGNOgsw78602f04`
   - Localização: `FlowBighub.php`

2. **FTP Stockly**:
   - Host: ftp.bigsales.pt
   - User: u931739318.stockly
   - Password: `&RNBd7rV7kK#/Yk8`
   - Localização: `.env`

### Recomendações
- Mover credenciais hardcoded para variáveis de ambiente
- Implementar rotação de tokens
- Usar Laravel Secrets para produção
- Implementar rate limiting nas APIs

---

**Gerado em**: 2025-10-21
**Versão do Laravel**: 11.x
**Projeto**: Apolus - Order Processing & Marketplace Integration

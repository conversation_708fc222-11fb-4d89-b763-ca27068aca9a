# Database Schema - Order Processing (Repasse de Orders)

## Con<PERSON><PERSON> Principal
- **Driver**: MySQL
- **Host**: **************
- **Database**: `db_accounts`

---

## Tabelas do Sistema de Repasse de Orders

### 1. `tbl_products_channels`
Tabela de matching entre produtos (EAN), canais de venda e vendedores.

| Coluna | Tipo | Descrição | Constraints |
|--------|------|-----------|-------------|
| `id` | BIGINT UNSIGNED | ID do registro | PRIMARY KEY, AUTO_INCREMENT |
| `ean` | VARCHAR(255) | Código EAN do produto | NOT NULL |
| `channel` | VARCHAR(255) | Canal de venda (marketplace) | NOT NULL |
| `user_id` | BIGINT UNSIGNED | ID do vendedor | NOT NULL |
| `is_active` | BOOLEAN | Se o produto está ativo | NOT NULL |
| `stock` | INTEGER | Quantidade em estoque | NULLABLE |
| `price` | DECIMAL(10,2) | Preço do produto | NOT NULL |
| `shipping_price` | DECIMAL(10,2) | Preço de envio | NULLABLE |

**Modelo**: `App\Models\ProductChannel`

**Uso**:
- **Seller Matching**: Encontrar vendedor com EAN + channel + price
- **Validação de Preço**: Comparar preço do pedido com preço cadastrado
- **Validação de Estoque**: Verificar disponibilidade
- **Verificação de Status**: Apenas vendedores ativos (`is_active = true`)

**Queries Comuns**:
```php
// Buscar sellers com matching de EAN e channel
ProductChannel::where('ean', $ean)
    ->where('channel', $channel)
    ->where('is_active', true)
    ->get();

// Buscar com filtro de preço
ProductChannel::where('ean', $ean)
    ->where('channel', $channel)
    ->where('is_active', true)
    ->where('price', '<=', $order_price * 1.05) // 5% tolerance
    ->get();
```

**Índices Recomendados**:
```sql
CREATE INDEX idx_ean_channel ON tbl_products_channels(ean, channel);
CREATE INDEX idx_user_active ON tbl_products_channels(user_id, is_active);
CREATE INDEX idx_ean_channel_active ON tbl_products_channels(ean, channel, is_active);
```

**Referência no Código**: `OrderController::getSeller()` - linhas 728-876

---

### 2. `tbl_user_clients`
Tabela de vendedores (clientes) cadastrados no sistema.

| Coluna | Tipo | Descrição | Constraints |
|--------|------|-----------|-------------|
| `id` | BIGINT UNSIGNED | ID do cliente | PRIMARY KEY, AUTO_INCREMENT |
| `name` | VARCHAR(255) | Nome do cliente/vendedor | NOT NULL |
| `email` | VARCHAR(255) | Email do cliente | NULLABLE |
| `delivery_by` | VARCHAR(255) | Método de entrega | NULLABLE |
| `user_active` | BOOLEAN | Se o usuário está ativo | NOT NULL |

**Modelo**: `App\Models\UserClient`

**Uso**:
- **Validação de Vendedor**: Verificar se vendedor está ativo
- **Notificações**: Enviar emails de pedidos atribuídos
- **Informações de Entrega**: Método de entrega preferido

**Queries Comuns**:
```php
// Verificar se vendedor está ativo
UserClient::where('id', $user_id)
    ->where('user_active', true)
    ->first();
```

**Índices Recomendados**:
```sql
CREATE INDEX idx_user_active ON tbl_user_clients(user_active);
CREATE INDEX idx_email ON tbl_user_clients(email);
```

---

### 3. `tbl_user_products`
Tabela de produtos e imagens associados aos vendedores.

| Coluna | Tipo | Descrição | Constraints |
|--------|------|-----------|-------------|
| `id` | BIGINT UNSIGNED | ID do registro | PRIMARY KEY, AUTO_INCREMENT |
| `user_id` | BIGINT UNSIGNED | ID do vendedor | NOT NULL |
| `barCode` | VARCHAR(255) | Código de barras (EAN) | NOT NULL |
| `images` | TEXT | URLs de imagens do produto (JSON) | NULLABLE |
| `created_at` | TIMESTAMP | Data de criação | NULLABLE |
| `updated_at` | TIMESTAMP | Data de atualização | NULLABLE |

**Modelo**: `App\Models\UserProduct`

**Uso**:
- **Imagens de Produtos**: Buscar imagens para incluir no payload do pedido
- **Associação Produto-Vendedor**: Validar que vendedor possui o produto

**Queries Comuns**:
```php
// Buscar imagem do produto por EAN e vendedor
UserProduct::where('barCode', $ean)
    ->where('user_id', $user_id)
    ->first();
```

**Referência no Código**: `FlowBighub::getImgProduct()` - linha 142

**Índices Recomendados**:
```sql
CREATE INDEX idx_barcode_user ON tbl_user_products(barCode, user_id);
CREATE INDEX idx_user_id ON tbl_user_products(user_id);
```

---

## Relacionamentos Entre Tabelas

```
tbl_products_channels
    ├── user_id → tbl_user_clients.id (vendedor do produto)
    └── ean → tbl_user_products.barCode

tbl_user_products
    └── user_id → tbl_user_clients.id
```

---

## Fluxo de Repasse de Orders

### 1. Ingestão do Pedido
```
Marketplace Webhook → OrderController::processOrders()
    - Recebe payload do marketplace
    - Identifica marketplace e país
```

### 2. Extração de EAN
```
OrderFlowService::extractEanCode($sku)
    - Remove prefixos: BIGHUB-, *, etc
    - Extrai código EAN limpo do SKU
    - Retorna: string EAN (ex: "1234567890123")
```

### 3. Seller Matching
```
OrderController::getSeller($ean, $channel, $price, $order_id, $tbl_id)
    ↓
Query: tbl_products_channels
    WHERE ean = $ean
    AND channel = $channel
    AND is_active = true
    ↓
Validações de Preço:
    1. Preço exato match: price == order_price
    2. Preço menor/igual: price <= order_price
    3. Tolerância 5%: price <= order_price × 1.05
    ↓
Retorna: user_id (seller ID) ou null
```

**Casos Especiais no Matching**:
```php
// Produtos "relógio" → sempre user_id 843
if (stripos($product_title, 'relógio') !== false) {
    return 843;
}

// Sem match → suporte (user_id 25)
if (!$seller_found) {
    return 25;
}
```

### 4. Validação de Comissão
```
OrderFlowService::porcentagemaCobrar($total, $commission)
    ↓
Cálculo:
    - Se commission <= 13% → retorna 15%
    - Se commission > 13% → retorna commission + 3%
    ↓
OrderFlowService::comparaComissoes($bighub_commission, $marketplace_commission)
    ↓
Valida: bighub_commission >= marketplace_commission
```

### 5. Geração de Payload
```
FlowBighub::generatePayloadMirakls($channel, $order, $user_id, $country_mkt)
    ↓
Para cada item do pedido:
    1. Extrai EAN do SKU
    2. Busca imagem: tbl_user_products
       WHERE barCode = $ean AND user_id = $user_id
    3. Calcula comissão
    4. Monta item do payload
    ↓
Retorna: array $payload completo
```

### 6. Atribuição do Pedido (API BigHub)
```
FlowBighub::curl_put_update_order_user($order_id, $user_id)
    ↓
API: PUT /api/orders/{order_id}/update-user/{user_id}
Auth: Bearer Token
    ↓
Atualiza pedido no BigHub com vendedor atribuído
```

### 7. Inserção no BigHub
```
FlowBighub::insert_tbl_order($payload)
    ↓
API: POST /api/v2/sales/orders
Auth: x-api-key header
Body: $payload (JSON)
    ↓
Cria registro do pedido no sistema BigHub
```

### 8. Notificação ao Vendedor
```
API: POST /api/v2/communications/messages
    ↓
Envia email para: tbl_user_clients.email
Assunto: Novo pedido atribuído
Conteúdo: Detalhes do pedido e marketplace
```

---

## Regras de Negócio

### Regra 1: Watch Products (Relógios)
```php
// OrderController linha ~200
if (stripos($item->product_title, 'relógio') !== false) {
    $user_id = 843; // Vendedor fixo ID 843
}
```
**Todos os produtos** com "relógio" no título são automaticamente atribuídos ao vendedor ID 843.

---

### Regra 2: Multi-Product Orders
```php
// Para pedidos com múltiplos produtos
foreach ($order_items as $item) {
    $seller = getSeller($item->ean, $channel, $item->price);

    if ($seller != $first_seller) {
        // Todos produtos devem ter o MESMO vendedor
        return 25; // Suporte
    }
}
```
**Todos os produtos** do pedido devem ter match com o **mesmo vendedor**. Caso contrário, pedido vai para suporte.

---

### Regra 3: Price Matching Priority
```php
// OrderController::getSeller()
// Prioridade na seleção de vendedor:

1. Exact Match:
   WHERE price = $order_price

2. Lower or Equal:
   WHERE price <= $order_price
   ORDER BY price DESC

3. 5% Tolerance:
   WHERE price <= ($order_price * 1.05)
   ORDER BY price ASC

4. No Match:
   return 25; // Suporte
```

---

### Regra 4: Commission Validation
```php
// OrderFlowService::comparaComissoes()
$bighub_commission >= $marketplace_commission
```
A comissão cobrada pelo BigHub **deve ser sempre maior ou igual** à comissão do marketplace.

**Fórmula de Comissão BigHub**:
- Se marketplace commission ≤ 13% → cobra **15%**
- Se marketplace commission > 13% → cobra **marketplace + 3%**

---

### Regra 5: Active Products Only
```php
ProductChannel::where('is_active', true)
```
Apenas produtos com `is_active = true` são considerados no matching.

---

## Marketplaces Suportados

### Mirakl (30+ plataformas)
Processamento padronizado via `processMarketplaceMirakls()`:

**Lista Completa**:
- pixmania, shopapotheke, planetahuerto, rueducommerce, elcorteingles
- phonehouse, leroymerlin, macway, tiendanimal, eprice, bulevip
- empik, clubefashion, carrefour, leclerc, quirumedes, pccomponentes
- conrad, bigbang, ventunique, worten, conforama, ubaldi, mediamarkt
- bricodepot, zooplus, truffaut, alltricks, castorama, conforamaiberia
- culturafr, tdmp, alternate, bestmarine

**Payload**: `FlowBighub::generatePayloadMirakls()`

---

### Marketplaces Especiais
Processamento customizado com classes dedicadas:

| Marketplace | Classe | Método Principal |
|-------------|--------|------------------|
| **fnac** | `FlowFnac` | `generatePayload()` |
| **cdiscount** | `FlowCdiscount` | `generatePayload()` |
| **docmorris** | `FlowDocMorris` | `generatePayload()` |
| **manomano** | `FlowManoMano` | `generatePayload()` |
| **stockly** | `FlowStockly` | `generatePayload()` + FTP tracking |

**Referência**: `OrderController` - linhas 31-50

---

## APIs Externas (BigHub)

### Base URLs
- **Connect API**: `https://connect-big.bighub.store`
- **App API**: `https://app.bighub.store`

### Autenticação
- **Bearer Token**: `16|Mou2rwjjJMLc3BGRO4KaMgMlu3xDCvJ1ciGNOgsw78602f04`
- **API Key**: `7e0913c6-015d-4c60-9c49-3e4837922e53`

---

### Endpoints Utilizados

#### 1. GET `/api/orders/user/{id}`
```php
FlowBighub::curl_get_orders_no_user($id)
```
- **Descrição**: Busca pedidos não atribuídos de um usuário
- **Auth**: Bearer Token
- **Response**: Array de pedidos pendentes

---

#### 2. PUT `/api/orders/{id}/update-user/{user_id}`
```php
FlowBighub::curl_put_update_order_user($order_id, $user_id)
```
- **Descrição**: Atribui um pedido a um vendedor específico
- **Auth**: Bearer Token
- **Params**:
  - `order_id`: ID do pedido
  - `user_id`: ID do vendedor
- **Response**: Confirmação da atribuição

---

#### 3. GET `/api/orders/table/{tableId}`
```php
FlowBighub::getOrdersByTableId($tableId)
```
- **Descrição**: Busca pedido pelo ID da tabela interna
- **Auth**: Bearer Token
- **Response**: Objeto com dados do pedido

---

#### 4. POST `/api/v2/sales/orders`
```php
FlowBighub::insert_tbl_order($payload)
```
- **Descrição**: Insere novo pedido no sistema BigHub
- **Auth**: x-api-key header
- **Body**: Payload completo do pedido (JSON)
- **Response**: ID do pedido criado

---

#### 5. POST `/api/v2/communications/messages`
- **Descrição**: Envia notificações por email aos vendedores
- **Auth**: x-api-key header
- **Body**:
  ```json
  {
    "to": "<EMAIL>",
    "subject": "Novo Pedido Atribuído",
    "message": "Detalhes do pedido..."
  }
  ```

---

## Pontos Críticos de Performance

### 1. Query de Matching (Crítica)
```sql
-- Executada para CADA produto de CADA pedido
SELECT * FROM tbl_products_channels
WHERE ean = ?
  AND channel = ?
  AND is_active = 1
ORDER BY
  CASE WHEN price = ? THEN 1 ELSE 2 END,
  price ASC
LIMIT 1;
```

**Otimizações Recomendadas**:
```sql
-- Índice composto ideal
CREATE INDEX idx_matching_optimized
ON tbl_products_channels(ean, channel, is_active, price);

-- Considerar particionamento por channel
ALTER TABLE tbl_products_channels
PARTITION BY LIST(channel) (...);
```

---

### 2. API Calls por Pedido
```
Pedido Single-Product:
  1x GET /api/orders/table/{id}      (buscar dados)
  1x POST /api/v2/sales/orders       (inserir pedido)
  1x PUT /api/orders/{id}/update-user (atribuir vendedor)
  1x POST /api/v2/communications     (notificar)
  = 4 API calls

Pedido Multi-Product (N produtos):
  1x GET /api/orders/table/{id}
  1x POST /api/v2/sales/orders
  1x PUT /api/orders/{id}/update-user
  N x Query tbl_user_products (buscar imagens)
  1x POST /api/v2/communications
  = 4 + N API calls/queries
```

**Otimizações Recomendadas**:
- Implementar **queue system** (Laravel Queue) para processar assincronamente
- **Cache** de product images por EAN
- **Batch processing** de múltiplos pedidos
- **Retry logic** com exponential backoff para API failures

---

### 3. Busca de Imagens
```php
// FlowBighub::getImgProduct() - linha 142
// Executado para cada item do pedido
UserProduct::where('barCode', $ean)
    ->where('user_id', $user_id)
    ->first();
```

**Otimizações**:
```sql
-- Índice para acelerar busca
CREATE INDEX idx_barcode_user
ON tbl_user_products(barCode, user_id);

-- Cache Redis
Cache::remember("product_image_{$ean}_{$user_id}", 3600, function() {
    return UserProduct::where('barCode', $ean)
        ->where('user_id', $user_id)
        ->first();
});
```

---

## Monitoramento e Queries Úteis

### 1. Taxa de Match por Marketplace
```sql
SELECT
    channel,
    COUNT(*) as total_requests,
    SUM(CASE WHEN user_id IS NOT NULL AND user_id != 25 THEN 1 ELSE 0 END) as matched,
    SUM(CASE WHEN user_id = 25 THEN 1 ELSE 0 END) as to_support,
    ROUND(
        SUM(CASE WHEN user_id IS NOT NULL AND user_id != 25 THEN 1 ELSE 0 END) / COUNT(*) * 100,
        2
    ) as match_rate_percent
FROM tbl_products_channels
GROUP BY channel
ORDER BY total_requests DESC;
```

---

### 2. Top Vendedores por Volume
```sql
SELECT
    uc.id,
    uc.name,
    uc.email,
    COUNT(pc.id) as total_products,
    SUM(CASE WHEN pc.is_active = 1 THEN 1 ELSE 0 END) as active_products
FROM tbl_user_clients uc
LEFT JOIN tbl_products_channels pc ON uc.id = pc.user_id
WHERE uc.user_active = 1
GROUP BY uc.id, uc.name, uc.email
ORDER BY active_products DESC
LIMIT 20;
```

---

### 3. Produtos Sem Imagens
```sql
SELECT
    pc.ean,
    pc.channel,
    uc.name as seller_name,
    pc.price
FROM tbl_products_channels pc
JOIN tbl_user_clients uc ON pc.user_id = uc.id
LEFT JOIN tbl_user_products up ON pc.ean = up.barCode AND pc.user_id = up.user_id
WHERE pc.is_active = 1
  AND up.id IS NULL
ORDER BY pc.channel, uc.name;
```

---

### 4. Produtos com Múltiplos Vendedores (Competição)
```sql
SELECT
    ean,
    channel,
    COUNT(*) as num_sellers,
    MIN(price) as min_price,
    MAX(price) as max_price,
    AVG(price) as avg_price
FROM tbl_products_channels
WHERE is_active = 1
GROUP BY ean, channel
HAVING COUNT(*) > 1
ORDER BY num_sellers DESC, channel;
```

---

### 5. Vendedores Inativos com Produtos
```sql
SELECT
    uc.id,
    uc.name,
    uc.email,
    COUNT(pc.id) as total_products
FROM tbl_user_clients uc
JOIN tbl_products_channels pc ON uc.id = pc.user_id
WHERE uc.user_active = 0
GROUP BY uc.id, uc.name, uc.email
ORDER BY total_products DESC;
```

---

### 6. Análise de Preços Fora da Tolerância
```sql
-- Produtos que frequentemente caem na regra de 5% tolerance
-- (indica possível desatualização de preços)
SELECT
    ean,
    channel,
    user_id,
    price,
    -- Simulação: assumindo price típico do marketplace
    ROUND(price / 1.05, 2) as original_marketplace_price
FROM tbl_products_channels
WHERE is_active = 1
  AND price > (SELECT AVG(price) * 1.05 FROM tbl_products_channels WHERE is_active = 1)
ORDER BY price DESC;
```

---

## Casos de Erro Conhecidos

### 1. EAN Não Encontrado
```
Cenário: Produto não cadastrado em tbl_products_channels
Ação: Pedido vai para suporte (user_id = 25)
Log: "No seller found for EAN {ean} on channel {channel}"
```

### 2. Preço Fora da Tolerância
```
Cenário: price > order_price * 1.05
Ação: Pedido vai para suporte (user_id = 25)
Log: "Price mismatch for EAN {ean}: expected {order_price}, found {price}"
```

### 3. Multi-Produto com Sellers Diferentes
```
Cenário: Produto A → Seller 100, Produto B → Seller 200
Ação: Pedido completo vai para suporte (user_id = 25)
Log: "Multi-product order requires same seller"
```

### 4. Vendedor Inativo
```
Cenário: is_active = false ou user_active = false
Ação: Seller ignorado no matching, busca próximo ou vai para suporte
Log: "Seller {user_id} is inactive"
```

### 5. Produto Sem Imagem
```
Cenário: Não existe em tbl_user_products
Ação: Payload criado sem imagem (image = null)
Log: "No image found for EAN {ean}, user {user_id}"
```

---

## Integração FTP (Stockly)

### Configuração
```env
FTP_HOST=ftp.bigsales.pt
FTP_PORT=21
FTP_USER=u931739318.stockly
FTP_PASSWORD=&RNBd7rV7kK#/Yk8
```

### Uso
- **Controller**: `FtpTrackingController`
- **Marketplace**: Stockly
- **Função**: Upload de arquivos de tracking de pedidos

---

## Estrutura de Dados: Payload Exemplo

### Payload Mirakl (generatePayloadMirakls)
```json
{
  "order_id": "MKP-12345",
  "marketplace": "fnac",
  "channel": "fnac_fr",
  "country": "FR",
  "user_id": 150,
  "customer": {
    "name": "Cliente Nome",
    "email": "<EMAIL>",
    "address": "..."
  },
  "items": [
    {
      "ean": "1234567890123",
      "sku": "BIGHUB-1234567890123",
      "title": "Produto Teste",
      "quantity": 1,
      "price": 29.99,
      "commission": 4.50,
      "bighub_commission": 4.50,
      "image": "https://cdn.bighub.store/images/123.jpg"
    }
  ],
  "shipping": {
    "method": "Standard",
    "price": 5.99,
    "address": "..."
  },
  "totals": {
    "subtotal": 29.99,
    "shipping": 5.99,
    "total": 35.98,
    "commission": 4.50
  }
}
```

---

**Gerado em**: 2025-10-21
**Foco**: Sistema de Repasse de Orders
**Tabelas**: 3 principais (`tbl_products_channels`, `tbl_user_clients`, `tbl_user_products`)
**Projeto**: Apolus - Order Processing & Marketplace Integration

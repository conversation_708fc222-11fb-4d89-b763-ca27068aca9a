# 🚀 Deploy Simples - FTP Tracking API

## ✅ Solução Simplificada

Criamos um arquivo PHP direto na pasta `public/` que **NÃO precisa de rotas do Laravel**, evitando problemas de cache e permissões!

---

## 📤 Arquivo para Enviar

### **APENAS 1 ARQUIVO!**

**Arquivo:** `public/ftp_tracking.php`

**Localização Local:**
```
/home/<USER>/Projects/apolus/service.srv/laravel/app/public/ftp_tracking.php
```

**Destino na Nuvem:**
```
/caminho/do/projeto/service.srv/laravel/app/public/ftp_tracking.php
```

---

## 🔧 Configuração do .env

Adicione no arquivo `.env` da nuvem:

```bash
# FTP Configuration
FTP_HOST=ftp.bigsales.pt
FTP_PORT=21
FTP_USER=u931739318.stockly
FTP_PASSWORD="&RNBd7rV7kK#/Yk8"
```

---

## ✅ Pronto!

**NÃO PRECISA:**
- ❌ Limpar cache de rotas
- ❌ Executar `php artisan route:cache`
- ❌ Reiniciar PHP-FPM ou Nginx
- ❌ Enviar múltiplos arquivos

**Basta enviar o arquivo e funciona imediatamente!** 🎉

---

## 🧪 Teste Imediato

```bash
curl -X POST https://apolus.bighub.store/ftp_tracking.php \
  -H "Content-Type: application/json" \
  -d '{
    "order": "01",
    "tracking_nr": "TEST123",
    "carrier": "DHL",
    "carrier_url": "https://www.dhl.com/pt-pt/home/<USER>/tracking-express.html?submit=1&tracking-id=TEST123",
    "date": "17/02/2025"
  }'
```

**Resposta Esperada:**
```json
{
  "success": true,
  "message": "Tracking information uploaded successfully",
  "data": {
    "order": "01",
    "tracking_nr": "TEST123",
    "carrier": "DHL",
    "carrier_url": "https://www.dhl.com/pt-pt/home/<USER>/tracking-express.html?submit=1&tracking-id=TEST123",
    "date": "17/02/2025",
    "filename": "tracking-01-17022025.csv"
  }
}
```

---

## 📋 URL Final

```
POST https://apolus.bighub.store/ftp_tracking.php
```

**Vantagens desta solução:**
- ✅ Funciona imediatamente após upload
- ✅ Não precisa de cache de rotas
- ✅ Não precisa de permissões especiais
- ✅ Mesma estrutura do `forward.php` que já funciona
- ✅ Usa o autoload do Laravel (acessa .env e Log)

---

## 🔍 Debug

Se não funcionar, verifique:

### 1. Arquivo foi enviado?
```bash
ls -la public/ftp_tracking.php
```

### 2. Permissões corretas?
```bash
chmod 644 public/ftp_tracking.php
```

### 3. Ver logs
```bash
tail -f storage/logs/laravel.log
```

---

## 📝 Payload Completo

```json
{
  "order": "01",
  "tracking_nr": "TK123456789",
  "carrier": "DHL",
  "carrier_url": "https://www.dhl.com/pt-pt/home/<USER>/tracking-express.html?submit=1&tracking-id=TK123456789",
  "date": "17/02/2025"
}
```

**Campos:**
- `order` (obrigatório) - Número do pedido
- `tracking_nr` (obrigatório) - Código de rastreio
- `carrier` (obrigatório) - Transportadora
- `carrier_url` (obrigatório) - URL completa de tracking
- `date` (opcional) - Data no formato DD/MM/YYYY (se não enviada, usa data atual)

**Arquivo CSV gerado:** `tracking-01-17022025.csv`

**Local no FTP:** `/orders/tracking/tracking-01-17022025.csv`

---

## ✨ Resumo

1. ✅ Enviar arquivo `public/ftp_tracking.php`
2. ✅ Adicionar credenciais FTP no `.env`
3. ✅ Testar com curl
4. ✅ Pronto! 🎉

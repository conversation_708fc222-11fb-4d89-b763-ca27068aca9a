# Developer Migration Guide - MySQL to PostgreSQL Catalog Schema

## Overview

This guide explains the database migration from MySQL to PostgreSQL with a complete catalog schema redesign. Use this as your reference when updating application code.

## Migration Summary

- **Source**: MySQL database `db_accounts`
- **Target**: PostgreSQL database `bighub`
- **Scope**: Full database migration with catalog normalization and internationalization

---

## Tables Status

### ✅ Tables Kept Without Changes

These tables were migrated as-is from MySQL to PostgreSQL:

```
feature_flags
migrations
order_status_timeline
tbl_batch_files
tbl_batch_fns
tbl_batch_lines
tbl_batch_offers
tbl_batch_restrictions
tbl_batch_sources
tbl_carriers
tbl_channels
tbl_communications_attachments
tbl_communications_channels
tbl_communications_history
tbl_communications_messages
tbl_communications_templates
tbl_communications_variables
tbl_company
tbl_countries
tbl_dailys
tbl_indicators
tbl_inventory
tbl_invoices
tbl_languages
tbl_log_acesso_adm
tbl_log_emails
tbl_logs_tracking
tbl_marketplaces
tbl_markets
tbl_notifications
tbl_offers_reports
tbl_operations_notifications
tbl_orders
tbl_orders_history
tbl_orders_notifications
tbl_orders_payment
tbl_packages
tbl_payments
tbl_purchase_details
tbl_purchase_details_itens
tbl_purchase_order_timeline
tbl_purchases
tbl_rules
tbl_rules_actions
tbl_rules_conditions
tbl_seller_payment_summary
tbl_subscriptions
tbl_suppliers
tbl_timeline
tbl_trackings
tbl_translations
tbl_user_clients
tbl_user_settings
tbl_usuarios
```

**Action Required**: None - Update connection strings only

### ❌ Tables Permanently Deleted

These tables were NOT migrated (obsolete/backup/test data):

```
tbl_categories_attributes
tbl_commission_worten
tbl_container_approved
tbl_ean_x_sku
tbl_groups
tbl_marketplace_received
tbl_offers_bk
tbl_offers_test
tbl_operation_history
tbl_orders_backup
tbl_orders_backup_25_04
tbl_price_history
tbl_price_history_backup
tbl_prices
tbl_producs_marktplaces
tbl_products_attributes
tbl_products_channels_bk
tbl_products_images
tbl_queue_files
tbl_stock_history
tbl_temp_products
tbl_temporary
```

**Action Required**: Remove any code referencing these tables

### ⚠️ Tables Deprecated (Renamed with `_old_` prefix)

These tables were renamed but data preserved. They will be deleted soon unless claimed:

```
_old_mapping_columns      (was: mapping_columns)
_old_mappings             (was: mappings)
_old_tbl_feedmaster       (was: tbl_feedmaster)
_old_tbl_offers           (was: tbl_offers)
```

**Action Required**: 
- Update code to not use these tables
- If you need them, notify the team IMMEDIATELY

### 🔄 Tables Transformed/Replaced

These tables were migrated to new normalized schema:

| Old Table | Replaced By | Description |
|-----------|-------------|-------------|
| `tbl_attributes` | `products` (weight/size fields) | Attributes merged into product dimensions |
| `tbl_attributes_groups` | `products` (weight/size fields) | Attributes merged into product dimensions |
| `tbl_categories` | `categories` + `catalog_translations` | Normalized with i18n support |
| `tbl_products` | `products` | Normalized product master data |
| `tbl_user_products` | `provider_offers` + `products` + `categories` + `catalog_translations` | Split into 4 normalized tables |
| `tbl_products_channels` | `provider_offers` + `marketplace_offers` | Reconstructed from offers with marketplace support |

**Action Required**: Update queries using field mappings below

---

## New Catalog Schema - Entity Relationship Diagram

```mermaid
erDiagram
    brands ||--o{ products : "brand_id"
    colors ||--o{ products : "color_id"
    categories ||--o{ categories : "parent_id (self-ref)"
    categories ||--o{ products : "category_id"
    products ||--o{ product_variants : "product_id"
    products ||--o{ product_media : "product_id"
    products ||--o{ provider_offers : "product_id"
    products ||--o{ provider_offers : "product_id (via ean)"
    products ||--o{ marketplace_offers : "product_id"
    products ||--o{ catalog_translations : "entity_id (type=product)"
    categories ||--o{ catalog_translations : "entity_id (type=category)"
    colors ||--o{ catalog_translations : "entity_id (type=color)"
    provider_offers ||--o{ marketplace_offers : "offer_id"
    tbl_user_clients ||--o{ provider_offers : "provider_id"
    tbl_user_clients ||--o{ marketplace_offers : "provider_id"
    tbl_marketplaces ||--o{ marketplace_offers : "marketplace_id"

    brands {
        int id PK
        varchar name
        varchar logo_url
        enum status
        timestamptz created_at
        timestamptz updated_at
    }

    colors {
        int id PK
        enum status
        timestamptz created_at
        timestamptz updated_at
    }

    categories {
        int id PK
        int parent_id FK
        enum status
        timestamptz created_at
        timestamptz updated_at
    }

    products {
        int id PK
        varchar ean UK
        int category_id FK
        int brand_id FK
        int color_id FK
        decimal product_weight_kg
        decimal product_height_cm
        decimal product_width_cm
        decimal product_length_cm
        decimal package_weight_kg
        decimal package_height_cm
        decimal package_width_cm
        decimal package_length_cm
        decimal volume_m3
        enum status
        enum review_status
        timestamptz created_at
        timestamptz updated_at
    }

    product_variants {
        int id PK
        int product_id FK
        int provider_id
        varchar name
        text description
        text short_description
        varchar slug
        varchar brand_name
        decimal product_weight_kg
        decimal product_height_cm
        decimal product_width_cm
        decimal product_length_cm
        decimal package_weight_kg
        decimal package_height_cm
        decimal package_width_cm
        decimal package_length_cm
        decimal volume_m3
        varchar image_url
        timestamptz created_at
        timestamptz updated_at
    }

    catalog_translations {
        int id PK
        int entity_id FK
        enum type
        char language_code
        varchar name
        text description
        text short_description
        varchar slug
    }

    product_media {
        int id PK
        int product_id FK
        enum type
        varchar url
        boolean is_primary
        int sort_order
    }

    provider_offers {
        bigint id PK
        bigint provider_id
        bigint product_id FK
        varchar provider_sku
        decimal base_price
        decimal units_per_package
        bigint stock
        enum condition
        enum status
        timestamptz created_at
        timestamptz updated_at
    }

    provider_offers {
        int id PK
        int provider_id FK
        enum provider_type
        varchar ean
        varchar sku
        decimal base_price
        int stock
        enum condition
        int time_to_ship
        decimal shipping_price
        enum status
        timestamptz created_at
        timestamptz updated_at
    }

    marketplace_offers {
        int id PK
        int offer_id FK
        int provider_id FK
        enum provider_type
        int product_id FK
        decimal base_price
        decimal final_price
        int-array applied_rules
        int stock
        enum condition
        int time_to_ship
        decimal shipping_price
        int marketplace_id FK
        enum status
        enum marketplace_status
        boolean is_buybox
        timestamptz created_at
        timestamptz updated_at
    }
```

### Table Relationships Summary

| Parent Table | Child Table | Foreign Key | Relationship |
|--------------|-------------|-------------|--------------|
| **brands** | products | `brand_id` | One brand → many products |
| **colors** | products | `color_id` | One color → many products |
| **categories** | categories | `parent_id` | Self-referencing hierarchy |
| **categories** | products | `category_id` | One category → many products |
| **categories** | catalog_translations | `entity_id` (where type='category') | One category → many translations |
| **colors** | catalog_translations | `entity_id` (where type='color') | One color → many translations |
| **products** | product_variants | `product_id` | One product → many variants (one per provider) |
| **products** | catalog_translations | `entity_id` (where type='product') | One product → many translations |
| **products** | product_media | `product_id` | One product → many media items |
| **products** | provider_offers | `product_id` | One product → many offers (one per provider) |
| **products** | provider_offers | via `ean` | One product → many provider offers |
| **products** | marketplace_offers | `product_id` | One product → many marketplace offers |
| **provider_offers** | marketplace_offers | `offer_id` | One provider offer → many marketplace offers |
| **tbl_user_clients** | provider_offers | `provider_id` | One provider → many provider offers |
| **tbl_user_clients** | marketplace_offers | `provider_id` | One provider → many marketplace offers |
| **tbl_marketplaces** | marketplace_offers | `marketplace_id` | One marketplace → many offers |

---

## Field Mappings for tables (old to new)

### Table: `tbl_categories` → `categories` + `catalog_translations`

#### Structure: `categories`

| Old Field (`tbl_categories`) | New Field (`categories`) | Transformation | Notes |
|------------------------------|--------------------------|----------------|-------|
| `id` | `id` | Kept as-is | Primary key preserved |
| `parent_id` | `parent_id` | Kept as-is | Hierarchical structure |
| `status` | `status` | **0 → 'inactive'<br>1 → 'active'** | Now ENUM type |
| `created_at` | `created_at` | Kept as-is | |
| `name` | - | **→ `catalog_translations.name`** | i18n support |
| `name_category` | - | **→ `catalog_translations.name`** | Merged with `name` |

#### How to Query

**OLD (MySQL)**:
```sql
SELECT id, name, parent_id, status 
FROM tbl_categories 
WHERE status = 1;
```

**NEW (PostgreSQL)**:
```sql
SELECT 
    c.id,
    ct.name,
    c.parent_id,
    c.status
FROM categories c
LEFT JOIN catalog_translations ct 
    ON ct.entity_id = c.id 
    AND ct.type = 'category' 
    AND ct.language_code = 'PT'
WHERE c.status = 'active';
```

---

### Table: `tbl_products` → `products` + `catalog_translations`

#### Structure: `products`

| Old Field (`tbl_products`) | New Field | Table | Transformation | Notes |
|----------------------------|-----------|-------|----------------|-------|
| - | `id` | `products` | Introduced | Primary key |
| `ean` | `ean` | `products` | Kept as-is | Product EAN, not primary key any more |
| `sku` | - | **→ `provider_offers.provider_sku`** | SKU is provider-specific |
| `title` | - | **→ `catalog_translations.name`** | i18n support |
| `short_description` | - | **→ `catalog_translations.short_description`** | i18n support |
| `long_description` | - | **→ `catalog_translations.description`** | i18n support |
| `brand` | - | **→ `brands.name` lookup** | Normalized to brands table |
| `category_id` | `category_id` | `products` | Kept as-is | FK to categories |
| `color` | - | **→ `colors` + `catalog_translations`** | Normalized with i18n |
| `condition` | - | **→ `provider_offers.condition`** | Offer-specific, not product |
| `status` | `status` | `products` | **String → ENUM** | 'active'/'inactive' |
| `created_at` | `created_at` | `products` | Kept as-is | |
| `updated_at` | `updated_at` | `products` | Kept as-is | |

#### How to Query

**OLD (MySQL)**:
```sql
SELECT ean, title, brand, category_id, status 
FROM tbl_products 
WHERE status = 'active';
```

**NEW (PostgreSQL)**:
```sql
SELECT 
    p.ean,
    ct.name as title,
    b.name as brand,
    p.category_id,
    p.status
FROM products p
LEFT JOIN catalog_translations ct 
    ON ct.entity_id = p.id 
    AND ct.type = 'product' 
    AND ct.language_code = 'PT'
LEFT JOIN brands b ON b.id = p.brand_id
WHERE p.status = 'active';
```

---

### Table: `tbl_user_products` → `products` + `product_variants` + `provider_offers`

This table was **split into 3 tables**:
- **`products`**: Master product catalog (one per EAN)
- **`product_variants`**: Provider-specific product data
- **`provider_offers`**: Provider pricing and inventory

#### Complete Field Mapping

| Old Field (`tbl_user_products`) | New Location | New Field | Transformation | Notes |
|----------------------------------|--------------|-----------|----------------|-------|
| `id` | `provider_offers` | `id` | **Kept intact** | Original ID preserved |
| `user_id` | `provider_offers` | `provider_id` | **Renamed** | |
| `cod_mkt` | - | - | **Removed** | Not migrated |
| `origin` | - | - | **Removed** | Not migrated |
| `barcode` | `products` | `ean` | **To product master** | Moved to products |
| `sku` | `provider_offers` | `provider_sku` | **Renamed** | Provider-specific |
| `title` |  `products` → `catalog_translations` | `name` | **To translations** | i18n support |
| `brand` | `products` → `brands` + `catalog_translations` | `brand_id` | **Normalized** | Lookup via brands table |
| `price` | `provider_offers` | `base_price` | **Renamed** | Offer pricing |
| `description` |  `products` → `catalog_translations` | `description` | **To translations** | Full description, i18n |
| `descriptionSoon` |  `products` → `catalog_translations` | `short_description` | **To translations** | Short description, i18n |
| `lengthProductWithoutPackaging` | `products` | `product_length_cm` | **To product** | Product dimensions |
| `widthProductWWithoutPackaging` | `products` | `product_width_cm` | **To product** | Product dimensions |
| `heightProductWithoutPackaging` | `products` | `product_height_cm` | **To product** | Product dimensions |
| `productWeightWithoutPackaging` | `products` | `product_weight_kg` | **To product** | Product dimensions |
| `lengthProductPackageProduct` | `products` | `package_length_cm` | **To product** | Package dimensions |
| `widthProductWPackageProduct` | `products` | `package_width_cm` | **To product** | Package dimensions |
| `heightProductPackageProduct` | `products` | `package_height_cm` | **To product** | Package dimensions |
| `producTotalWeightPackageProduct` | `products` | `package_weight_kg` | **To product** | Package dimensions |
| `weight_type` | - | - | **Removed** | Not migrated |
| `unitsProduct` | `provider_offers` | `units_per_package` | **Renamed** | |
| `cubic` | `products` | `volume_m3` | **To product** | Volume |
| `categories` | `products` → `categories` + `catalog_translations` | `category_id` | **To product** | FK to categories |
| `state` | `products` | `status` | **To product as ENUM** | 'active'/'inactive' |
| `additionalPostageBox` | - | - | **Removed** | Not migrated |
| `color` | `products` → `colors` + `catalog_translations` | `color_id` | **Normalized** | Lookup via colors + translations |
| `sku_bighub` | - | - | **Removed** | Not migrated |
| `stock` | `provider_offers` | `stock` | **Kept** | Inventory level |
| `images` | `products` → `product_media` | `url` | **Extracted** | First image becomes primary |
| `created_at` | `provider_offers` | `created_at` | Kept as-is | Timestamp |
| `updated_at` | `provider_offers` | `updated_at` | Kept as-is | Timestamp |
| `is_deleted` | - | - | **Removed** | Not migrated |
| `product_session` | `provider_offers` | `status` | **2 → 'active'<br>else → 'inactive'** | Offer status as ENUM |
| `obs` | - | - | **Removed** | Not migrated |
| `active` | - | - | **Removed** | Merged into status |
| `status` | - | - | **Removed** | Merged into status |
| `postage` | - | - | **Removed** | Not migrated |
| `cost_price` | `provider_offers` | `base_price` | **Merged** | Combined with price |
| `transaction_id` | - | - | **Removed** | Not migrated |
| `date_send_container` | - | - | **Removed** | Not migrated |
| `weight` | `products` | `product_weight_kg` | **To product** | Fallback if productWeightWithoutPackaging null |
| `mvp` | - | - | **Removed** | Not migrated |
| `condition` | `provider_offers` | `condition` | **To offers as ENUM** | 'new'/'used' |
| `data_hash` | - | - | **Removed** | Not migrated |
| `leadtime_to_ship` | - | - | **Removed** | Not migrated |

#### How to Query (Complex Example)

**OLD (MySQL)** - Get provider product with all info:
```sql
SELECT 
    id,
    user_id,
    barcode,
    sku,
    title,
    brand,
    price,
    stock,
    condition,
    product_session
FROM tbl_user_products
WHERE user_id = 123 AND product_session = 2;
```

**NEW (PostgreSQL)** - Same data:
```sql
SELECT 
    po.id,
    po.provider_id,
    p.ean,
    po.provider_sku,
    ct.name as title,
    b.name as brand,
    po.base_price,
    po.stock,
    po.condition,
    po.status
FROM provider_offers po
INNER JOIN products p ON p.id = po.product_id
LEFT JOIN catalog_translations ct 
    ON ct.entity_id = p.id 
    AND ct.type = 'product' 
    AND ct.language_code = 'PT'
LEFT JOIN brands b ON b.id = p.brand_id
WHERE po.provider_id = 123 
  AND po.status = 'active';
```

---

### Table: `tbl_products_channels` → `seller_offers` + `marketplace_offers`

#### Structure: `marketplace_offers`

| Old Field (`tbl_products_channels`) | New Field | Table | Transformation | Notes |
|-------------------------------------|-----------|-------|----------------|-------|
| `id` | `id` | `marketplace_offers` | Kept as-is | Primary key |
| - | `offer_id` | `marketplace_offers` | **Introduced** | FK to provider_offers |
| `user_id` | `provider_id` | `marketplace_offers` | **Renamed** | FK to tbl_user_clients |
| - | `provider_type` | `marketplace_offers` | **Introduced** | 'seller' or 'supplier' |
| `channel` + `country` | `marketplace_id` | `marketplace_offers` | **Combined** | FK to tbl_marketplaces |
| `ean` | `product_id` | `marketplace_offers` | **→ products lookup** | Via product_id → product.id |
| `sku` | - | **→ `provider_offers.sku`** | To base offer | |
| `price` | `final_price` | `marketplace_offers` | **Renamed** | After rules applied |
| - | `base_price` | `marketplace_offers` | **Introduced** | Before rules |
| - | `applied_rules` | `marketplace_offers` | **Introduced** | Array of rule IDs |
| `stock` | `stock` | `marketplace_offers` | Kept as-is | Inventory |
| `condition` | `condition` | `marketplace_offers` | **→ ENUM** | 'new' or 'used' |
| `shipping_time` | `time_to_ship` | `marketplace_offers` | **Renamed** | Days to ship |
| `shipping_price` | `shipping_price` | `marketplace_offers` | Kept as-is | Shipping cost |
| `is_active` | - | - | **Removed** | Not used |
| `status` | `status` | `marketplace_offers` | **→ ENUM** | 'active' or 'inactive' |
| - | `marketplace_status` | `marketplace_offers` | **Introduced** | 'pending', 'approved', 'rejected' |
| - | `is_buybox` | `marketplace_offers` | **Introduced** | Buybox winner flag |
| `notes` | - | - | **Removed** | Not migrated |
| `created_at` | `created_at` | `marketplace_offers` | Kept as-is | |
| `updated_at` | `updated_at` | `marketplace_offers` | Kept as-is | |

#### How to Query

**OLD (MySQL)**:
```sql
SELECT 
    id, 
    user_id, 
    channel, 
    country, 
    ean, 
    price, 
    stock, 
    status
FROM tbl_products_channels
WHERE user_id = 123 AND status = 1;
```

**NEW (PostgreSQL)**:
```sql
SELECT 
    mo.id,
    mo.provider_id,
    m.name as marketplace,
    m.country,
    p.ean,
    mo.final_price,
    mo.stock,
    mo.status
FROM marketplace_offers mo
INNER JOIN tbl_marketplaces m ON m.id = mo.marketplace_id
INNER JOIN products p ON p.id = mo.product_id
WHERE mo.provider_id = 123 
  AND mo.status = 'active';
```

---

## New Table Structures

### 1. `brands`

Product brands/manufacturers

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| `id` | SERIAL | PRIMARY KEY | Brand ID |
| `name` | VARCHAR(255) | NOT NULL | Brand name (e.g., "Nike", "Apple") |
| `logo_url` | VARCHAR(500) | | URL to brand logo |
| `status` | ENUM | NOT NULL, DEFAULT 'active' | 'active' or 'inactive' |
| `created_at` | TIMESTAMPTZ | NOT NULL | Creation timestamp |
| `updated_at` | TIMESTAMPTZ | NOT NULL | Last update |

**Usage**: Normalized brand names, deduplicated

---

### 2. `colors`

Product colors (names in translations)

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| `id` | SERIAL | PRIMARY KEY | Color ID |
| `status` | ENUM | NOT NULL, DEFAULT 'active' | 'active' or 'inactive' |
| `created_at` | TIMESTAMPTZ | NOT NULL | Creation timestamp |
| `updated_at` | TIMESTAMPTZ | NOT NULL | Last update |

**Usage**: Color dimension. Actual color names stored in `catalog_translations`

**Query color name**:
```sql
SELECT ct.name as color_name
FROM colors c
JOIN catalog_translations ct 
    ON ct.entity_id = c.id 
    AND ct.type = 'color'
WHERE c.id = 5;
```

---

### 3. `categories`

Product categories with hierarchy

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| `id` | SERIAL | PRIMARY KEY | Category ID (preserved from old table) |
| `parent_id` | INTEGER | FK → categories.id | Parent category for hierarchy |
| `status` | ENUM | NOT NULL, DEFAULT 'active' | 'active' or 'inactive' |
| `created_at` | TIMESTAMPTZ | NOT NULL | Creation timestamp |
| `updated_at` | TIMESTAMPTZ | NOT NULL | Last update |

**Usage**: Category hierarchy. Names stored in `catalog_translations`

**Query category tree**:
```sql
SELECT 
    c.id,
    ct.name,
    c.parent_id,
    pt.name as parent_name
FROM categories c
LEFT JOIN catalog_translations ct 
    ON ct.entity_id = c.id 
    AND ct.type = 'category' 
    AND ct.language_code = 'PT'
LEFT JOIN categories pc ON pc.id = c.parent_id
LEFT JOIN catalog_translations pt 
    ON pt.entity_id = pc.id 
    AND pt.type = 'category' 
    AND pt.language_code = 'PT'
WHERE c.status = 'active';
```

---

### 4. `products`

Product master catalog (one record per EAN)

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| `id` | SERIAL | PRIMARY KEY | Product ID |
| `ean` | VARCHAR(50) | UNIQUE | European Article Number (barcode) |
| `category_id` | INTEGER | FK → categories.id | Product category |
| `brand_id` | INTEGER | FK → brands.id | Product brand |
| `color_id` | INTEGER | FK → colors.id | Product color |
| `product_weight_kg` | DECIMAL(10,3) | | Weight without packaging (kg) |
| `product_height_cm` | DECIMAL(10,2) | | Height without packaging (cm) |
| `product_width_cm` | DECIMAL(10,2) | | Width without packaging (cm) |
| `product_length_cm` | DECIMAL(10,2) | | Length without packaging (cm) |
| `package_weight_kg` | DECIMAL(10,3) | | Total package weight (kg) |
| `package_height_cm` | DECIMAL(10,2) | | Package height (cm) |
| `package_width_cm` | DECIMAL(10,2) | | Package width (cm) |
| `package_length_cm` | DECIMAL(10,2) | | Package length (cm) |
| `volume_m3` | DECIMAL(10,3) | | Product volume (m³) |
| `status` | ENUM | NOT NULL, DEFAULT 'active' | 'active' or 'inactive' |
| `review_status` | ENUM | NOT NULL, DEFAULT 'pending' | 'pending' or 'approved' |
| `created_at` | TIMESTAMPTZ | NOT NULL | Creation timestamp |
| `updated_at` | TIMESTAMPTZ | NOT NULL | Last update |

**Usage**: 
- Master product data (one per EAN, deduplicated from multiple providers)
- Product names/descriptions in `catalog_translations`
- Most complete dimension data selected from all provider variants

---

### 5. `product_variants`

Provider-specific product information

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| `id` | SERIAL | PRIMARY KEY | Variant ID |
| `product_id` | INTEGER | FK → products.id, NOT NULL | Link to master product |
| `provider_id` | INTEGER | NOT NULL | Provider/seller ID (from tbl_user_clients) |
| `name` | VARCHAR(255) | | Product name from this provider |
| `description` | TEXT | | Full description from this provider |
| `short_description` | TEXT | | Short description from this provider |
| `slug` | VARCHAR(255) | | URL-friendly slug |
| `brand_name` | VARCHAR(255) | | Brand name as provided by this seller |
| `product_weight_kg` | DECIMAL(10,3) | | Product weight from this provider |
| `product_height_cm` | DECIMAL(10,2) | | Product height from this provider |
| `product_width_cm` | DECIMAL(10,2) | | Product width from this provider |
| `product_length_cm` | DECIMAL(10,2) | | Product length from this provider |
| `package_weight_kg` | DECIMAL(10,3) | | Package weight from this provider |
| `package_height_cm` | DECIMAL(10,2) | | Package height from this provider |
| `package_width_cm` | DECIMAL(10,2) | | Package width from this provider |
| `package_length_cm` | DECIMAL(10,2) | | Package length from this provider |
| `volume_m3` | DECIMAL(10,3) | | Volume from this provider |
| `image_url` | VARCHAR(500) | | Primary image URL from this provider |
| `created_at` | TIMESTAMPTZ | NOT NULL | Creation timestamp |
| `updated_at` | TIMESTAMPTZ | NOT NULL | Last update |

**Usage**: Each provider can have their own product data for the same EAN

---

### 6. `catalog_translations`

Multi-language translations for colors, categories, and products

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| `id` | SERIAL | PRIMARY KEY | Translation ID |
| `entity_id` | INTEGER | NOT NULL | ID of color/category/product |
| `type` | ENUM | NOT NULL | 'color', 'category', or 'product' |
| `language_code` | CHAR(2) | NOT NULL | ISO language code (e.g., 'PT') |
| `name` | VARCHAR(255) | | Translated name |
| `description` | TEXT | | Translated full description |
| `short_description` | TEXT | | Translated short description |
| `slug` | VARCHAR(255) | | URL-friendly slug |

**Unique Constraint**: `(entity_id, type, language_code)` - one translation per entity per language

**Usage**: Store all translatable text (product names, category names, color names, descriptions)

---

### 7. `product_media`

Product images and videos

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| `id` | SERIAL | PRIMARY KEY | Media ID |
| `product_id` | INTEGER | FK → products.id, NOT NULL | Product this media belongs to |
| `type` | ENUM | NOT NULL | 'image' or 'video' |
| `url` | VARCHAR(500) | NOT NULL | URL to media asset |
| `is_primary` | BOOLEAN | NOT NULL, DEFAULT false | TRUE for primary/default image |
| `sort_order` | INTEGER | NOT NULL, DEFAULT 0 | Display order (0 = primary) |

**Usage**: 
- Extracted from `tbl_user_products.images` field
- First image marked as `is_primary = true`
- Supports multiple images per product

---

### 8. `provider_offers`

Provider offers (pricing, inventory, SKU)

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| `id` | BIGINT | PRIMARY KEY | Offer ID (preserved from tbl_user_products.id) |
| `provider_id` | BIGINT | NOT NULL | Provider/seller ID |
| `product_id` | BIGINT | FK → products.id, NOT NULL | Link to master product |
| `provider_sku` | VARCHAR(100) | | Provider's SKU for this product |
| `base_price` | DECIMAL(15,6) | | Base price for this offer |
| `units_per_package` | DECIMAL(10,2) | DEFAULT 1.00 | Units per package |
| `stock` | BIGINT | NOT NULL, DEFAULT 0 | Available inventory |
| `condition` | ENUM | NOT NULL, DEFAULT 'new' | 'new' or 'used' |
| `status` | ENUM | NOT NULL, DEFAULT 'active' | 'active' or 'inactive' |
| `created_at` | TIMESTAMPTZ | NOT NULL | Creation timestamp |
| `updated_at` | TIMESTAMPTZ | NOT NULL | Last update |

**Status Mapping**:
- `tbl_user_products.product_session = 2` → `status = 'active'`
- `tbl_user_products.product_session ≠ 2` → `status = 'inactive'`

**Usage**: Provider-specific offers for products

---

### 09. `marketplace_offers`

Marketplace-specific offers (extends seller_offers)

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| `id` | SERIAL | PRIMARY KEY | Marketplace offer ID |
| `offer_id` | INTEGER | FK → seller_offers.id, NOT NULL | Reference to base seller offer |
| `provider_id` | INTEGER | FK → tbl_user_clients.id, NOT NULL | Provider/seller ID |
| `provider_type` | ENUM | NOT NULL | 'seller' or 'supplier' |
| `product_id` | INTEGER | FK → products.id, NOT NULL | Product reference |
| `base_price` | DECIMAL(15,6) | | Base price |
| `final_price` | DECIMAL(10,2) | | Final price after rules |
| `applied_rules` | INTEGER[] | | Array of rule IDs applied |
| `stock` | INTEGER | DEFAULT 0 | Available stock |
| `condition` | ENUM | NOT NULL, DEFAULT 'new' | 'new' or 'used' |
| `time_to_ship` | INTEGER | | Days to ship |
| `shipping_price` | DECIMAL(10,2) | | Shipping cost |
| `marketplace_id` | INTEGER | FK → tbl_marketplaces.id, NOT NULL | Target marketplace |
| `status` | ENUM | NOT NULL, DEFAULT 'active' | 'active' or 'inactive' |
| `marketplace_status` | ENUM | NOT NULL, DEFAULT 'pending' | 'pending', 'approved', or 'rejected' |
| `is_buybox` | BOOLEAN | NOT NULL, DEFAULT false | TRUE if winning buybox |
| `created_at` | TIMESTAMPTZ | NOT NULL | Creation timestamp |
| `updated_at` | TIMESTAMPTZ | NOT NULL | Last update |

**Replaces**: `tbl_products_channels`

**Usage**: Marketplace-specific offers with pricing rules and buybox status

**Relationship**:
```
provider_offers (base offer)
    ↓ (offer_id)
marketplace_offers (marketplace-specific extension)
```

---

## Common Query Patterns

### 1. Get Product by EAN with Full Details

```sql
SELECT 
    p.id,
    p.ean,
    ct.name as product_name,
    ct.description,
    ct.short_description,
    b.name as brand,
    cat_t.name as category,
    col_t.name as color,
    p.product_weight_kg,
    p.product_length_cm,
    p.product_width_cm,
    p.product_height_cm,
    p.status
FROM products p
LEFT JOIN catalog_translations ct 
    ON ct.entity_id = p.id 
    AND ct.type = 'product' 
    AND ct.language_code = 'PT'
LEFT JOIN brands b ON b.id = p.brand_id
LEFT JOIN categories c ON c.id = p.category_id
LEFT JOIN catalog_translations cat_t 
    ON cat_t.entity_id = c.id 
    AND cat_t.type = 'category' 
    AND cat_t.language_code = 'PT'
LEFT JOIN colors col ON col.id = p.color_id
LEFT JOIN catalog_translations col_t 
    ON col_t.entity_id = col.id 
    AND col_t.type = 'color' 
    AND col_t.language_code = 'PT'
WHERE p.ean = '7891234567890';
```

### 2. Get Provider Offers for a Product

```sql
SELECT 
    po.id,
    po.provider_id,
    uc.name as provider_name,
    po.provider_sku,
    po.base_price,
    po.stock,
    po.condition,
    po.status
FROM provider_offers po
INNER JOIN products p ON p.id = po.product_id
LEFT JOIN tbl_user_clients uc ON uc.id = po.provider_id
WHERE p.ean = '7891234567890'
  AND po.status = 'active'
ORDER BY po.base_price ASC;
```

### 3. Get Product with Images

```sql
SELECT 
    p.ean,
    ct.name as product_name,
    pm.url as image_url,
    pm.is_primary,
    pm.sort_order
FROM products p
LEFT JOIN catalog_translations ct 
    ON ct.entity_id = p.id 
    AND ct.type = 'product' 
    AND ct.language_code = 'PT'
LEFT JOIN product_media pm ON pm.product_id = p.id
WHERE p.id = 123
ORDER BY pm.sort_order;
```

### 4. Search Products by Name (i18n)

```sql
SELECT 
    p.id,
    p.ean,
    ct.name,
    ct.short_description,
    b.name as brand
FROM catalog_translations ct
INNER JOIN products p ON p.id = ct.entity_id
LEFT JOIN brands b ON b.id = p.brand_id
WHERE ct.type = 'product'
  AND ct.language_code = 'PT'
  AND ct.name ILIKE '%keyword%'
  AND p.status = 'active';
```

### 5. Get Provider-Specific Product Data

```sql
SELECT 
    pv.provider_id,
    pv.name as provider_product_name,
    pv.description as provider_description,
    pv.brand_name as provider_brand,
    pv.product_weight_kg,
    pv.package_weight_kg
FROM product_variants pv
INNER JOIN products p ON p.id = pv.product_id
WHERE p.ean = '7891234567890'
  AND pv.provider_id = 123;
```

### 6. Get Marketplace Offers for a Product

```sql
SELECT 
    mo.id,
    mo.provider_id,
    m.name as marketplace_name,
    mo.base_price,
    mo.final_price,
    mo.stock,
    mo.condition,
    mo.marketplace_status,
    mo.is_buybox,
    mo.status
FROM marketplace_offers mo
INNER JOIN products p ON p.id = mo.product_id
INNER JOIN tbl_marketplaces m ON m.id = mo.marketplace_id
WHERE p.ean = '7891234567890'
  AND mo.status = 'active'
  AND mo.marketplace_status = 'approved'
ORDER BY mo.is_buybox DESC, mo.final_price ASC;
```

### 7. Get Provider Offer with Marketplace Extensions

```sql
SELECT 
    po.id as provider_offer_id,
    po.provider_id,
    po.ean,
    po.sku,
    po.base_price as provider_base_price,
    po.stock as provider_stock,
    mo.id as marketplace_offer_id,
    m.name as marketplace_name,
    mo.final_price,
    mo.marketplace_status,
    mo.is_buybox
FROM provider_offers po
LEFT JOIN marketplace_offers mo ON mo.offer_id = po.id
LEFT JOIN tbl_marketplaces m ON m.id = mo.marketplace_id
WHERE po.provider_id = 123
  AND po.status = 'active';
```

---

## ENUM Types Reference

### `catalog_entity_type`
- `'color'` - Color entity
- `'category'` - Category entity
- `'product'` - Product entity

### `catalog_status`
- `'active'` - Active/enabled
- `'inactive'` - Inactive/disabled

### `product_review_status`
- `'pending'` - Pending review
- `'approved'` - Approved

### `media_type`
- `'image'` - Image file
- `'video'` - Video file

### `offer_condition`
- `'new'` - New product
- `'used'` - Used product

### `offer_status`
- `'active'` - Active offer
- `'inactive'` - Inactive offer

### `provider_type`
- `'seller'` - Seller/reseller
- `'supplier'` - Supplier/manufacturer

### `marketplace_status`
- `'pending'` - Pending approval
- `'approved'` - Approved for marketplace
- `'rejected'` - Rejected

---

## Migration Checklist for Developers

### Database Connection

- [ ] Update database connection strings:
  - Host (PostgreSQL)
  - Database: `bighub`
  - Username: (from env vars)
  - Password: (from env vars)

### Code Updates

- [ ] Replace `tbl_user_products` queries with `products` + `provider_offers` + `product_variants`
- [ ] Replace `tbl_categories` queries with `categories` + `catalog_translations`
- [ ] Replace `tbl_products` queries with `products` + `catalog_translations`
- [ ] Add JOINs to `catalog_translations` for names/descriptions
- [ ] Update status checks: `status = 1` → `status = 'active'`
- [ ] Update condition checks: `condition = 'new'` → use ENUM
- [ ] Rename `user_id` → `provider_id`
- [ ] Rename `sku` → `provider_sku`
- [ ] Update dimension field names (add `product_` or `package_` prefix)

### Testing

- [ ] Test product listing pages
- [ ] Test product detail pages
- [ ] Test provider/seller pages
- [ ] Test search functionality (now uses translations)
- [ ] Test filtering by category, brand, color
- [ ] Test offer management
- [ ] Test image display (now from product_media)
- [ ] Test multi-language support (if applicable)
- [ ] Test marketplace offers (tbl_products_channels replacement)
- [ ] Test buybox functionality
- [ ] Test pricing rules (applied_rules field)

---

## Quick Reference: Old → New

| Old Table | New Table(s) | Type of Change |
|-----------|--------------|----------------|
| `tbl_categories` | `categories` + `catalog_translations` | Normalized + i18n |
| `tbl_products` | `products` + `catalog_translations` | Normalized + i18n |
| `tbl_user_products` | `products` + `product_variants` + `provider_offers` | Split into 3 tables |
| `tbl_products_channels` | `provider_offers` + `marketplace_offers` | Reconstructed with marketplace support |
| `tbl_attributes` | `products` (dimensions) | Merged into product |
| `mapping_columns` | `_old_mapping_columns` | Deprecated |
| `mappings` | `_old_mappings` | Deprecated |
| `tbl_feedmaster` | `_old_tbl_feedmaster` | Deprecated |
| `tbl_offers` | `_old_tbl_offers` | Deprecated |

---

## Support

**Questions?** Contact the DevOps/Database team

**Documentation**:
- `MODEL_STRUCTURE.md` - DBT model structure
- `MIGRATION_WORKFLOW.md` - Complete migration steps

**DBT Docs**: Run `dbt docs serve` for interactive schema documentation


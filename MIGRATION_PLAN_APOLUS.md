# Migration Plan: Apolus Order Processing - MySQL to PostgreSQL

## Overview

Este documento detalha as mudanças necessárias no código do sistema Apolus para migrar de MySQL (`db_accounts`) para PostgreSQL (`bighub`) com o novo schema normalizado de catálogo.

**IMPORTANTE**: Este é um plano de migração de código. A migração de dados já foi realizada pela equipe de DevOps/Database.

---

## Migration Summary

- **Current Database**: MySQL - `db_accounts` @ **************
- **Target Database**: PostgreSQL - `bighub` (novo servidor)
- **Affected Tables**: 2 das 3 tabelas principais do Apolus
- **Impact Level**: 🔴 **HIGH** - Código crítico de matching de pedidos

---

## Tables Migration Status

### ✅ No Changes Required

| Table | Status | Action |
|-------|--------|--------|
| `tbl_user_clients` | **Kept as-is** | ✅ Apenas atualizar connection string |

### ⚠️ Code Changes Required

| Old Table (MySQL) | New Tables (PostgreSQL) | Impact |
|-------------------|------------------------|--------|
| `tbl_user_products` | `products` + `product_variants` + `provider_offers` + `product_media` | 🔴 HIGH |
| `tbl_products_channels` | `provider_offers` + `marketplace_offers` | 🔴 HIGH |

---

## Field Mappings - Quick Reference

### 1. `tbl_user_products` → New Schema

| Old Field | New Location | New Field |
|-----------|--------------|-----------|
| `id` | `provider_offers` | `id` |
| `user_id` | `provider_offers` | `provider_id` |
| `barCode` | `products` | `ean` |
| `images` | `product_media` | `url` (is_primary = true) |
| `created_at` | `provider_offers` | `created_at` |
| `updated_at` | `provider_offers` | `updated_at` |

### 2. `tbl_products_channels` → New Schema

| Old Field | New Location | New Field |
|-----------|--------------|-----------|
| `id` | `marketplace_offers` | `id` |
| `user_id` | `marketplace_offers` | `provider_id` |
| `ean` | `marketplace_offers` | `product_id` (FK to products.id) |
| `channel` | `marketplace_offers` | `marketplace_id` (FK to tbl_marketplaces.id) |
| `price` | `marketplace_offers` | `final_price` |
| `stock` | `marketplace_offers` | `stock` |
| `is_active` | `marketplace_offers` | `status` ('active' or 'inactive') |
| `shipping_price` | `marketplace_offers` | `shipping_price` |

### 3. `tbl_user_clients` → No Changes ✅

Permanece idêntica. Apenas atualizar connection string.

---

## Code Changes Required

### File 1: `app/Models/ProductChannel.php`

**Current Code**:
```php
class ProductChannel extends Model
{
    use HasFactory;

    protected $table = 'tbl_products_channels';
    protected $primaryKey = 'id';
    public $timestamps = false;

    protected $fillable = [
        'ean', 'channel', 'user_id', 'is_active', 'stock',
        'price', 'shipping_price'
    ];
}
```

**New Code**:
```php
class ProductChannel extends Model
{
    use HasFactory;

    protected $table = 'marketplace_offers';
    protected $primaryKey = 'id';
    public $timestamps = true; // ⚠️ Changed to true

    protected $fillable = [
        'offer_id',
        'provider_id',      // was: user_id
        'provider_type',
        'product_id',       // was: ean (now FK)
        'base_price',
        'final_price',      // was: price
        'applied_rules',
        'stock',
        'condition',
        'time_to_ship',
        'shipping_price',
        'marketplace_id',   // was: channel
        'status',           // was: is_active
        'marketplace_status',
        'is_buybox'
    ];

    protected $casts = [
        'applied_rules' => 'array',
        'is_buybox' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Relationships
    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function marketplace()
    {
        return $this->belongsTo(Marketplace::class, 'marketplace_id');
    }

    public function provider()
    {
        return $this->belongsTo(UserClient::class, 'provider_id');
    }

    public function providerOffer()
    {
        return $this->belongsTo(ProviderOffer::class, 'offer_id');
    }
}
```

---

### File 2: `app/Models/UserProduct.php`

**Current Code**:
```php
class UserProduct extends Model
{
    use HasFactory;

    protected $table = 'tbl_user_products';
    protected $primaryKey = 'id';

    protected $fillable = [
        'user_id',
        'barCode',
        'images',
    ];
}
```

**New Code - Option 1: Rename to ProviderOffer.php**:
```php
// Rename file: UserProduct.php → ProviderOffer.php
class ProviderOffer extends Model
{
    use HasFactory;

    protected $table = 'provider_offers';
    protected $primaryKey = 'id';

    protected $fillable = [
        'provider_id',      // was: user_id
        'product_id',
        'provider_sku',
        'base_price',
        'units_per_package',
        'stock',
        'condition',
        'status'
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Relationships
    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function provider()
    {
        return $this->belongsTo(UserClient::class, 'provider_id');
    }

    public function marketplaceOffers()
    {
        return $this->hasMany(MarketplaceOffer::class, 'offer_id');
    }
}
```

**New Code - Option 2: Create new Product.php model**:
```php
// New file: app/Models/Product.php
class Product extends Model
{
    use HasFactory;

    protected $table = 'products';
    protected $primaryKey = 'id';

    protected $fillable = [
        'ean',
        'category_id',
        'brand_id',
        'color_id',
        'product_weight_kg',
        'product_height_cm',
        'product_width_cm',
        'product_length_cm',
        'package_weight_kg',
        'package_height_cm',
        'package_width_cm',
        'package_length_cm',
        'volume_m3',
        'status',
        'review_status'
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Relationships
    public function media()
    {
        return $this->hasMany(ProductMedia::class, 'product_id');
    }

    public function primaryImage()
    {
        return $this->hasOne(ProductMedia::class, 'product_id')
            ->where('is_primary', true);
    }

    public function providerOffers()
    {
        return $this->hasMany(ProviderOffer::class, 'product_id');
    }

    public function marketplaceOffers()
    {
        return $this->hasMany(MarketplaceOffer::class, 'product_id');
    }
}
```

**New Code - Option 3: Create ProductMedia.php model**:
```php
// New file: app/Models/ProductMedia.php
class ProductMedia extends Model
{
    use HasFactory;

    protected $table = 'product_media';
    protected $primaryKey = 'id';
    public $timestamps = false;

    protected $fillable = [
        'product_id',
        'type',
        'url',
        'is_primary',
        'sort_order'
    ];

    protected $casts = [
        'is_primary' => 'boolean'
    ];

    // Relationships
    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }
}
```

---

### File 3: `app/Http/Controllers/OrderController.php`

#### Change 1: Update getSeller() method (lines 640-737)

**Current Code**:
```php
public function getSeller($ean, $channel, $price, $order_id = null, $tbl_id = null)
{
    if($channel == 'stockly'){
        // Search for seller with exact price
        $offer = ProductChannel::where('ean', $ean)
            ->where('user_id', '!=', 120)
            ->where('is_active', 1)
            ->where('stock', '>', 0)
            ->where('price', $price)
            ->orderByDesc('price')
            ->first();

        if ($offer) {
            return $this->getSellerDetails($offer);
        }

        // Search for seller with price less than or equal
        $offer = ProductChannel::where('ean', $ean)
            ->where('user_id', '!=', 120)
            ->where('is_active', 1)
            ->where('stock', '>', 0)
            ->where('price', '<=', $price)
            ->orderBy('price')
            ->first();

        if ($offer) {
            return $this->getSellerDetails($offer);
        }

        // Search for seller with price up to 5% higher
        $priceMax = $price * 1.05;
        $offer = ProductChannel::where('ean', $ean)
            ->where('user_id', '!=', 120)
            ->where('is_active', 1)
            ->where('stock', '>', 0)
            ->where('price', '<=', $priceMax)
            ->orderBy('price')
            ->first();

        if ($offer) {
            return $this->getSellerDetails($offer);
        }
    }

    // Search for seller with exact price
    $offer = ProductChannel::where('ean', $ean)
        ->where('channel', $channel)
        ->where('user_id', '!=', 120)
        ->where('is_active', 1)
        ->where('stock', '>', 0)
        ->where('price', $price)
        ->orderByDesc('price')
        ->first();

    if ($offer) {
        return $this->getSellerDetails($offer);
    }

    // Search for seller with price less than or equal
    $offer = ProductChannel::where('ean', $ean)
        ->where('channel', $channel)
        ->where('user_id', '!=', 120)
        ->where('is_active', 1)
        ->where('stock', '>', 0)
        ->where('price', '<=', $price)
        ->orderBy('price')
        ->first();

    if ($offer) {
        return $this->getSellerDetails($offer);
    }

    // Search for seller with price up to 5% higher
    $priceMax = $price * 1.05;
    $offer = ProductChannel::where('ean', $ean)
        ->where('channel', $channel)
        ->where('user_id', '!=', 120)
        ->where('is_active', 1)
        ->where('stock', '>', 0)
        ->where('price', '<=', $priceMax)
        ->orderBy('price')
        ->first();

    if ($offer) {
        return $this->getSellerDetails($offer);
    }

    // Forward to support if no match
    if ($channel != 'Miravia' && $this->debug) {
        $this->flowBighub->curl_put_update_order_user($tbl_id, 25);
        \Log::info("Forwarding order ".$order_id." to support.");
        return null;
    }
}
```

**New Code**:
```php
public function getSeller($ean, $channel, $price, $order_id = null, $tbl_id = null)
{
    // Helper function to find offer with product + marketplace join
    $findOffer = function($ean, $channel, $priceCondition, $priceValue, $orderDirection = 'asc') {
        $query = ProductChannel::query()
            ->select('marketplace_offers.*')
            ->join('products', 'products.id', '=', 'marketplace_offers.product_id')
            ->join('tbl_marketplaces', 'tbl_marketplaces.id', '=', 'marketplace_offers.marketplace_id')
            ->where('products.ean', $ean)
            ->where('marketplace_offers.provider_id', '!=', 120)
            ->where('marketplace_offers.status', 'active')
            ->where('marketplace_offers.stock', '>', 0);

        // Add channel filter if not Stockly
        if ($channel !== 'stockly') {
            $query->where('tbl_marketplaces.name', $channel);
        }

        // Apply price condition
        if ($priceCondition === '=') {
            $query->where('marketplace_offers.final_price', $priceValue);
        } else {
            $query->where('marketplace_offers.final_price', '<=', $priceValue);
        }

        // Order by price
        if ($orderDirection === 'desc') {
            $query->orderByDesc('marketplace_offers.final_price');
        } else {
            $query->orderBy('marketplace_offers.final_price');
        }

        return $query->first();
    };

    // Priority 1: Exact price match
    $offer = $findOffer($ean, $channel, '=', $price, 'desc');
    if ($offer) {
        return $this->getSellerDetails($offer);
    }

    // Priority 2: Price <= order price
    $offer = $findOffer($ean, $channel, '<=', $price, 'asc');
    if ($offer) {
        return $this->getSellerDetails($offer);
    }

    // Priority 3: Price <= order price × 1.05 (5% tolerance)
    $priceMax = $price * 1.05;
    $offer = $findOffer($ean, $channel, '<=', $priceMax, 'asc');
    if ($offer) {
        return $this->getSellerDetails($offer);
    }

    // Forward to support if no match
    if ($channel != 'Miravia' && $this->debug) {
        $this->flowBighub->curl_put_update_order_user($tbl_id, 25);
        \Log::info("Forwarding order ".$order_id." to support.");
        return null;
    }

    return null;
}
```

#### Change 2: Update getSellerDetails() method (lines 742-756)

**Current Code**:
```php
private function getSellerDetails($offer)
{
    $seller = UserClient::select('delivery_by', 'user_active')
        ->find($offer->user_id);

    if ($seller) {
        $offer->delivery_by = $seller->delivery_by;
        $offer->user_active = $seller->user_active;
    } else {
        $offer->delivery_by = null;
        $offer->user_active = null;
    }

    return $offer;
}
```

**New Code**:
```php
private function getSellerDetails($offer)
{
    // ⚠️ Changed: user_id → provider_id
    $seller = UserClient::select('delivery_by', 'user_active')
        ->find($offer->provider_id);

    if ($seller) {
        $offer->delivery_by = $seller->delivery_by;
        $offer->user_active = $seller->user_active;
    } else {
        $offer->delivery_by = null;
        $offer->user_active = null;
    }

    return $offer;
}
```

---

### File 4: `app/Models/FlowBighub.php`

#### Change 1: Update getImgProduct() method (around line 421)

**Current Code**:
```php
public function getImgProduct($ean, $user_id)
{
    $product = UserProduct::where('barCode', $ean)
        ->where('user_id', $user_id)
        ->first();

    if ($product && !empty($product->images)) {
        return $product->images;
    }

    return null;
}
```

**New Code - Option 1: Use product_media table**:
```php
public function getImgProduct($ean, $user_id)
{
    // Find product by EAN
    $product = \DB::table('products')
        ->where('ean', $ean)
        ->first();

    if (!$product) {
        return null;
    }

    // Get primary image from product_media
    $media = \DB::table('product_media')
        ->where('product_id', $product->id)
        ->where('is_primary', true)
        ->first();

    if ($media && !empty($media->url)) {
        return $media->url;
    }

    // Fallback: get first image ordered by sort_order
    $media = \DB::table('product_media')
        ->where('product_id', $product->id)
        ->where('type', 'image')
        ->orderBy('sort_order')
        ->first();

    return $media ? $media->url : null;
}
```

**New Code - Option 2: Use product_variants table (provider-specific)**:
```php
public function getImgProduct($ean, $user_id)
{
    // Get provider-specific variant image
    $variant = \DB::table('product_variants as pv')
        ->join('products as p', 'p.id', '=', 'pv.product_id')
        ->where('p.ean', $ean)
        ->where('pv.provider_id', $user_id)
        ->select('pv.image_url')
        ->first();

    if ($variant && !empty($variant->image_url)) {
        return $variant->image_url;
    }

    // Fallback to product master image
    $product = \DB::table('products')
        ->where('ean', $ean)
        ->first();

    if (!$product) {
        return null;
    }

    $media = \DB::table('product_media')
        ->where('product_id', $product->id)
        ->where('is_primary', true)
        ->first();

    return $media ? $media->url : null;
}
```

---

### File 5: `app/Services/OrderFlowService.php`

#### Change 1: Update getImgProduct() method (around line 98)

**Current Code**:
```php
public function getImgProduct($ean, $user_id)
{
    $product = UserProduct::where('barCode', $ean)
        ->where('user_id', $user_id)
        ->first();

    if ($product && !empty($product->images)) {
        $img = $product->images;
        return $img;
    }

    return null;
}
```

**New Code**:
```php
public function getImgProduct($ean, $user_id)
{
    // Find product by EAN
    $product = \DB::table('products')
        ->where('ean', $ean)
        ->first();

    if (!$product) {
        return null;
    }

    // Get primary image
    $media = \DB::table('product_media')
        ->where('product_id', $product->id)
        ->where('is_primary', true)
        ->first();

    if ($media && !empty($media->url)) {
        return $media->url;
    }

    // Fallback: first image
    $media = \DB::table('product_media')
        ->where('product_id', $product->id)
        ->where('type', 'image')
        ->orderBy('sort_order')
        ->first();

    return $media ? $media->url : null;
}
```

---

### File 6: `config/database.php`

#### Add PostgreSQL Connection

**Add to connections array**:
```php
'connections' => [

    // ... existing connections (sqlite, mysql, etc)

    'pgsql_bighub' => [
        'driver' => 'pgsql',
        'url' => env('PGSQL_URL'),
        'host' => env('PGSQL_HOST', '127.0.0.1'),
        'port' => env('PGSQL_PORT', '5432'),
        'database' => env('PGSQL_DATABASE', 'bighub'),
        'username' => env('PGSQL_USERNAME', 'postgres'),
        'password' => env('PGSQL_PASSWORD', ''),
        'charset' => env('PGSQL_CHARSET', 'utf8'),
        'prefix' => '',
        'prefix_indexes' => true,
        'search_path' => 'public',
        'sslmode' => 'prefer',
    ],

],
```

---

### File 7: `.env`

#### Update Environment Variables

**Add PostgreSQL connection**:
```env
# PostgreSQL (NEW)
PGSQL_CONNECTION=pgsql_bighub
PGSQL_HOST=<postgresql_host>
PGSQL_PORT=5432
PGSQL_DATABASE=bighub
PGSQL_USERNAME=<username>
PGSQL_PASSWORD=<password>

# MySQL (LEGACY - keep for transition period)
DB_CONNECTION=mysql
DB_HOST=**************
DB_PORT=3306
DB_DATABASE=db_accounts
DB_USERNAME=hublord
DB_PASSWORD=sYR9*z#G0fJ1,ONr4wS}xEka9WRad8r8
```

---

### File 8: Models that need connection update

**Add to each model that uses migrated tables**:

```php
// Example: ProductChannel.php
class ProductChannel extends Model
{
    // Add this line
    protected $connection = 'pgsql_bighub';

    // ... rest of the model
}
```

**Models requiring connection update**:
- `ProductChannel.php` → add `protected $connection = 'pgsql_bighub';`
- `UserProduct.php` (or new `ProviderOffer.php`) → add `protected $connection = 'pgsql_bighub';`
- New `Product.php` → add `protected $connection = 'pgsql_bighub';`
- New `ProductMedia.php` → add `protected $connection = 'pgsql_bighub';`

**Model that KEEPS MySQL connection**:
- `UserClient.php` → **NO CHANGE** (stays on MySQL/default connection for now)

---

## New Models to Create

### 1. `app/Models/Product.php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    use HasFactory;

    protected $connection = 'pgsql_bighub';
    protected $table = 'products';
    protected $primaryKey = 'id';

    protected $fillable = [
        'ean',
        'category_id',
        'brand_id',
        'color_id',
        'product_weight_kg',
        'product_height_cm',
        'product_width_cm',
        'product_length_cm',
        'package_weight_kg',
        'package_height_cm',
        'package_width_cm',
        'package_length_cm',
        'volume_m3',
        'status',
        'review_status'
    ];

    protected $casts = [
        'product_weight_kg' => 'decimal:3',
        'product_height_cm' => 'decimal:2',
        'product_width_cm' => 'decimal:2',
        'product_length_cm' => 'decimal:2',
        'package_weight_kg' => 'decimal:3',
        'package_height_cm' => 'decimal:2',
        'package_width_cm' => 'decimal:2',
        'package_length_cm' => 'decimal:2',
        'volume_m3' => 'decimal:3',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Relationships
    public function media()
    {
        return $this->hasMany(ProductMedia::class, 'product_id');
    }

    public function primaryImage()
    {
        return $this->hasOne(ProductMedia::class, 'product_id')
            ->where('is_primary', true);
    }

    public function providerOffers()
    {
        return $this->hasMany(ProviderOffer::class, 'product_id');
    }

    public function marketplaceOffers()
    {
        return $this->hasMany(ProductChannel::class, 'product_id');
    }

    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id');
    }

    public function brand()
    {
        return $this->belongsTo(Brand::class, 'brand_id');
    }

    public function color()
    {
        return $this->belongsTo(Color::class, 'color_id');
    }
}
```

### 2. `app/Models/ProductMedia.php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductMedia extends Model
{
    use HasFactory;

    protected $connection = 'pgsql_bighub';
    protected $table = 'product_media';
    protected $primaryKey = 'id';
    public $timestamps = false;

    protected $fillable = [
        'product_id',
        'type',
        'url',
        'is_primary',
        'sort_order'
    ];

    protected $casts = [
        'is_primary' => 'boolean',
        'sort_order' => 'integer'
    ];

    // Relationships
    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }
}
```

### 3. `app/Models/ProviderOffer.php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProviderOffer extends Model
{
    use HasFactory;

    protected $connection = 'pgsql_bighub';
    protected $table = 'provider_offers';
    protected $primaryKey = 'id';

    protected $fillable = [
        'provider_id',
        'product_id',
        'provider_sku',
        'base_price',
        'units_per_package',
        'stock',
        'condition',
        'status'
    ];

    protected $casts = [
        'base_price' => 'decimal:6',
        'units_per_package' => 'decimal:2',
        'stock' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Relationships
    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function provider()
    {
        return $this->belongsTo(UserClient::class, 'provider_id');
    }

    public function marketplaceOffers()
    {
        return $this->hasMany(ProductChannel::class, 'offer_id');
    }
}
```

### 4. `app/Models/Marketplace.php` (if not exists)
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Marketplace extends Model
{
    use HasFactory;

    protected $table = 'tbl_marketplaces';
    protected $primaryKey = 'id';

    protected $fillable = [
        'name',
        'country',
        'status'
    ];

    // Relationships
    public function offers()
    {
        return $this->hasMany(ProductChannel::class, 'marketplace_id');
    }
}
```

---

## Testing Checklist

### Unit Tests

- [ ] Test `OrderController::getSeller()` with new schema
  - [ ] Exact price match
  - [ ] Price <= order price
  - [ ] 5% tolerance
  - [ ] No match (forward to support)
  - [ ] Stockly channel (no channel filter)

- [ ] Test `FlowBighub::getImgProduct()` with new schema
  - [ ] Product with primary image
  - [ ] Product with multiple images
  - [ ] Product with no images
  - [ ] Non-existent EAN

- [ ] Test `OrderFlowService::getImgProduct()` with new schema
  - [ ] Same scenarios as above

### Integration Tests

- [ ] Test full order processing flow (end-to-end)
  - [ ] Mirakl marketplace order
  - [ ] Fnac order
  - [ ] Cdiscount order
  - [ ] DocMorris order
  - [ ] ManoMano order
  - [ ] Stockly order

- [ ] Test seller matching with real data
  - [ ] Single product order
  - [ ] Multi-product order (same seller)
  - [ ] Multi-product order (different sellers → support)

- [ ] Test payload generation
  - [ ] With product images
  - [ ] Without product images
  - [ ] Multiple items

### Database Tests

- [ ] Verify PostgreSQL connection works
- [ ] Verify queries return correct data
- [ ] Verify relationships work correctly
- [ ] Verify ENUM types work correctly
- [ ] Test query performance (compare with MySQL)

### Backwards Compatibility (Transition Period)

- [ ] Verify MySQL connection still works for `tbl_user_clients`
- [ ] Verify dual-connection setup works correctly
- [ ] Test rollback scenario (revert to MySQL if needed)

---

## Deployment Strategy

### Phase 1: Preparation (Development)
1. Update `.env` with PostgreSQL credentials
2. Add new models (`Product`, `ProductMedia`, `ProviderOffer`)
3. Update existing models (`ProductChannel`, `UserProduct`)
4. Run tests locally

### Phase 2: Code Migration
1. Update `OrderController::getSeller()`
2. Update `OrderController::getSellerDetails()`
3. Update `FlowBighub::getImgProduct()`
4. Update `OrderFlowService::getImgProduct()`
5. Run full test suite

### Phase 3: Staging Deployment
1. Deploy to staging environment
2. Run integration tests
3. Monitor logs for errors
4. Performance testing
5. Fix any issues found

### Phase 4: Production Deployment
1. Deploy during low-traffic period
2. Enable detailed logging
3. Monitor order processing
4. Keep MySQL connection as fallback (read-only)
5. Monitor for 48 hours

### Phase 5: Cleanup (after 1 week)
1. Remove MySQL connection for migrated tables
2. Remove old `UserProduct` model (if fully replaced)
3. Archive old code
4. Update documentation

---

## Rollback Plan

If issues are found in production:

1. **Immediate Rollback**:
   ```php
   // In models, change connection back to MySQL
   protected $connection = 'mysql'; // was: 'pgsql_bighub'

   // In ProductChannel.php
   protected $table = 'tbl_products_channels'; // was: 'marketplace_offers'
   ```

2. **Revert code changes**:
   - Restore `OrderController::getSeller()` to old version
   - Restore `getImgProduct()` methods to old version
   - Deploy immediately

3. **Investigate**:
   - Check logs for specific errors
   - Verify data integrity
   - Test queries manually

---

## Performance Considerations

### Query Optimization

**Old MySQL Query**:
```sql
SELECT * FROM tbl_products_channels
WHERE ean = '1234567890123'
  AND channel = 'fnac'
  AND is_active = 1
  AND stock > 0
LIMIT 1;
```

**New PostgreSQL Query**:
```sql
SELECT mo.*
FROM marketplace_offers mo
JOIN products p ON p.id = mo.product_id
JOIN tbl_marketplaces m ON m.id = mo.marketplace_id
WHERE p.ean = '1234567890123'
  AND m.name = 'fnac'
  AND mo.status = 'active'
  AND mo.stock > 0
LIMIT 1;
```

**Recommended Indexes**:
```sql
-- Products table
CREATE INDEX idx_products_ean ON products(ean);
CREATE INDEX idx_products_status ON products(status);

-- Marketplace offers table
CREATE INDEX idx_marketplace_offers_product_marketplace
ON marketplace_offers(product_id, marketplace_id);

CREATE INDEX idx_marketplace_offers_provider_status
ON marketplace_offers(provider_id, status);

CREATE INDEX idx_marketplace_offers_stock_status
ON marketplace_offers(stock, status) WHERE stock > 0;

-- Product media table
CREATE INDEX idx_product_media_product_primary
ON product_media(product_id, is_primary);

CREATE INDEX idx_product_media_product_sort
ON product_media(product_id, sort_order);
```

---

## Known Issues & Solutions

### Issue 1: Channel Name vs Marketplace ID

**Problem**: Old code uses `channel` string, new schema uses `marketplace_id` (integer).

**Solution**:
- Option A: Join with `tbl_marketplaces` to filter by name
- Option B: Create mapping array `$channelToMarketplaceId` and use direct ID lookup

**Recommended**: Option A (more flexible, but slightly slower)

### Issue 2: EAN vs Product ID

**Problem**: Old code queries by `ean` directly, new schema uses `product_id`.

**Solution**: Always join with `products` table to convert EAN → product_id

### Issue 3: is_active (boolean) vs status (enum)

**Problem**: Old code uses `is_active = 1`, new uses `status = 'active'`

**Solution**: Update all comparisons:
- `where('is_active', 1)` → `where('status', 'active')`
- `where('is_active', 0)` → `where('status', 'inactive')`

### Issue 4: user_id vs provider_id

**Problem**: Field renamed throughout codebase

**Solution**: Update all references:
- `$offer->user_id` → `$offer->provider_id`
- `where('user_id', ...)` → `where('provider_id', ...)`

### Issue 5: Images field (JSON string) vs product_media table

**Problem**: Old code expects single JSON string, new has separate table

**Solution**: Update `getImgProduct()` to query `product_media` table

---

## Support & Questions

- **Database Team**: For schema questions and PostgreSQL issues
- **DevOps Team**: For deployment and infrastructure
- **Lead Developer**: For code review and approval

---

## References

- `DEVELOPER_MIGRATION_GUIDE.md` - Complete migration guide
- `PROMPT.md` - Project context and current architecture
- `DATABASE_SCHEMA_ORDERS.md` - Current order processing tables
- `DATABASE_FIELDS_CONSULTED.md` - Fields currently queried

---

**Created**: 2025-10-21
**Version**: 1.0
**Status**: 🔴 READY FOR IMPLEMENTATION
**Impact**: HIGH - Critical order processing code

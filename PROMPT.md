# Project Context Prompt - Apolus Order Processing System

## Quick Overview
Este é o sistema Apolus - um middleware Laravel para processamento automatizado de pedidos de 30+ marketplaces europeus (Fnac, Cdiscount, DocMorris, ManoMano, plataformas Mirakl). O sistema faz matching inteligente entre pedidos recebidos e vendedores cadastrados, com base em EAN, canal e preço.

---

## Database Schema - Order Processing Tables

### Conexão MySQL
- **Host**: **************
- **Database**: `db_accounts`

### 3 Tabelas Principais

#### 1. `tbl_products_channels` - Matching de Produtos/Vendedores

| Campo | Tipo | Null | Descrição |
|-------|------|------|-----------|
| `id` | BIGINT UNSIGNED | NO | ID único |
| `ean` | VARCHAR(255) | NO | Código EAN do produto |
| `channel` | VARCHAR(255) | NO | Canal de venda (marketplace) |
| `user_id` | BIGINT UNSIGNED | NO | ID do vendedor |
| `is_active` | BOOLEAN | NO | Produto ativo |
| `stock` | INTEGER | YES | Quantidade em estoque |
| `price` | DECIMAL(10,2) | NO | Preço do produto |
| `shipping_price` | DECIMAL(10,2) | YES | Preço de envio |

**Modelo**: `App\Models\ProductChannel`

---

#### 2. `tbl_user_clients` - Vendedores Cadastrados

| Campo | Tipo | Null | Descrição |
|-------|------|------|-----------|
| `id` | BIGINT UNSIGNED | NO | ID único do vendedor |
| `name` | VARCHAR(255) | NO | Nome do vendedor |
| `email` | VARCHAR(255) | YES | Email do vendedor |
| `delivery_by` | VARCHAR(255) | YES | Método de entrega |
| `user_active` | BOOLEAN | NO | Vendedor ativo |

**Modelo**: `App\Models\UserClient`

---

#### 3. `tbl_user_products` - Imagens de Produtos

| Campo | Tipo | Null | Descrição |
|-------|------|------|-----------|
| `id` | BIGINT UNSIGNED | NO | ID único |
| `user_id` | BIGINT UNSIGNED | NO | ID do vendedor |
| `barCode` | VARCHAR(255) | NO | Código EAN |
| `images` | TEXT | YES | URLs de imagens (JSON) |
| `created_at` | TIMESTAMP | YES | Data de criação |
| `updated_at` | TIMESTAMP | YES | Data de atualização |

**Modelo**: `App\Models\UserProduct`

---

## Arquitetura do Sistema

### Estrutura de Código
```
service.srv/laravel/app/
├── app/
│   ├── Http/Controllers/
│   │   └── OrderController.php          # Controller principal de pedidos
│   ├── Services/
│   │   └── OrderFlowService.php         # Lógica de negócio (EAN, comissões)
│   └── Models/
│       ├── FlowBighub.php               # Integração BigHub API
│       ├── FlowFnac.php                 # Processamento Fnac
│       ├── FlowCdiscount.php            # Processamento Cdiscount
│       ├── FlowDocMorris.php            # Processamento DocMorris
│       ├── FlowManoMano.php             # Processamento ManoMano
│       ├── FlowStockly.php              # Processamento Stockly
│       ├── ProductChannel.php           # Model tbl_products_channels
│       ├── UserClient.php               # Model tbl_user_clients
│       └── UserProduct.php              # Model tbl_user_products
```

### Padrão Arquitetural
**Controllers** → **Services** → **Models (Flow Classes)** → **External APIs**

---

## Fluxo de Repasse de Orders (8 Etapas)

### 1. Ingestão do Pedido
```
Marketplace Webhook → OrderController::processOrders()
```

### 2. Extração de EAN
```php
OrderFlowService::extractEanCode($sku)
// Remove prefixos: BIGHUB-, *, etc
// Retorna: "1234567890123"
```

### 3. Seller Matching
```php
OrderController::getSeller($ean, $channel, $price)

Query: tbl_products_channels
  WHERE ean = $ean
    AND channel = $channel
    AND user_id != 120
    AND is_active = 1
    AND stock > 0

Prioridades:
  1. price = $price (exact match)
  2. price <= $price (menor/igual)
  3. price <= $price × 1.05 (tolerância 5%)

Retorna: user_id ou null
```

### 4. Validação de Comissão
```php
OrderFlowService::porcentagemaCobrar($total, $commission)
// Se commission <= 13% → retorna 15%
// Se commission > 13% → retorna commission + 3%

OrderFlowService::comparaComissoes($bighub, $marketplace)
// Valida: bighub >= marketplace
```

### 5. Geração de Payload
```php
FlowBighub::generatePayloadMirakls($channel, $order, $user_id, $country_mkt)

Para cada item:
  1. Extrai EAN
  2. Busca imagem: tbl_user_products WHERE barCode = $ean AND user_id = $user_id
  3. Calcula comissão
  4. Monta payload
```

### 6. Atribuição do Pedido
```php
FlowBighub::curl_put_update_order_user($order_id, $user_id)
// API: PUT /api/orders/{order_id}/update-user/{user_id}
```

### 7. Inserção no BigHub
```php
FlowBighub::insert_tbl_order($payload)
// API: POST /api/v2/sales/orders
```

### 8. Notificação
```php
// API: POST /api/v2/communications/messages
// Email para tbl_user_clients.email
```

---

## Regras de Negócio Críticas

### 1. Watch Products (Relógios)
```php
if (stripos($product_title, 'relógio') !== false) {
    $user_id = 843; // Vendedor fixo ID 843
}
```

### 2. Multi-Product Orders
Todos os produtos do pedido devem ter match com o **mesmo vendedor**. Caso contrário → suporte (user_id = 25).

### 3. Price Matching Priority
1. Exact match: `price = order_price`
2. Lower/equal: `price <= order_price`
3. 5% tolerance: `price <= order_price × 1.05`
4. No match: `user_id = 25` (suporte)

### 4. Commission Rule
```
BigHub Commission >= Marketplace Commission
```

### 5. Active Products Only
```php
WHERE is_active = 1 AND stock > 0
```

### 6. Excluded Seller
```php
WHERE user_id != 120  // Sempre excluído
```

---

## Marketplaces Suportados

### Mirakl (30+ plataformas)
Processamento padronizado via `processMarketplaceMirakls()`:
- pixmania, shopapotheke, planetahuerto, rueducommerce, elcorteingles
- phonehouse, leroymerlin, macway, tiendanimal, eprice, bulevip
- empik, clubefashion, carrefour, leclerc, quirumedes, pccomponentes
- conrad, bigbang, ventunique, worten, conforama, ubaldi, mediamarkt
- bricodepot, zooplus, truffaut, alltricks, castorama, conforamaiberia
- culturafr, tdmp, alternate, bestmarine

### Marketplaces Especiais
| Marketplace | Flow Class | Método |
|-------------|-----------|--------|
| fnac | FlowFnac | generatePayload() |
| cdiscount | FlowCdiscount | generatePayload() |
| docmorris | FlowDocMorris | generatePayload() |
| manomano | FlowManoMano | generatePayload() |
| stockly | FlowStockly | generatePayload() + FTP tracking |

---

## APIs Externas (BigHub)

### Base URLs
- **Connect API**: https://connect-big.bighub.store
- **App API**: https://app.bighub.store

### Autenticação
```php
// FlowBighub.php
const BEAR = '16|Mou2rwjjJMLc3BGRO4KaMgMlu3xDCvJ1ciGNOgsw78602f04';
const XAPIKEY = '7e0913c6-015d-4c60-9c49-3e4837922e53';
```

### Endpoints Principais
1. `GET /api/orders/user/{id}` - Buscar pedidos não atribuídos
2. `PUT /api/orders/{id}/update-user/{user_id}` - Atribuir pedido
3. `GET /api/orders/table/{tableId}` - Buscar pedido por ID
4. `POST /api/v2/sales/orders` - Inserir pedido
5. `POST /api/v2/communications/messages` - Enviar notificações

---

## Queries Críticas de Performance

### Seller Matching (executada 1-3x por produto)
```sql
SELECT * FROM tbl_products_channels
WHERE ean = ?
  AND channel = ?
  AND user_id != 120
  AND is_active = 1
  AND stock > 0
  AND price <= ?
ORDER BY price ASC
LIMIT 1;
```

### Buscar Imagem (executada 1x por produto)
```sql
SELECT * FROM tbl_user_products
WHERE barCode = ?
  AND user_id = ?
LIMIT 1;
```

### Buscar Detalhes do Vendedor (executada 1x por pedido)
```sql
SELECT delivery_by, user_active
FROM tbl_user_clients
WHERE id = ?
LIMIT 1;
```

---

## Performance: Queries por Pedido

### 1 Pedido com 1 Produto
```
tbl_products_channels: 1-3 queries (matching)
tbl_user_clients:      1 query (detalhes)
tbl_user_products:     1 query (imagem)
APIs BigHub:           4 calls

Total: 3-5 DB queries + 4 API calls
```

### 1 Pedido com N Produtos
```
tbl_products_channels: N × (1-3) queries
tbl_user_clients:      1 query
tbl_user_products:     N queries
APIs BigHub:           4 calls

Total: (2N a 4N + 1) DB queries + 4 API calls
```

---

## Índices Recomendados

```sql
-- Alta prioridade (critical path)
CREATE INDEX idx_ean_channel_active_stock
ON tbl_products_channels(ean, channel, is_active, stock);

CREATE INDEX idx_barcode_user
ON tbl_user_products(barCode, user_id);

-- Média prioridade
CREATE INDEX idx_user_active
ON tbl_products_channels(user_id, is_active);

CREATE INDEX idx_user_active_clients
ON tbl_user_clients(user_active);
```

---

## Casos de Erro Conhecidos

### 1. EAN Não Encontrado
- **Cenário**: Produto não existe em `tbl_products_channels`
- **Ação**: `user_id = 25` (suporte)

### 2. Preço Fora da Tolerância
- **Cenário**: `price > order_price × 1.05`
- **Ação**: `user_id = 25` (suporte)

### 3. Multi-Produto com Sellers Diferentes
- **Cenário**: Produto A → Seller 100, Produto B → Seller 200
- **Ação**: Pedido completo vai para suporte (`user_id = 25`)

### 4. Vendedor Inativo
- **Cenário**: `is_active = false` ou `user_active = false`
- **Ação**: Ignorado no matching, busca próximo ou vai para suporte

### 5. Produto Sem Imagem
- **Cenário**: Não existe em `tbl_user_products`
- **Ação**: Payload criado com `image = null`

---

## Problemas Conhecidos (CRITICAL)

### Error Handling Ausente
Documentado em `news/REFERENCIA_PROJETO.md`:

**OrderController**: Múltiplos foreach loops sem try-catch (linhas 169, 182, 419, 430, 728, 740, 864, 876)

**Unsafe Property Access**:
```php
$order->marketplace->name
$order->channel->code
```
❌ Sem null checks antes de acessar propriedades aninhadas

**Flow Classes**: foreach loops em:
- FlowFnac (linha 48)
- FlowCdiscount (linha 40)
- FlowDocMorris (linhas 47-48)

**Die/Exit statements**:
- FlowBighub linha 208
- FlowFnac linha 131

---

## Estrutura do Projeto

### Dois Apps Laravel Separados

#### 1. Main Service: `service.srv/laravel/app/`
- **Aplicação principal de processamento de pedidos**
- Toda lógica de negócio de marketplaces
- Comandos de desenvolvimento executados aqui

#### 2. Cloud App: `cloud/`
- App Laravel separado para features cloud
- Dependências e configuração próprias

**IMPORTANTE**: Sempre clarificar qual Laravel app está sendo referenciado. Default é `service.srv/laravel/app/` para trabalho de orders.

---

## Comandos Comuns

```bash
# Development (de service.srv/laravel/app/)
composer run dev  # Server + queue + logs + vite

# Testing
php artisan test
php artisan test --filter=test_seller_matching

# Code Quality
./vendor/bin/pint

# Database
php artisan migrate:fresh --seed
```

---

## Environment

```env
# service.srv/laravel/app/.env
DB_CONNECTION=mysql
DB_HOST=**************
DB_DATABASE=db_accounts

# Docker
# Roda na porta 8082 via docker-compose
```

---

## Documentos de Referência Criados

1. **DATABASE_SCHEMA.md** - Schema completo (todas as tabelas)
2. **DATABASE_SCHEMA_ORDERS.md** - Foco em repasse de orders (3 tabelas principais)
3. **DATABASE_FIELDS_CONSULTED.md** - Lista de campos consultados (simplificada)
4. **PROMPT.md** (este arquivo) - Contexto completo do projeto

---

## Como Usar Este Prompt

### Para uma nova IA/Assistente:
1. Leia este arquivo completo primeiro
2. Entenda as 3 tabelas principais e suas relações
3. Revise o fluxo de 8 etapas de processamento
4. Leia as 6 regras de negócio críticas
5. Consulte `news/REFERENCIA_PROJETO.md` para problemas conhecidos

### Para debugging de orders:
1. Verifique seller matching em `OrderController::getSeller()`
2. Valide queries em `tbl_products_channels`
3. Confirme cálculo de comissão em `OrderFlowService::porcentagemaCobrar()`
4. Check payload generation em `FlowBighub::generatePayloadMirakls()`

### Para novos marketplaces:
1. Se é Mirakl → usar `processMarketplaceMirakls()`
2. Se é especial → criar nova classe `FlowXXX` no padrão existente
3. Adicionar ao array `$specialMarketplaces` ou `$miraklMarketplaces`

---

## Tech Stack

- **Framework**: Laravel 11.x
- **PHP**: >= 8.2
- **Database**: MySQL (remote) / SQLite (local dev)
- **Cache**: Redis (opcional) / File
- **Queue**: Database driver
- **Docker**: Nginx + PHP-FPM (porta 8082)

---

## Notas Importantes

1. ❌ **Não é repositório Git** (sem controle de versão inicializado)
2. 📝 Documentação existe em PT (`service.srv/doc_pt/`) e EN (`service.srv/docs/`)
3. 🔑 Credenciais hardcoded em `FlowBighub.php` (deve mover para .env)
4. ⚠️ Error handling crítico ausente em loops e property access
5. 🎯 Sempre verificar qual Laravel app (service.srv vs cloud)

---

**Gerado em**: 2025-10-21
**Versão**: 1.0
**Projeto**: Apolus - Order Processing & Marketplace Integration

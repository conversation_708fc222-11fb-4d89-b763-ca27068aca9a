<?php

namespace App\Http\Controllers;

use App\Models\FlowBighub;
use App\Models\FlowFnac;
use App\Models\FlowCdiscount;
use App\Models\ProductChannel;
use App\Models\UserClient;

use Illuminate\Http\Request;

class OrderController extends Controller
{
    protected $flowBighub;
    private $debug;
    protected $flowFnac;
    protected $flowCdiscount;
    private $user_id_debug;
    private $order_bySupport;

    /**
     * Lista de marketplaces que usam o processador MIRAKL
     */
    protected $miraklMarketplaces = [
        'pixmania', 'shopapotheke', 'planetahuerto', 'rueducommerce', 'elcorteingles', 'phonehouse',
        'leroymerlin', 'macway', 'tiendanimal', 'eprice', 'bulevip', 'empik', 'clubefashion', 'carrefour',
        'leclerc', 'quirumedes', 'pccomponentes', 'conrad', 'bigbang', 'ventunique', 'worten', 'conforama',
        'ubaldi', 'mediamarkt', 'bricodepot', 'zooplus', 'truffaut', 'alltricks'
    ];

    protected $marketplacesAmigrar = [
        'castorama', 'xxxlgroup', 'axelspringerde',
        'iberia', 'wook', 'miravia', 'aliexpress', 'makro', 'kaufland', 'mktbighub'
    ];


    /**
     * Lista de marketplaces que usam processamento específico
     */
    protected $specialMarketplaces = [
        'fnac','cdiscount'
    ];

    protected $id_marketplace_exception = [28, 57, 58];

    /**
     * Construtor com injeção de dependência
     *
     * @param FlowBighub $flowOrders
     */
    public function __construct(FlowBighub $flowBighub, FlowFnac $flowFnac, FlowCdiscount $flowCdiscount)
    {
        $this->flowBighub = $flowBighub;
        $this->flowFnac = $flowFnac;
        $this->flowCdiscount = $flowCdiscount;
        $this->debug = false;
        $this->user_id_debug = null;
        $this->order_bySupport = false;
    }


    public function processOrdersRequestedBySupport(Request $request)
    {
        try {
;
            $this->order_bySupport = true;

            // Acessar os valores enviados na requisição
            $channel = $request->input('channel');
            $idUsuario = $request->input('id_usuario');
            $orderType = $request->input('order_type');

            $orderIdTbl = $request->input('order_id');
            $userId = $request->input('client_id');

            // var_dump($orderIdTbl, $userId);
            // die();

            // Processar a requisição...
            $resp = $this->processUnassignedOrders($orderIdTbl, $userId);

            // Retornar resposta (sem cabeçalhos CORS manuais)
            return response()->json([
                'status' => 'success',
                'message' => 'Order successfully assigned to user',
                'resp' => $resp
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to process order: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Processa os pedidos sem usuário ou um pedido específico
     *
     * @param int|null $order_id ID do pedido específico (opcional)
     * @param int|null $user_id ID do usuário (opcional)
     * @return \Illuminate\Http\Response
     */
    public function processUnassignedOrders($order_idTbl = null, $user_id = null)
    {
        $this->debug = true;
        $this->user_id_debug = $user_id;

        \Log::info("Iniciando processamento de ordens");

        // Caso 1: Pedido específico
        if ($order_idTbl !== null) {
            \Log::info("Processando pedido específico: $order_idTbl");

            // Busca dados do pedido específico pelo endpoint
            $order_data = $this->flowBighub->getOrdersByTableId($order_idTbl);

            if (!$order_data || empty($order_data)) {
                return response()->json(['message' => 'Pedido não encontrado'], 404);
            }

            // echo json_encode($order_data->data);
            // die();

            // Processa apenas este pedido específico
            $result = $this->processSingleOrder($order_data->data[0], $this->user_id_debug);

            echo json_encode($result);
            die();

            return response()->json([
                'message' => 'Processamento do pedido específico concluído',
                'order_id' => $order_idTbl,
                'result' => $result
            ]);
        }

        // Caso 2: Processamento normal (todos os pedidos sem usuário)
        \Log::info("Indo buscar as ordens para serem feitos os repasses.");

        if($this->debug){
            $orders_no_user_support = $this->flowBighub->curl_get_orders_no_user(25);
            $orders_no_user = $this->flowBighub->curl_get_orders_no_user(33);
            \Log::info("Validando orders modo debug - ATIVO - Equipe Desenvolvimento.");
        }else{
            $orders_no_user = $this->flowBighub->curl_get_orders_no_user(33);
            \Log::info("Validando orders modo produção.");
        }

        // Contadores de status
        $processed = 0;
        $failed = 0;
        $special_processed = 0;
        $waiting_acceptance = [];
        $waiting_debit_payment = [];
        $staging = [];
        $exceptions_mkt = [];
        $orders_support = [];
        $created = [];


        if (!is_null($orders_no_user_support) && isset($orders_no_user_support->data)) {
            foreach ($orders_no_user_support->data as $order) {
                $orders_support[] = [
                    'order' => $order->order_id,
                    'channel' => $order->channel->code,
                    'eans' => $order->sku,
                    'price' => $order->price,
                    'order_created' => $order->order_created_at
                ];
                continue;
            }
        }


        if (!is_null($orders_no_user) && isset($orders_no_user->data)) {
            foreach ($orders_no_user->data as $order) {

                $result = $this->exploreCode($order->channel->code);
                if (in_array($order->marketplace->id, $this->id_marketplace_exception)) {
                    $exceptions_mkt[] = [
                        'order' => $order->order_id,
                        'channel' => $result['mkt'].'-'.$result['code'],
                        'price' => $order->price,
                        'order_created' => $order->order_created_at
                    ];
                    continue;
                }

                if($order->status == 'WAITING_ACCEPTANCE' || $order->status == 'WaitingAcceptance'){
                    $waiting_acceptance[] = [
                        'order' => $order->order_id,
                        'channel' => $result['mkt'].'-'.$result['code'],
                        'price' => $order->price,
                        'order_created' => $order->order_created_at
                    ];
                    continue;
                }

                if($order->status == 'WAITING_DEBIT_PAYMENT' || $order->status == 'WAITING_DEBIT' ){
                    $waiting_debit_payment[] = [
                        'order' => $order->order_id,
                        'channel' => $result['mkt'].'-'.$result['code'],
                        'price' => $order->price,
                        'order_created' => $order->order_created_at
                    ];
                    continue;
                }


                if($order->status == 'STAGING'){
                    $staging[] = [
                        'order' => $order->order_id,
                        'channel' => $result['mkt'].'-'.$result['code'],
                        'price' => $order->price,
                        'order_created' => $order->order_created_at
                    ];
                    continue;
                }

                if($order->status == 'Created'){
                    $created[] = [
                        'order' => $order->order_id,
                        'channel' => $result['mkt'].'-'.$result['code'],
                        'price' => $order->price,
                        'order_created' => $order->order_created_at
                    ];
                    continue;
                }



                #Pular pedidos inválidos
                if (empty($order)) {
                    $failed++;
                    continue;
                }
                // if (empty($order) || ($user_id === null && $this->user_id_debug === null)) {
                //     $failed++;
                //     continue;
                // }

                // echo json_encode($order);
                // die();

                // Padronizar formato do pedido
                if (is_array($order)) {
                    $order = json_decode(json_encode($order));
                }

                $marketplace = $order->marketplace->name;

                // Verificar a qual grupo o marketplace pertence
                if (in_array($marketplace, $this->miraklMarketplaces)) {
                    // Processar usando a função unificada para marketplaces padrão
                    \Log::info("Marketplace MIRAKL - ".$marketplace);
                    if ($this->processMarketplaceMirakls($order)) {
                        $processed++;
                    } else {
                        $failed++;
                    }
                } elseif (in_array($marketplace, $this->specialMarketplaces)) {
                    // Processar usando funções específicas para marketplaces especiais
                    if ($this->processSpecialMarketplace($marketplace, $order)) {
                        $special_processed++;
                    } else {
                        $failed++;
                    }
                } else {
                    // Marketplace não reconhecido
                    \Log::warning("Marketplace não suportado: {$marketplace} - Order ID: {$order->order_id}");
                    $failed++;
                }
            }
        } else {
            // Opcional: logar ou retornar uma mensagem amigável
            echo json_encode([
                'message' => 'Nenhum pedido encontrado ou resposta inválida.',
                'status' => 'warning'
            ]);
            die();
        }

        return response()->json([
            'message' => 'Processamento concluído',
            'marketplaces_actives' => $this->miraklMarketplaces,
            'especial_marketplaces_actives' => $this->specialMarketplaces,
            'mirakls_processed' => $processed,
            'special_processed' => $special_processed,
            'waiting_acceptance' => $waiting_acceptance,
            'waiting_debit_payment' => $waiting_debit_payment,
            'staging' => $staging,
            'support_25' => $orders_support,
            'exceptions_mkt' => $exceptions_mkt,
            'failed' => $failed
        ]);
    }


    private function exploreCode($code) {
        // Verifica se existe um hífen no código
        if (strpos($code, '-') !== false) {
            // Divide a string usando o hífen como separador
            $partes = explode('-', $code);

            // Retorna um array com as duas partes
            return [
                'mkt' => $partes[0],
                'code' => $partes[1]
            ];
        }

        // Retorna valores vazios se não houver hífen
        return [
            'mkt' => '',
            'code' => ''
        ];
    }


    /**
     * Processa um pedido específico
     *
     * @param object $order Dados do pedido
     * @param int|null $user_id ID do usuário (opcional)
     * @return array Resultado do processamento
     */
    private function processSingleOrder($order)
    {
        // Padronizar formato do pedido
        if (is_array($order)) {
            $order = json_decode(json_encode($order));
        }

        $marketplace = $order->marketplace->name;
        $result = [];

        // Verificar a qual grupo o marketplace pertence
        if (in_array($marketplace, $this->miraklMarketplaces)) {
            // Processar usando a função unificada para marketplaces padrão
            \Log::info("Processando pedido específico para Marketplace MIRAKL - ".$marketplace);
            $success = $this->processMarketplaceMirakls($order);
            $result = [
                'success' => $success,
                'type' => 'mirakl',
                'marketplace' => $marketplace
            ];
        } elseif (in_array($marketplace, $this->specialMarketplaces)) {
            // Processar usando funções específicas para marketplaces especiais
            \Log::info("Processando pedido específico para Marketplace Especial - ".$marketplace);
            $success = $this->processSpecialMarketplace($marketplace, $order);
            $result = [
                'success' => $success,
                'type' => 'special',
                'marketplace' => $marketplace
            ];
        } else {
            // Marketplace não reconhecido
            \Log::warning("Marketplace não suportado: {$marketplace} - Order ID: {$order->order_id}");
            $result = [
                'success' => false,
                'error' => 'Marketplace não suportado',
                'marketplace' => $marketplace
            ];
        }

        return $result;
    }


    /**
     * Processa pedidos de diferentes marketplaces de forma simplificada
     */
    private function processMarketplaceMirakls($order, $users_id = [])
    {
        $result = $this->exploreCode($order->channel->code);
        if($result['code'] == 'INIT'){
            $result['code'] = $order->marketplace->country;
        }

        // Extrair dados básicos do pedido
        $tblId = $order->id;
        $order_id = $order->order_id;
        $marketplace = $order->marketplace->name;
        $country_mkt = $this->flowBighub->getCountryMarketplace($result['code']);
        $valor_adicional_portes = 0;

        // Normalizar o nome do marketplace
        $channel = $this->normalizeMarketplaceName($marketplace);

        // Extrair EAN de acordo com o marketplace
        $ean = $this->flowBighub->getEanCode($order->sku[0], $order, $channel);

        // Log para debug
        #var_dump("entrou {$channel} - order: {$order_id}");

        // Verifica se é um pedido com múltiplos produtos
        if ($order->product_quantity > 1) {

            // Lógica para múltiplos produtos
            $products = $order->response->order_lines;
            #$origin = $order->code_marketplace->code;

            foreach ($products as $prod) {

                // Buscar o Ean e validar se é um relogio
                if (isset($prod->product_title) && stripos($prod->product_title, 'relógio') !== false) {
                    $this->user_id_debug = 843;
                }else{
                    // Calcula preço
                    #$price = round($this->aplicarImposto($prod->total_price, $origin) - $valor_adicional_portes, 2);
                    $price = $prod->total_price;

                    // Ajuste para quantidade
                    if ($prod->quantity > 1) {
                        $price = ($prod->price / $prod->quantity) - $valor_adicional_portes;
                    }

                    // Busca vendedor
                    $price = number_format($price, 2, '.', '');
                    if(!$this->user_id_debug){
                        $found_seller = $this->getSeller($ean, $channel, $price, $order_id, $tblId);

                        $users_id = [];
                        if (isset($found_seller) && $this->isUserActive($found_seller)) {
                            $users_id[] = $found_seller->user_id;
                        }
                    }
                }

            }


            // Verifica se todos os produtos são do mesmo vendedor
            if(!$this->user_id_debug && $this->user_id_debug != 843){
                $user_id = $this->getSingleSellerId($users_id);
            }else{
                $user_id = $this->user_id_debug;
            }

        } else {
            #var_dump("{$channel} = 1 produto");

            // Lógica para produto único
            $price = $order->price - $valor_adicional_portes;

            // Ajuste para quantidade
            if (isset($order->response_first->order_lines[0]->quantity) && $order->response_first->order_lines[0]->quantity > 1) {
                $price = ($order->price / $order->response_first->order_lines[0]->quantity) - $valor_adicional_portes;
            }

            // Buscar o Ean e validar se é um relogio
            if (isset($order->response_first->order_lines[0]->product_title) && stripos($order->response_first->order_lines[0]->product_title, 'relógio') !== false) {
                $this->user_id_debug = 843;
            }else{
                // Busca vendedor
                if(!$this->user_id_debug){
                    $found_seller = $this->getSeller($ean, $channel, $price, $order_id, $tblId);
                }
            }


            if($this->debug && !$this->user_id_debug){
                #echo json_encode($found_seller);
                $user_id = isset($found_seller) ? $found_seller->user_id : null;
            }else{
                $user_id = $this->user_id_debug;
            }


        }

        // Finaliza o envio da order para o seller
        if ($user_id && !in_array($user_id, [999999])) {

            // echo json_encode($order);
            // die();

            $payload = $this->flowBighub->generatePayloadMirakls($channel, $order, $user_id, $country_mkt, $found_seller ?? null);
            if($this->debug && !$this->order_bySupport){
                echo json_encode($payload);
                #die();
            }

            $insertResult = $this->flowBighub->insert_tbl_order($payload);

            if($this->debug && !$this->order_bySupport){
                echo json_encode($insertResult);
            }

            if ($insertResult->status == 200) {
                \Log::info("Order: ".$order_id." inserida na tbl_order");

                $this->flowBighub->curl_put_update_order_user($tblId, $user_id);
                \Log::info("Repassando a order ".$order_id." para o user_id: ".$user_id);

                $this->flowBighub->createSendEmail($user_id, $order_id, $channel.'-'.$country_mkt);
                \Log::info("Notificando o seller sobre uma venda, order: ".$order_id.", email enviado para o user_id: ".$user_id);

                return true;
            }else{
                \Log::info("Error ao inserir order na tbl_orders - ".$insertResult);
                echo 'Error ao inserir na tbl_orders';
                return  null;
            }
        }else{
            echo 'Não encontrou seller';
            return null;
        }

        return false;
    }


    /**
     * Normaliza o nome do marketplace
     */
    private function normalizeMarketplaceName($marketplace)
    {
        $marketplaceMap = [
            'carrefourfr' => 'carrefour',
            'shopapothekeat' => 'shopapotheke',
            'pccompes' => 'pccomp',
            'pccompit' => 'pccomp',
            'pccompfr' => 'pccomp',
            'pccomponentes' => 'pccomp',
            'leclerc' => 'eleclerc'
        ];

        return $marketplaceMap[$marketplace] ?? $marketplace;
    }

    /**
     * Verifica se o usuário está ativo
     */
    private function isUserActive($seller)
    {
        return !isset($seller->user_active) || $seller->user_active == 1;
    }

    /**
     * Retorna um único ID de vendedor se todos forem iguais
     */
    private function getSingleSellerId($users_id)
    {
        if (empty($users_id)) {
            return null;
        }

        return $this->validarValoresIguais($users_id) ? $users_id[0] : 999999;
    }

	private function validarValoresIguais($array)
	{
		// Verifica se o array está vazio
		if (empty($array)) {
			return true;
		}

		// Obtém o primeiro valor do array
		$primeiro_valor = reset($array);

		// Verifica se todos os valores são iguais ao primeiro valor
		foreach ($array as $valor) {
			if ($valor !== $primeiro_valor) {
				return false;
			}
		}

		// Se todos os valores são iguais, retorna true
		return true;
	}

    public function getSeller($ean, $channel, $price, $order_id = null, $tbl_id = null)
    {

        // var_dump($ean);
        // die();
        // Busca um vendedor com o preço exato
        $offer = ProductChannel::where('ean', $ean)
            ->where('channel', $channel)
            ->where('user_id', '!=', 120)
            ->where('is_active', 1)
            ->where('stock', '>', 0)
            ->where('price', $price)
            ->orderByDesc('price')
            ->first();

        if ($offer) {
            return $this->getSellerDetails($offer);
        }

        // Busca um vendedor com preço menor ou igual
        $offer = ProductChannel::where('ean', $ean)
            ->where('channel', $channel)
            ->where('user_id', '!=', 120)
            ->where('is_active', 1)
            ->where('stock', '>', 0)
            ->where('price', '<=', $price)
            ->orderBy('price')
            ->first();

        if ($offer) {
            return $this->getSellerDetails($offer);
        }

        // Busca um vendedor com preço até 5% maior
        $priceMax = $price * 1.05;
        $offer = ProductChannel::where('ean', $ean)
            ->where('channel', $channel)
            ->where('user_id', '!=', 120)
            ->where('is_active', 1)
            ->where('stock', '>', 0)
            ->where('price', '<=', $priceMax)
            ->orderBy('price')
            ->first();

        if ($offer) {
            return $this->getSellerDetails($offer);
        }

        // Caso especial para repassar ao suporte
        if ($channel != 'Miravia' && $this->debug) {
            $this->flowBighub->curl_put_update_order_user($tbl_id, 25);
            \Log::info("Repassando a order ".$order_id." para o suporte.");
            return null;
        }
    }

    /**
     * Adiciona detalhes do vendedor ao objeto de oferta
     */
    private function getSellerDetails($offer)
    {
        $seller = UserClient::select('delivery_by', 'user_active')
            ->find($offer->user_id);

        if ($seller) {
            $offer->delivery_by = $seller->delivery_by;
            $offer->user_active = $seller->user_active;
        } else {
            $offer->delivery_by = null;
            $offer->user_active = null;
        }

        return $offer;
    }

    /**
     * Processa marketplaces especiais que precisam de lógica diferente
     *
     * @param string $marketplace
     * @param object $order
     * @return bool
     */
    private function processSpecialMarketplace($marketplace, $order)
    {
        // Rotear para o método específico com base no marketplace
        switch ($marketplace) {
            case 'fnac':
                return $this->processFnacOrder($order);

            case 'cdiscount':
                return $this->processCdiscountOrder($order);

            default:
                return false;
        }
    }

    /**
     * Processar pedidos do Cdiscount
     */
    private function processCdiscountOrder($order)
    {
        $result = $this->exploreCode($order->channel->code);
        if ($result['code'] == 'INIT') {
            $result['code'] = $order->marketplace->country;
        }

        // Extrair dados básicos do pedido
        $tblId = $order->id;
        $order_id = $order->order_id;
        $marketplace = $order->marketplace->name;
        $country_mkt = $this->flowBighub->getCountryMarketplace($order->channel->code);
        $valor_adicional_portes = 0;

        // Normalizar o nome do marketplace
        $channel = $this->normalizeMarketplaceName($marketplace);

        // Extrair EAN de acordo com o marketplace
        $ean = $this->flowBighub->getEanCode($order->sku[0], $order, $channel);

        #Log para debug
        // var_dump("entrou {$channel} - order: {$order_id} - country: {$country_mkt}");
        // die();

        // Verifica se é um pedido com múltiplos produtos
        if ($order->product_quantity > 1) {

            // Lógica para múltiplos produtos
            $products = $order->response->raw_data->lines;
            #$origin = $order->code_marketplace->code;

            foreach ($products as $prod) {

                // Calcula preço
                #$price = round($this->aplicarImposto($prod->total_price, $origin) - $valor_adicional_portes, 2);
                $price = $prod->totalPrice->sellingPrice;

                // Ajuste para quantidade
                if ($prod->quantity > 1) {
                    $price = ($prod->totalPrice->sellingPrice / $prod->quantity) - $valor_adicional_portes;
                }

                // Busca vendedor
                $price = number_format($price, 2, '.', '');
                if (!$this->user_id_debug) {
                    $found_seller = $this->getSeller($ean, $channel, $price, $order_id, $tblId);

                    $users_id = [];
                    if (isset($found_seller) && $this->isUserActive($found_seller)) {
                        $users_id[] = $found_seller->user_id;
                    }
                }
            }

            // Verifica se todos os produtos são do mesmo vendedor
            if (!$this->user_id_debug) {
                $user_id = $this->getSingleSellerId($users_id);
            } else {
                $user_id = $this->user_id_debug;
            }

        } else {
            #var_dump("{$channel} = 1 produto");

            // Lógica para produto único
            $price = $order->price - $valor_adicional_portes;


            // Busca vendedor
            if (!$this->user_id_debug) {
                $found_seller = $this->getSeller($ean, $channel, $price, $order_id, $tblId);
            }

            if ($this->debug && !$this->user_id_debug) {
                #echo json_encode($found_seller);
                $user_id = isset($found_seller) ? $found_seller->user_id : null;
            } else {
                $user_id = $this->user_id_debug;
            }
        }


        // Finaliza o envio da order para o seller
        if ($user_id && !in_array($user_id, [999999])) {

            $payload = $this->flowCdiscount->generatePayloadCdiscount($channel, $order, $user_id, $country_mkt, $found_seller ?? null);
            if ($this->debug && !$this->order_bySupport) {
                echo json_encode($payload);
                #die();
            }

            $insertResult = $this->flowBighub->insert_tbl_order($payload);
            if ($this->debug && !$this->order_bySupport) {
                echo json_encode($insertResult);
            }

            if ($insertResult->status == 200) {
                \Log::info("Order: " . $order_id . " inserida na tbl_order");

                $this->flowBighub->curl_put_update_order_user($tblId, $user_id);
                \Log::info("Repassando a order " . $order_id . " para o user_id: " . $user_id);

                $this->flowBighub->createSendEmail($user_id, $order_id, $channel . '-' . $country_mkt);
                \Log::info("Notificando o seller sobre uma venda, order: " . $order_id . ", email enviado para o user_id: " . $user_id);

                return true;
            } else {
                \Log::info("Error ao inserir order na tbl_orders - " . $insertResult);
                echo 'Error ao inserir na tbl_orders';
                return  null;
            }
        } else {
            echo 'Não encontrou seller';
            return null;
        }

        return false;
    }

        /**
     * Processar pedidos do Fnac
     */
    private function processFnacOrder($order)
    {
        $result = $this->exploreCode($order->channel->code);
        if ($result['code'] == 'INIT') {
            $result['code'] = $order->marketplace->country;
        }

        // Extrair dados básicos do pedido
        $tblId = $order->id;
        $order_id = $order->order_id;
        $marketplace = $order->marketplace->name;
        $country_mkt = $this->flowBighub->getCountryMarketplace($result['code']);
        $valor_adicional_portes = 0;

        // Normalizar o nome do marketplace
        $channel = $this->normalizeMarketplaceName($marketplace);

        // Extrair EAN de acordo com o marketplace
        $ean = $this->flowBighub->getEanCode($order->sku[0], $order, $channel);

        // Log para debug
        #var_dump("entrou {$channel} - order: {$order_id}");

        // Verifica se é um pedido com múltiplos produtos
        if ($order->product_quantity > 1) {

            // Lógica para múltiplos produtos
            $products = $order->response->order_lines;
            #$origin = $order->code_marketplace->code;

            foreach ($products as $prod) {

                // Calcula preço
                #$price = round($this->aplicarImposto($prod->total_price, $origin) - $valor_adicional_portes, 2);
                $price = $prod->price;

                // Ajuste para quantidade
                if ($prod->quantity > 1) {
                    $price = ($prod->price / $prod->quantity) - $valor_adicional_portes;
                }

                // Busca vendedor
                $price = number_format($price, 2, '.', '');
                if (!$this->user_id_debug) {
                    $found_seller = $this->getSeller($ean, $channel, $price, $order_id, $tblId);

                    $users_id = [];
                    if (isset($found_seller) && $this->isUserActive($found_seller)) {
                        $users_id[] = $found_seller->user_id;
                    }
                }
            }

            // Verifica se todos os produtos são do mesmo vendedor
            if (!$this->user_id_debug) {
                $user_id = $this->getSingleSellerId($users_id);
            } else {
                $user_id = $this->user_id_debug;
            }
        } else {
            #var_dump("{$channel} = 1 produto");

            // Lógica para produto único
            $price = $order->price - $valor_adicional_portes;


            // Busca vendedor
            if (!$this->user_id_debug) {
                $found_seller = $this->getSeller($ean, $channel, $price, $order_id, $tblId);
            }

            if ($this->debug && !$this->user_id_debug) {
                #echo json_encode($found_seller);
                $user_id = isset($found_seller) ? $found_seller->user_id : null;
            } else {
                $user_id = $this->user_id_debug;
            }
        }


        // Finaliza o envio da order para o seller
        if ($user_id && !in_array($user_id, [999999])) {

            $payload = $this->flowFnac->generatePayloadFnac($channel, $order, $user_id, $country_mkt, $found_seller ?? null);
            if ($this->debug && !$this->order_bySupport) {
                echo json_encode($payload);
                #die();
            }

            $insertResult = $this->flowBighub->insert_tbl_order($payload);
            if ($this->debug && !$this->order_bySupport) {
                echo json_encode($insertResult);
            }

            if ($insertResult->status == 200) {
                \Log::info("Order: " . $order_id . " inserida na tbl_order");

                $this->flowBighub->curl_put_update_order_user($tblId, $user_id);
                \Log::info("Repassando a order " . $order_id . " para o user_id: " . $user_id);

                $this->flowBighub->createSendEmail($user_id, $order_id, $channel . '-' . $country_mkt);
                \Log::info("Notificando o seller sobre uma venda, order: " . $order_id . ", email enviado para o user_id: " . $user_id);

                return true;
            } else {
                \Log::info("Error ao inserir order na tbl_orders - " . $insertResult);
                echo 'Error ao inserir na tbl_orders';
                return  null;
            }
        } else {
            echo 'Não encontrou seller';
            return null;
        }

        return false;
    }


}
<?php

namespace App\Services;

use App\Models\UserProduct;

class OrderFlowService
{
    /**
     * Obter o código EAN baseado no SKU e outras informações
     *
     * @param string $sku SKU do produto
     * @param object $order Objeto do pedido
     * @param string $marketplace Nome do marketplace
     * @return string Código EAN
     */
    public function getEanCode($sku, $order, $marketplace)
    {
        $ean = substr($sku, 6);

        if (isset($order->marketplace->account) && $order->marketplace->account == 2) {
            if ($marketplace == 'carrefour') {
                $ean = substr($sku, 10);
            } else {
                $ean = substr($sku, 7);
            }
        }

        return $ean;
    }

    /**
     * Obter a imagem do produto pelo código EAN
     *
     * @param string $ean Código EAN do produto
     * @param int $user_id ID do usuário
     * @return string URL da imagem
     */
    public function getImgProduct($ean, $user_id)
    {
        $img = 'https://cdn.bighub.store/image/product-placeholder.png';

        $product = UserProduct::where('user_id', $user_id)
                            ->where('barCode', $ean)
                            ->first();

        if ($product && !empty($product->images)) {
            $img = $product->images;
        }

        return $img;
    }


    /**
     * Calcula a porcentagem a ser cobrada com base nos valores
     *
     * @param float|string $valor_total Valor total
     * @param float|string $valor_comission Valor da comissão
     * @return float Porcentagem a ser cobrada
     */
    public function porcentagemaCobrar($valor_total, $valor_comission)
    {
        $valor_total = is_string($valor_total) && is_numeric($valor_total) ? floatval($valor_total) : $valor_total;
        $valor_comission = is_string($valor_comission) && is_numeric($valor_comission) ? floatval($valor_comission) : $valor_comission;

        if ($valor_total == 0) {
            return 0;
        }

        $porcentagem = round(($valor_comission / $valor_total) * 100, 2);

        if ($porcentagem <= 13) {
            return 15;
        } elseif ($porcentagem <= 15) {
            return 17;
        } elseif ($porcentagem <= 17) {
            return 19;
        } elseif ($porcentagem <= 19) {
            return 21;
        } elseif ($porcentagem <= 23) {
            return 25;
        } else {
            return 20;
        }
    }

    /**
     * Calcula a porcentagem proporcional entre comissão e preço
     *
     * @param float|string $totalCommission Valor total da comissão
     * @param float|string $totalPrice Valor total do preço
     * @return float Porcentagem calculada
     */
    public function calcularPorcentagemProporcional($totalCommission, $totalPrice)
    {
        if (is_string($totalCommission) && is_numeric($totalCommission)) {
            $totalCommission = floatval($totalCommission);
        }

        if (is_string($totalPrice) && is_numeric($totalPrice)) {
            $totalPrice = floatval($totalPrice);
        }

        if ($totalPrice == 0) {
            return 0;
        }

        $porcentagem = ($totalCommission / $totalPrice) * 100;

        return $porcentagem;
    }

    /**
     * Compara as comissões do BigHub e do Marketplace
     *
     * @param array $payload Dados do pedido
     * @return bool Resultado da comparação
     */
    public function comparaComissoes($payload)
    {
        if (isset($payload['finance']['total_commission_bighub']) && isset($payload['finance']['total_commission_marketplace'])) {
            $bighubCommission = $payload['finance']['total_commission_bighub'];
            $marketplaceCommission = $payload['finance']['total_commission_marketplace'];

            if ($bighubCommission > $marketplaceCommission || $bighubCommission == $marketplaceCommission) {
                return true;
            } else {
                echo "O valor da comissão BigHub $bighubCommission não é maior nem igual ao o valor da comissão do Marketplace $marketplaceCommission";
                die();
                return false;
            }
        } else {
            echo "Os campos necessários não existem no payload.";
            return false;
        }
    }

    /**
     * Identifica o código do país a partir do nome ou texto
     *
     * @param string $country Nome ou texto do país
     * @return string|null Código do país em duas letras
     */
    public function getCountry($country)
    {
        $customer_country = null;

        $countries_FR = [
            'France (métropolitaine)',
            'FR',
            'FRA',
            'FRANCE',
            'france',
            'France',
            'FRANÇA',
            'frança',
            'França',
            'FRANZÖSISCH',
            'französisch',
            'Französisch',
            'FRANKREICH',
            'frankreich',
            'Frankreich',
            'PARIS',
            'paris',
            'Paris',
            'MARSEILLE',
            'marseille',
            'Marseille',
            'LYON',
            'lyon',
            'Lyon',
            'TOULOUSE',
            'toulouse',
            'Toulouse',
            'NICE',
            'nice',
            'Nice',
            'BORDEAUX',
            'bordeaux',
            'Bordeaux',
            'LILLE',
            'lille',
            'Lille',
            'NANTES',
            'nantes',
            'Nantes',
            'STRASBOURG',
            'strasbourg',
            'Strasbourg'
        ];

        $countries_AT = [
            'AT',
            'AUT',
            'AUSTRIA',
            'austria',
            'Austria',
            'ÁUSTRIA',
            'áustria',
            'Áustria',
            'ÖSTERREICH',
            'österreich',
            'Österreich',
            'VIENNA',
            'vienna',
            'Vienna',
            'WIEN',
            'wien',
            'Wien',
            'SALZBURG',
            'salzburg',
            'Salzburg',
            'INNSBRUCK',
            'innsbruck',
            'Innsbruck',
            'GRAZ',
            'graz',
            'Graz',
            'LINZ',
            'linz',
            'Linz',
            'KLAGENFURT',
            'klagenfurt',
            'Klagenfurt'
        ];

        $countries_DE = [
            'DE',
            'DEU',
            'GERMANY',
            'germany',
            'Germany',
            'ALEMANHA',
            'alemanha',
            'Alemanha',
            'DEUTSCHLAND',
            'deutschland',
            'Deutschland',
            'BERLIN',
            'berlin',
            'Berlin',
            'FRANKFURT',
            'frankfurt',
            'Frankfurt',
            'HAMBURG',
            'hamburg',
            'Hamburg',
            'MUNICH',
            'munich',
            'Munich',
            'MÜNCHEN',
            'münchen',
            'München',
            'STUTTGART',
            'stuttgart',
            'Stuttgart',
            'COLOGNE',
            'cologne',
            'Cologne',
            'KÖLN',
            'köln',
            'Köln'
        ];

        $countries_ES = [
            'ES',
            'ESP',
            'SPAIN',
            'spain',
            'Spain',
            'ESPAÑA',
            'españa',
            'Espana',
            'España',
            'ESPANHA',
            'espanha',
            'Espanha',
            'SPANISCH',
            'spanisch',
            'Spanisch',
            'SPANIEN',
            'spanien',
            'Spanien',
            'MADRID',
            'madrid',
            'Madrid',
            'BARCELONA',
            'barcelona',
            'Barcelona',
            'VALENCIA',
            'valencia',
            'Valencia',
            'SEVILLA',
            'sevilla',
            'Sevilla',
            'ZARAGOZA',
            'zaragoza',
            'Zaragoza',
            'MÁLAGA',
            'málaga',
            'Málaga',
            'BILBAO',
            'bilbao',
            'Bilbao',
            'GRANADA',
            'granada',
            'Granada'
        ];

        $countries_PT = [
            'PT',
            'PRT',
            'PORTUGAL',
            'portugal',
            'Portugal',
            'PORTUGUÊS',
            'português',
            'Português',
            'PORTUGIESISCH',
            'portugiesisch',
            'Portugiesisch',
            'LISBON',
            'lisbon',
            'Lisbon',
            'LISBOA',
            'lisboa',
            'Lisboa',
            'PORTO',
            'porto',
            'Porto',
            'COIMBRA',
            'coimbra',
            'Coimbra',
            'FARO',
            'faro',
            'Faro',
            'BRAGA',
            'braga',
            'Braga',
            'AVEIRO',
            'aveiro',
            'Aveiro',
            'GUIMARÃES',
            'guimarães',
            'Guimarães',
            'MADEIRA',
            'madeira',
            'Madeira',
            'AÇORES',
            'açores',
            'Açores'
        ];

        $countries_IT = [
            'IT',
            'ITA',
            'ITALY',
            'italy',
            'Italy',
            'ITALIA',
            'italia',
            'Italia',
            'ITALIANO',
            'italiano',
            'Italiano',
            'ITALIEN',
            'italien',
            'Italien',
            'ROMA',
            'roma',
            'Roma',
            'ROME',
            'rome',
            'Rome',
            'MILANO',
            'milano',
            'Milano',
            'MILAN',
            'milan',
            'Milan',
            'NAPOLI',
            'napoli',
            'Napoli',
            'NAPLES',
            'naples',
            'Naples',
            'TORINO',
            'torino',
            'Torino',
            'TURIN',
            'turin',
            'Turin',
            'FIRENZE',
            'firenze',
            'Firenze',
            'FLORENCE',
            'florence',
            'Florence',
            'VENEZIA',
            'venezia',
            'Venezia',
            'VENICE',
            'venice',
            'Venice',
            'BOLOGNA',
            'bologna',
            'Bologna',
            'GENOVA',
            'genova',
            'Genova',
            'GENOA',
            'genoa',
            'Genoa',
            'PALERMO',
            'palermo',
            'Palermo'
        ];

        $countries_SI = [
            'Slovenija',
            'SI',
            'SVN', // Códigos de país
            'SLOVENIA',
            'slovenia',
            'Slovenia', // Inglês
            'ESLOVÉNIA',
            'eslovénia',
            'Eslovénia', // Português (pt-PT)
            'ESLOVÊNIA',
            'eslovênia',
            'Eslovênia', // Português (pt-BR)
            'SLOWENIEN',
            'slowenien',
            'Slowenien', // Alemão
            'SLOVENSKO',
            'slovensko',
            'Slovensko', // Esloveno
            'SLOVENEC',
            'slovenec',
            'Slovenec', // Gentílico masculino (esloveno)
            'SLOVENKA',
            'slovenka',
            'Slovenka', // Gentílico feminino (esloveno)
            'LJUBLJANA',
            'ljubljana',
            'Ljubljana', // Capital
            'MARIBOR',
            'maribor',
            'Maribor',
            'CELJE',
            'celje',
            'Celje',
            'KOPER',
            'koper',
            'Koper',
            'KRANJ',
            'kranj',
            'Kranj',
            'NOVO MESTO',
            'novo mesto',
            'Novo Mesto',
            'PTUJ',
            'ptuj',
            'Ptuj',
            'PIRAN',
            'piran',
            'Piran',
            'BLED',
            'bled',
            'Bled'
        ];

        $countries_BE = [
            'BE',
            'BEL',
            'BELGIUM',
            'belgium',
            'Belgium',
            'BELGIO',
            'belgio',
            'Belgio',
            'BELGIQUE',
            'belgique',
            'Belgique',
            'BELGIEN',
            'belgien',
            'Belgien',
            'BRUXELLES',
            'bruxelles',
            'Bruxelles',
            'BRUSSELS',
            'brussels',
            'Brussels',
            'BRÜSSEL',
            'brüssel',
            'Brüssel',
            'ANTWERP',
            'antwerp',
            'Antwerp',
            'ANVERS',
            'anvers',
            'Anvers',
            'GENT',
            'gent',
            'Gent',
            'GHENT',
            'ghent',
            'Ghent',
            'LIEGE',
            'liege',
            'Liège',
            'LIÈGE',
            'liège',
            'CHARLEROI',
            'charleroi',
            'Charleroi',
            'NAMUR',
            'namur',
            'Namur',
            'MONS',
            'mons',
            'Mons',
            'LOUVAIN',
            'louvain',
            'Louvain',
            'LEUVEN',
            'leuven',
            'Leuven',
            'BRUGGE',
            'brugge',
            'Brugge',
            'BRUGES',
            'bruges',
            'Bruges',
        ];



        $countryInput = isset($country)
            ? trim(strtolower($country))
            : '';

        if (!empty($countryInput)) {
            if (in_array($countryInput, array_map('strtolower', $countries_FR), true)) {
                $customer_country = 'FR';
            } elseif (in_array($countryInput, array_map('strtolower', $countries_AT), true)) {
                $customer_country = 'AT';
            } elseif (in_array($countryInput, array_map('strtolower', $countries_DE), true)) {
                $customer_country = 'DE';
            } elseif (in_array($countryInput, array_map('strtolower', $countries_ES), true)) {
                $customer_country = 'ES';
            } elseif (in_array($countryInput, array_map('strtolower', $countries_IT), true)) {
                $customer_country = 'IT';
            } elseif (in_array($countryInput, array_map('strtolower', $countries_PT), true)) {
                $customer_country = 'PT';
            } elseif (in_array($countryInput, array_map('strtolower', $countries_SI), true)) {
                $customer_country = 'SI';
            } elseif (in_array($countryInput, array_map('strtolower', $countries_BE), true)) {
                $customer_country = 'BE';
            }
            return $customer_country;
        }

        if($customer_country == null){
            return $this->getCountryCodeGPT($country);
        }

        return null;
    }

    /**
     * Utiliza a API do GPT para identificar o código do país
     *
     * @param string $countryName Nome do país
     * @return string Código do país em duas letras
     */
    private function getCountryCodeGPT($countryName)
    {
        $apiKey = '********************************************************************************************************************************************************************';
        $url = 'https://api.openai.com/v1/chat/completions';

        $data = [
            'model' => 'gpt-4',
            'messages' => [
                ['role' => 'system', 'content' => 'Você é um assistente útil.'],
                ['role' => 'user', 'content' => "Tenho o country de um formulário que foi digitado $countryName. Poderia me dizer qual é esse country em duas letras? Não quero esse tipo de resposta: Acredito que houve um erro de digitação. O correto seria France', cujo código de duas letras é 'FR'. Quero apenas o FR como resposta nesse caso."],
            ],
            'temperature' => 0.2,
        ];

        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $apiKey,
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

        $response = curl_exec($ch);
        curl_close($ch);

        $decodedResponse = json_decode($response, true);
        return $decodedResponse['choices'][0]['message']['content'] ?? 'Erro ao obter código do país';
    }


}

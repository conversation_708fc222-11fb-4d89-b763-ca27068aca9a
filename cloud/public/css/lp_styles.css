* {
    margin: 0px;
    padding: 0px;
    box-sizing: border-box;
}

body {
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: rgb(51, 51, 51);
    background-color: rgb(248, 250, 252);
}

.container {
    max-width: 1200px;
    margin: 0px auto;
    padding: 0px 2rem;
}

.top-bar {
    background: linear-gradient(to right, var(--dark-color), var(--dark-color));
    color: var(--main-color);
    padding: 0.6rem 0px;
}

.top-bar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.top-contact span {
    margin-right: 1.5rem;
    font-size: 1.2rem;
}

.header {
    background-color: white;
    padding: 3rem 0px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 6px -1px;
}

.profile-container {
    max-width: 1200px;
    margin: 0px auto;
    padding: 0px 2rem;
    display: flex;
    align-items: center;
    gap: 2.2rem;
}

.profile-image {
    position: relative;
    flex-shrink: 0;
}

.profile-image img {
    width: 180px;
    height: 180px;
    border-radius: 50%;
    object-fit: cover;
    border: 5px solid var(--main-color);
    box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 6px;
}

.profile-badge {
    position: absolute;
    bottom: 0px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--main-color);
    color: white;
    padding: 0.3rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    white-space: nowrap;
    font-weight: 600;
}

.profile-info h1 {
    color: var(--dark-color);
    font-size: 1.6rem;
    font-weight: 700;
}

.subtitle {
    color: rgb(74, 85, 104);
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

main {
    max-width: 1200px;
    margin: 0px auto;
    padding: 0px 2rem;
}

.section-title {
    text-align: center;
    color: var(--dark-color);
    font-size: 1.7rem;
    margin: 3rem 0px;
    position: relative;
    padding-bottom: 1rem;
}

.section-title::after {
    content: "";
    position: absolute;
    bottom: 0px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(to right, var(--main-color), var(--main-color));
    border-radius: 2px;
}

.properties-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2.5rem;
    margin: 2rem 0px;
}

.property-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 6px;
    transition: all 0.3s ease 0s;
}

.property-card:hover {
    transform: translateY(-10px);
    box-shadow: rgba(0, 0, 0, 0.15) 0px 8px 12px;
}

.property-image {
    position: relative;
    width: 100%;
}

.property-image img {
    width: 100%;
    height: 204px;
    object-fit: cover;
    display: block;
}

.property-badge {
    position: absolute;
    top: 0rem;
    right: 0.7rem;
    background: var(--main-color);
    color: rgb(255 255 255);
    padding: 0.2rem 1rem;
    border-radius: 0px 0px 6px 6px;
    font-size: 0.9rem;
    font-weight: 600;
}

.property-info {
    padding: 1.8rem;
}

.property-info h3 {
    color: var(--dark-color);
    font-size: 1.5rem;
}

.location {
    color: rgb(113, 128, 150);
}

.property-features {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    color: rgb(74, 85, 104);
}

.property-features span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.price {
    color: var(--dark-color);
    font-size: 1.3rem;
    font-weight: bold;
    margin-bottom: 1.5rem;
}

.cta-button {
    background: linear-gradient(to right, var(--dark-color), var(--dark-color));
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    width: 100%;
    transition: transform 0.3s ease 0s;
}

.cta-button:hover {
    transform: scale(1.02);
}

.footer {
    background-color: var(--main-color);
    color: white;
    padding: 4rem 0px 0px;
    margin-top: 4rem;
}

.footer-content {
    max-width: 1200px;
    margin: 0px auto;
    padding: 0px 2rem;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2.5rem;
}

.footer-info h3, .footer-social h3 {
    font-size: 1.5rem;
    margin-bottom: 1.0rem;
    color: rgb(247, 250, 252);
}

.footer-info p {
    margin-bottom: 0.5rem;
    color: rgb(255, 255, 255);
}

.footer-social .social-links {
    display: flex;
    gap: 1.5rem;
}

.social-icon {
    color: rgb(0 0 0);
    font-size: 2.5rem;
    transition: color 0.3s ease 0s;
}

.social-icon:hover {
    color: #ffffff;
}

.footer-bottom {
    margin-top: 3rem;
    padding: 1.5rem 0px;
    text-align: center;
    background-color: var(--dark-color);
}

.copyright {
    color: rgb(203, 213, 224);
    font-size: 0.9rem;
}

.language-selector {
    position: absolute;
    top: 10px;
    right: 20px;
}

.language-selector img {
    width: 24px;
    height: 16px;
    margin: 0 5px;
    cursor: pointer;
}

@media (max-width: 768px) {
    .container, .profile-container, main, .footer-content {
        padding: 0px 1rem;
    }

    .top-bar .container {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .profile-container {
        flex-direction: column;
        text-align: center;
    }

    .properties-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-social .social-links {
        justify-content: center;
    }
}

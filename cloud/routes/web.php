<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\OrderController;
use Illuminate\Http\Middleware\HandleCors;


Route::get('/', function () {
    return view('welcome');
});


Route::get('/set-order-user/{order_id?}/{user_id?}', [OrderController::class, 'processUnassignedOrders']);

Route::get('/set-user-to-user', [OrderController::class, 'processUnassignedOrders']);

#Route::post('/support-assign-orders', [OrderController::class, 'processOrdersRequestedBySupport']);

Route::post('/support-assign-orders', [OrderController::class, 'processOrdersRequestedBySupport'])
    ->middleware(HandleCors::class);


Route::get('/csrf-token', function () {
    return response()->json(['token' => csrf_token()]);
});

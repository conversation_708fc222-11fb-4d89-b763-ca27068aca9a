// Mock data for simulations and results tables
// This simulates data that would come from a backend API

// Simulation data
const simulationsData = [
    {
        id: 1,
        date: "2025-03-20 14:30:45",
        asset: "USD/CAD OTC",
        entryValue: 100,
        returnValue: 86,
        result: "green",
        gales: 0,
    },
    {
        id: 2,
        date: "2025-03-20 15:15:22",
        asset: "USD/CAD OTC",
        entryValue: 150,
        returnValue: -150,
        result: "red",
        gales: 1,
    },
    {
        id: 3,
        date: "2025-03-20 16:05:10",
        asset: "USD/CAD OTC",
        entryValue: 200,
        returnValue: 172,
        result: "green",
        gales: 2,
    },
    {
        id: 4,
        date: "2025-03-20 16:30:45",
        asset: "USD/CAD OTC",
        entryValue: 120,
        returnValue: 103,
        result: "green",
        gales: 0,
    },
    {
        id: 5,
        date: "2025-03-20 16:45:22",
        asset: "USD/CAD OTC",
        entryValue: 180,
        returnValue: -180,
        result: "red",
        gales: 1,
    },
    {
        id: 6,
        date: "2025-03-20 17:05:10",
        asset: "USD/CAD OTC",
        entryValue: 250,
        returnValue: 215,
        result: "green",
        gales: 0,
    },
    {
        id: 7,
        date: "2025-03-20 17:30:45",
        asset: "USD/CAD OTC",
        entryValue: 130,
        returnValue: -130,
        result: "red",
        gales: 0,
    },
    {
        id: 8,
        date: "2025-03-20 17:45:22",
        asset: "USD/CAD OTC",
        entryValue: 160,
        returnValue: 138,
        result: "green",
        gales: 1,
    },
    {
        id: 9,
        date: "2025-03-20 18:05:10",
        asset: "USD/CAD OTC",
        entryValue: 220,
        returnValue: 189,
        result: "green",
        gales: 0,
    },
    {
        id: 10,
        date: "2025-03-20 18:30:45",
        asset: "USD/CAD OTC",
        entryValue: 140,
        returnValue: -140,
        result: "red",
        gales: 2,
    },
    {
        id: 11,
        date: "2025-03-20 18:45:22",
        asset: "USD/CAD OTC",
        entryValue: 170,
        returnValue: 146,
        result: "green",
        gales: 0,
    },
    {
        id: 12,
        date: "2025-03-20 19:05:10",
        asset: "USD/CAD OTC",
        entryValue: 190,
        returnValue: -190,
        result: "red",
        gales: 1,
    },
];

// Results data
const resultsData = [
    {
        id: 1,
        date: "2025-03-20 14:30:45",
        asset: "USD/CAD OTC",
        entryValue: 100,
        returnValue: 86,
        result: "green",
        gales: 0,
    },
    {
        id: 2,
        date: "2025-03-20 15:15:22",
        asset: "USD/CAD OTC",
        entryValue: 150,
        returnValue: -150,
        result: "red",
        gales: 1,
    },
    {
        id: 3,
        date: "2025-03-20 16:05:10",
        asset: "USD/CAD OTC",
        entryValue: 200,
        returnValue: 172,
        result: "green",
        gales: 2,
    },
    {
        id: 4,
        date: "2025-03-20 17:22:33",
        asset: "USD/CAD OTC",
        entryValue: 100,
        returnValue: 86,
        result: "green",
        gales: 0,
    },
    {
        id: 5,
        date: "2025-03-20 18:10:05",
        asset: "USD/CAD OTC",
        entryValue: 250,
        returnValue: -250,
        result: "red",
        gales: 0,
    },
    {
        id: 6,
        date: "2025-03-20 18:45:30",
        asset: "USD/CAD OTC",
        entryValue: 180,
        returnValue: 155,
        result: "green",
        gales: 1,
    },
    {
        id: 7,
        date: "2025-03-20 19:20:15",
        asset: "USD/CAD OTC",
        entryValue: 120,
        returnValue: -120,
        result: "red",
        gales: 0,
    },
    {
        id: 8,
        date: "2025-03-20 19:55:40",
        asset: "USD/CAD OTC",
        entryValue: 300,
        returnValue: 258,
        result: "green",
        gales: 0,
    },
    {
        id: 9,
        date: "2025-03-20 20:30:22",
        asset: "USD/CAD OTC",
        entryValue: 220,
        returnValue: -220,
        result: "red",
        gales: 2,
    },
    {
        id: 10,
        date: "2025-03-20 21:05:18",
        asset: "USD/CAD OTC",
        entryValue: 150,
        returnValue: 129,
        result: "green",
        gales: 0,
    },
    {
        id: 11,
        date: "2025-03-20 21:40:33",
        asset: "USD/CAD OTC",
        entryValue: 200,
        returnValue: -200,
        result: "red",
        gales: 1,
    },
    {
        id: 12,
        date: "2025-03-20 22:15:45",
        asset: "USD/CAD OTC",
        entryValue: 170,
        returnValue: 146,
        result: "green",
        gales: 0,
    },
    {
        id: 13,
        date: "2025-03-20 22:50:12",
        asset: "USD/CAD OTC",
        entryValue: 230,
        returnValue: 198,
        result: "green",
        gales: 0,
    },
    {
        id: 14,
        date: "2025-03-20 23:25:30",
        asset: "USD/CAD OTC",
        entryValue: 190,
        returnValue: -190,
        result: "red",
        gales: 2,
    },
    {
        id: 15,
        date: "2025-03-21 00:00:55",
        asset: "USD/CAD OTC",
        entryValue: 160,
        returnValue: 138,
        result: "green",
        gales: 0,
    },
];

// Function to simulate fetching data from backend
function fetchSimulationsData() {
    return new Promise((resolve) => {
        // Simulate network delay
        setTimeout(() => {
            resolve(simulationsData);
        }, 300);
    });
}

function fetchResultsData() {
    return new Promise((resolve) => {
        // Simulate network delay
        setTimeout(() => {
            resolve(resultsData);
        }, 300);
    });
}

// Function to simulate fetching account balance
function fetchAccountBalance() {
    return new Promise((resolve) => {
        // Simulate network delay
        setTimeout(() => {
            resolve(2345.56);
        }, 500);
    });
}

// Function to simulate real-time updates by adding new data
function simulateNewTrade() {
    const isGreen = Math.random() > 0.5;
    const entryValue = Math.floor(Math.random() * 300) + 100;
    const gales = Math.floor(Math.random() * 3);
    const returnValue = isGreen ? Math.floor(entryValue * 0.86) : -entryValue;
    
    const now = new Date();
    const formattedDate = now.toISOString().replace('T', ' ').substring(0, 19);
    
    const newTrade = {
        id: resultsData.length + 1,
        date: formattedDate,
        asset: "USD/CAD OTC",
        entryValue: entryValue,
        returnValue: returnValue,
        result: isGreen ? "green" : "red",
        gales: gales
    };
    
    // Add to both datasets
    resultsData.unshift(newTrade);
    simulationsData.unshift(newTrade);
    
    // Keep arrays at a reasonable size
    if (resultsData.length > 50) resultsData.pop();
    if (simulationsData.length > 30) simulationsData.pop();
    
    return newTrade;
}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/lucide-icons@0.284.0/dist/umd/lucide.min.css">
</head>
<body class="bg-black text-white">
    <div class="container">
        <h1 class="dashboard-title">Trading Dashboard</h1>
        
        <!-- Top Cards -->
        <div class="cards-container">
            <!-- Portugal Time Card -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i data-lucide="clock" class="icon-small"></i>
                        Portugal Time
                    </h3>
                </div>
                <div class="card-content">
                    <p class="time-display" id="portugal-time">00:00:00</p>
                </div>
            </div>
            
            <!-- Brazil Time Card -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i data-lucide="clock" class="icon-small"></i>
                        Brazil Time
                    </h3>
                </div>
                <div class="card-content">
                    <p class="time-display" id="brazil-time">00:00:00</p>
                </div>
            </div>
            
            <!-- Account Balance Card -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i data-lucide="wallet" class="icon-small"></i>
                        Account Balance
                    </h3>
                </div>
                <div class="card-content">
                    <p class="balance-display" id="account-balance">R$ 0.00</p>
                </div>
            </div>
            
            <!-- Payout Card -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i data-lucide="percent" class="icon-small"></i>
                        Payout
                    </h3>
                </div>
                <div class="card-content">
                    <div class="payout-input">
                        <input type="number" id="payout-input" value="86" min="1" max="100" class="input">
                        <span class="payout-symbol">%</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Robot Simulations Table -->
            <div class="card table-card">
                <div class="card-header">
                    <h3 class="card-title">Robot Simulations</h3>
                    <p class="card-description">Simulated trading operations by the robot</p>
                </div>
                <div class="card-content flex-column">
                    <div class="table-container">
                        <table id="simulations-table">
                            <thead>
                                <tr>
                                    <th>Date & Time</th>
                                    <th>Asset</th>
                                    <th>Return/Loss</th>
                                    <th>Result</th>
                                    <th>Gales</th>
                                </tr>
                            </thead>
                            <tbody id="simulations-body">
                                <!-- Table rows will be inserted here by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                    <div class="pagination-container" id="simulations-pagination">
                        <div class="rows-per-page">
                            <span>Rows per page:</span>
                            <select id="simulations-rows-select">
                                <option value="5">5</option>
                                <option value="6" selected>6</option>
                                <option value="10">10</option>
                            </select>
                        </div>
                        <div class="pagination-controls">
                            <button class="pagination-button" id="simulations-prev-btn" disabled>
                                <i data-lucide="chevron-left" class="icon-tiny"></i>
                            </button>
                            <span class="pagination-info" id="simulations-page-info">1 / 1</span>
                            <button class="pagination-button" id="simulations-next-btn" disabled>
                                <i data-lucide="chevron-right" class="icon-tiny"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Trading Operation Card -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Trading Operation</h3>
                    <p class="card-description">Configure your trading parameters</p>
                </div>
                <div class="card-content">
                    <div class="form-group">
                        <label for="asset-display">Asset</label>
                        <div class="asset-display" id="asset-display">USD/CAD OTC</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="entry-value">Entry Value</label>
                        <div class="input-with-prefix">
                            <span class="input-prefix">R$</span>
                            <input type="number" id="entry-value" placeholder="0.00" class="input">
                        </div>
                    </div>
                    
                    <div class="potential-return">
                        <span class="return-label">Potential Return:</span>
                        <span class="return-value" id="potential-return">R$ 0.00</span>
                    </div>
                    
                    <div class="robot-toggle">
                        <label class="switch">
                            <input type="checkbox" id="robot-mode">
                            <span class="slider"></span>
                        </label>
                        <span class="toggle-label">
                            <i data-lucide="bot" class="icon-small"></i>
                            Enable Robot Mode
                        </span>
                    </div>
                </div>
                <div class="card-footer">
                    <button class="button button-outline" id="reset-button">Reset</button>
                    <button class="button button-primary" id="execute-button">
                        <i data-lucide="trending-up" class="icon-small"></i>
                        Execute Trade
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Trading Results Table -->
        <div class="card table-card full-width">
            <div class="card-header">
                <h3 class="card-title">Trading Results</h3>
                <p class="card-description">History of your recent trading operations</p>
            </div>
            <div class="card-content flex-column">
                <div class="table-container">
                    <table id="results-table">
                        <thead>
                            <tr>
                                <th>Date & Time</th>
                                <th>Asset</th>
                                <th>Return/Loss</th>
                                <th>Result</th>
                                <th>Gales</th>
                            </tr>
                        </thead>
                        <tbody id="results-body">
                            <!-- Table rows will be inserted here by JavaScript -->
                        </tbody>
                    </table>
                </div>
                <div class="pagination-container" id="results-pagination">
                    <div class="rows-per-page">
                        <span>Rows per page:</span>
                        <select id="results-rows-select">
                            <option value="5">5</option>
                            <option value="6">6</option>
                            <option value="10" selected>10</option>
                        </select>
                    </div>
                    <div class="pagination-controls">
                        <button class="pagination-button" id="results-prev-btn" disabled>
                            <i data-lucide="chevron-left" class="icon-tiny"></i>
                        </button>
                        <span class="pagination-info" id="results-page-info">1 / 1</span>
                        <button class="pagination-button" id="results-next-btn" disabled>
                            <i data-lucide="chevron-right" class="icon-tiny"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/lucide@latest"></script>
    <script src="data.js"></script>
    <script src="script.js"></script>
</body>
</html>
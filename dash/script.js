document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    const lucide = window.lucide || {};
    lucide.createIcons();
    
    // Backend API URL
    const API_BASE_URL = 'https://sua-api.com/api';
    
    // DOM Elements
    const portugalTimeEl = document.getElementById('portugal-time');
    const brazilTimeEl = document.getElementById('brazil-time');
    const accountBalanceEl = document.getElementById('account-balance');
    const payoutInputEl = document.getElementById('payout-input');
    const entryValueEl = document.getElementById('entry-value');
    const potentialReturnEl = document.getElementById('potential-return');
    const robotModeEl = document.getElementById('robot-mode');
    const resetButtonEl = document.getElementById('reset-button');
    const executeButtonEl = document.getElementById('execute-button');
    
    // Simulations table elements
    const simulationsBodyEl = document.getElementById('simulations-body');
    const simulationsRowsSelectEl = document.getElementById('simulations-rows-select');
    const simulationsPrevBtnEl = document.getElementById('simulations-prev-btn');
    const simulationsNextBtnEl = document.getElementById('simulations-next-btn');
    const simulationsPageInfoEl = document.getElementById('simulations-page-info');
    
    // Results table elements
    const resultsBodyEl = document.getElementById('results-body');
    const resultsRowsSelectEl = document.getElementById('results-rows-select');
    const resultsPrevBtnEl = document.getElementById('results-prev-btn');
    const resultsNextBtnEl = document.getElementById('results-next-btn');
    const resultsPageInfoEl = document.getElementById('results-page-info');
    
    // Pagination state
    let simulationsData = [];
    let resultsData = [];
    let simulationsPage = 1;
    let resultsPage = 1;
    let simulationsRowsPerPage = 6; // Default to 6 rows for simulations
    let resultsRowsPerPage = 10; // Default to 10 rows for results
    
    // Função para buscar dados de simulações do backend
    async function fetchSimulationsData() {
        try {
            // Em um ambiente real, você faria uma chamada fetch aqui
            // const response = await fetch(`${API_BASE_URL}/simulations`);
            // const data = await response.json();
            // return data;
            
            // Por enquanto, retornamos dados simulados
            return simulateSimulationsData();
        } catch (error) {
            console.error('Erro ao buscar simulações:', error);
            return simulateSimulationsData();
        }
    }
    
    // Função para buscar dados de resultados do backend
    async function fetchResultsData() {
        try {
            // Em um ambiente real, você faria uma chamada fetch aqui
            // const response = await fetch(`${API_BASE_URL}/results`);
            // const data = await response.json();
            // return data;
            
            // Por enquanto, retornamos dados simulados
            return simulateResultsData();
        } catch (error) {
            console.error('Erro ao buscar resultados:', error);
            return simulateResultsData();
        }
    }
    
    // Função para buscar saldo da conta do backend
    async function fetchAccountBalance() {
        try {
            // Em um ambiente real, você faria uma chamada fetch aqui
            // const response = await fetch(`${API_BASE_URL}/account/balance`);
            // const data = await response.json();
            // return data.balance;
            
            // Por enquanto, retornamos um valor simulado
            return 2345.56;
        } catch (error) {
            console.error('Erro ao buscar saldo da conta:', error);
            return 2345.56;
        }
    }
    
    // Função para executar uma operação de trading
    async function executeTrade(tradeData) {
        try {
            // Em um ambiente real, você faria uma chamada fetch aqui
            // const response = await fetch(`${API_BASE_URL}/trade/execute`, {
            //     method: 'POST',
            //     headers: {
            //         'Content-Type': 'application/json',
            //     },
            //     body: JSON.stringify(tradeData),
            // });
            // const data = await response.json();
            // return data;
            
            // Por enquanto, simulamos uma resposta de sucesso
            console.log('Executando operação:', tradeData);
            return { success: true, tradeId: Math.floor(Math.random() * 1000) };
        } catch (error) {
            console.error('Erro ao executar operação:', error);
            return { success: false, error: error.message };
        }
    }
    
    // Função para buscar atualizações em tempo real
    async function fetchRealTimeUpdates() {
        try {
            // Em um ambiente real, você faria uma chamada fetch aqui
            // const response = await fetch(`${API_BASE_URL}/updates/realtime`);
            // const data = await response.json();
            
            // Por enquanto, simulamos que não há novas operações
            return { newTrades: [], accountBalance: null };
        } catch (error) {
            console.error('Erro ao buscar atualizações em tempo real:', error);
            return { newTrades: [], accountBalance: null };
        }
    }
    
    // Funções de simulação para dados
    function simulateSimulationsData() {
        return [
            {
                id: 1,
                date: "2025-03-20 14:30:45",
                asset: "USD/CAD OTC",
                entryValue: 100,
                returnValue: 86,
                result: "green",
                gales: 0,
            },
            {
                id: 2,
                date: "2025-03-20 15:15:22",
                asset: "USD/CAD OTC",
                entryValue: 150,
                returnValue: -150,
                result: "red",
                gales: 1,
            },
            {
                id: 3,
                date: "2025-03-20 16:05:10",
                asset: "USD/CAD OTC",
                entryValue: 200,
                returnValue: 172,
                result: "green",
                gales: 2,
            },
            {
                id: 4,
                date: "2025-03-20 16:30:45",
                asset: "USD/CAD OTC",
                entryValue: 120,
                returnValue: 103,
                result: "green",
                gales: 0,
            },
            {
                id: 5,
                date: "2025-03-20 16:45:22",
                asset: "USD/CAD OTC",
                entryValue: 180,
                returnValue: -180,
                result: "red",
                gales: 1,
            },
            {
                id: 6,
                date: "2025-03-20 17:05:10",
                asset: "USD/CAD OTC",
                entryValue: 250,
                returnValue: 215,
                result: "green",
                gales: 0,
            },
            {
                id: 7,
                date: "2025-03-20 17:30:45",
                asset: "USD/CAD OTC",
                entryValue: 130,
                returnValue: -130,
                result: "red",
                gales: 0,
            },
            {
                id: 8,
                date: "2025-03-20 17:45:22",
                asset: "USD/CAD OTC",
                entryValue: 160,
                returnValue: 138,
                result: "green",
                gales: 1,
            },
            {
                id: 9,
                date: "2025-03-20 18:05:10",
                asset: "USD/CAD OTC",
                entryValue: 220,
                returnValue: 189,
                result: "green",
                gales: 0,
            },
            {
                id: 10,
                date: "2025-03-20 18:30:45",
                asset: "USD/CAD OTC",
                entryValue: 140,
                returnValue: -140,
                result: "red",
                gales: 2,
            },
            {
                id: 11,
                date: "2025-03-20 18:45:22",
                asset: "USD/CAD OTC",
                entryValue: 170,
                returnValue: 146,
                result: "green",
                gales: 0,
            },
            {
                id: 12,
                date: "2025-03-20 19:05:10",
                asset: "USD/CAD OTC",
                entryValue: 190,
                returnValue: -190,
                result: "red",
                gales: 1,
            },
        ];
    }
    
    function simulateResultsData() {
        return [
            {
                id: 1,
                date: "2025-03-20 14:30:45",
                asset: "USD/CAD OTC",
                entryValue: 100,
                returnValue: 86,
                result: "green",
                gales: 0,
            },
            {
                id: 2,
                date: "2025-03-20 15:15:22",
                asset: "USD/CAD OTC",
                entryValue: 150,
                returnValue: -150,
                result: "red",
                gales: 1,
            },
            {
                id: 3,
                date: "2025-03-20 16:05:10",
                asset: "USD/CAD OTC",
                entryValue: 200,
                returnValue: 172,
                result: "green",
                gales: 2,
            },
            {
                id: 4,
                date: "2025-03-20 17:22:33",
                asset: "USD/CAD OTC",
                entryValue: 100,
                returnValue: 86,
                result: "green",
                gales: 0,
            },
            {
                id: 5,
                date: "2025-03-20 18:10:05",
                asset: "USD/CAD OTC",
                entryValue: 250,
                returnValue: -250,
                result: "red",
                gales: 0,
            },
            {
                id: 6,
                date: "2025-03-20 18:45:30",
                asset: "USD/CAD OTC",
                entryValue: 180,
                returnValue: 155,
                result: "green",
                gales: 1,
            },
            {
                id: 7,
                date: "2025-03-20 19:20:15",
                asset: "USD/CAD OTC",
                entryValue: 120,
                returnValue: -120,
                result: "red",
                gales: 0,
            },
            {
                id: 8,
                date: "2025-03-20 19:55:40",
                asset: "USD/CAD OTC",
                entryValue: 300,
                returnValue: 258,
                result: "green",
                gales: 0,
            },
            {
                id: 9,
                date: "2025-03-20 20:30:22",
                asset: "USD/CAD OTC",
                entryValue: 220,
                returnValue: -220,
                result: "red",
                gales: 2,
            },
            {
                id: 10,
                date: "2025-03-20 21:05:18",
                asset: "USD/CAD OTC",
                entryValue: 150,
                returnValue: 129,
                result: "green",
                gales: 0,
            },
            {
                id: 11,
                date: "2025-03-20 21:40:33",
                asset: "USD/CAD OTC",
                entryValue: 200,
                returnValue: -200,
                result: "red",
                gales: 1,
            },
            {
                id: 12,
                date: "2025-03-20 22:15:45",
                asset: "USD/CAD OTC",
                entryValue: 170,
                returnValue: 146,
                result: "green",
                gales: 0,
            },
            {
                id: 13,
                date: "2025-03-20 22:50:12",
                asset: "USD/CAD OTC",
                entryValue: 230,
                returnValue: 198,
                result: "green",
                gales: 0,
            },
            {
                id: 14,
                date: "2025-03-20 23:25:30",
                asset: "USD/CAD OTC",
                entryValue: 190,
                returnValue: -190,
                result: "red",
                gales: 2,
            },
            {
                id: 15,
                date: "2025-03-21 00:00:55",
                asset: "USD/CAD OTC",
                entryValue: 160,
                returnValue: 138,
                result: "green",
                gales: 0,
            },
            {
                id: 13,
                date: "2025-03-20 22:50:12",
                asset: "USD/CAD OTC",
                entryValue: 230,
                returnValue: 198,
                result: "green",
                gales: 0,
            },
            {
                id: 14,
                date: "2025-03-20 23:25:30",
                asset: "USD/CAD OTC",
                entryValue: 190,
                returnValue: -190,
                result: "red",
                gales: 2,
            },
            {
                id: 15,
                date: "2025-03-21 00:00:55",
                asset: "USD/CAD OTC",
                entryValue: 160,
                returnValue: 138,
                result: "green",
                gales: 0,
            },
            {
                id: 13,
                date: "2025-03-20 22:50:12",
                asset: "USD/CAD OTC",
                entryValue: 230,
                returnValue: 198,
                result: "green",
                gales: 0,
            },
            {
                id: 14,
                date: "2025-03-20 23:25:30",
                asset: "USD/CAD OTC",
                entryValue: 190,
                returnValue: -190,
                result: "red",
                gales: 2,
            },
            {
                id: 15,
                date: "2025-03-21 00:00:55",
                asset: "USD/CAD OTC",
                entryValue: 160,
                returnValue: 138,
                result: "green",
                gales: 0,
            },
            {
                id: 13,
                date: "2025-03-20 22:50:12",
                asset: "USD/CAD OTC",
                entryValue: 230,
                returnValue: 198,
                result: "green",
                gales: 0,
            },
            {
                id: 14,
                date: "2025-03-20 23:25:30",
                asset: "USD/CAD OTC",
                entryValue: 190,
                returnValue: -190,
                result: "red",
                gales: 2,
            },
            {
                id: 15,
                date: "2025-03-21 00:00:55",
                asset: "USD/CAD OTC",
                entryValue: 160,
                returnValue: 138,
                result: "green",
                gales: 0,
            },
            
        ];
    }
    
    // Initialize the dashboard
    async function initDashboard() {
        // Start clock updates
        updateClocks();
        setInterval(updateClocks, 1000);
        
        // Fetch initial data
        await fetchData();
        
        // Set up real-time updates - busca atualizações a cada 10 segundos
        setInterval(async function() {
            await fetchRealTimeUpdates();
        }, 10000);
        
        // Set up event listeners
        setupEventListeners();
    }
    
    // Update the clocks
    function updateClocks() {
        const now = new Date();
        
        // Portugal time (Western European Time / GMT+0/+1)
        const portugalDate = new Date(now.toLocaleString("en-US", { timeZone: "Europe/Lisbon" }));
        portugalTimeEl.textContent = formatTime(portugalDate);
        
        // Brazil time (Brasilia Time / GMT-3)
        const brazilDate = new Date(now.toLocaleString("en-US", { timeZone: "America/Sao_Paulo" }));
        brazilTimeEl.textContent = formatTime(brazilDate);
    }
    
    // Format time as HH:MM:SS
    function formatTime(date) {
        return date.toLocaleTimeString("pt-BR", {
            hour: "2-digit",
            minute: "2-digit",
            second: "2-digit",
            hour12: false,
        });
    }
    
    // Fetch data from the backend
    async function fetchData() {
        try {
            // Fetch simulations data
            simulationsData = await fetchSimulationsData();
            renderSimulationsTable();
            
            // Fetch results data
            resultsData = await fetchResultsData();
            renderResultsTable();
            
            // Fetch account balance
            const balance = await fetchAccountBalance();
            accountBalanceEl.textContent = `R$ ${balance.toFixed(2)}`;
        } catch (error) {
            console.error('Error fetching data:', error);
        }
    }
    
    // FUNÇÃO CORRIGIDA: Render simulations table with pagination
    function renderSimulationsTable() {
        // Limpa a tabela
        simulationsBodyEl.innerHTML = '';
        
        // Verifica se há dados
        if (!simulationsData || simulationsData.length === 0) {
            simulationsBodyEl.innerHTML = '<tr><td colspan="5" class="text-center">Nenhum dado disponível</td></tr>';
            simulationsPageInfoEl.textContent = '0 / 0';
            simulationsPrevBtnEl.disabled = true;
            simulationsNextBtnEl.disabled = true;
            return;
        }
        
        // Calcula a paginação
        const totalPages = Math.max(1, Math.ceil(simulationsData.length / simulationsRowsPerPage));
        
        // Garante que a página atual está dentro dos limites
        if (simulationsPage > totalPages) {
            simulationsPage = totalPages;
        }
        
        const startIndex = (simulationsPage - 1) * simulationsRowsPerPage;
        const endIndex = Math.min(startIndex + simulationsRowsPerPage, simulationsData.length);
        const paginatedData = simulationsData.slice(startIndex, endIndex);
        
        // Atualiza informações de paginação
        simulationsPageInfoEl.textContent = `${simulationsPage} / ${totalPages}`;
        
        // Habilita/desabilita botões de paginação
        simulationsPrevBtnEl.disabled = simulationsPage <= 1;
        simulationsNextBtnEl.disabled = simulationsPage >= totalPages;
        
        // Renderiza as linhas
        paginatedData.forEach(item => {
            const row = document.createElement('tr');
            
            // Date & Time
            const dateCell = document.createElement('td');
            dateCell.textContent = item.date;
            dateCell.className = 'font-medium';
            row.appendChild(dateCell);
            
            // Asset
            const assetCell = document.createElement('td');
            assetCell.textContent = item.asset;
            row.appendChild(assetCell);
            
            // Return/Loss
            const returnCell = document.createElement('td');
            returnCell.textContent = `R$ ${item.returnValue.toFixed(2)}`;
            returnCell.className = item.result === 'green' ? 'text-green' : 'text-red';
            row.appendChild(returnCell);
            
            // Result
            const resultCell = document.createElement('td');
            const badge = document.createElement('span');
            badge.className = item.result === 'green' ? 'badge badge-green' : 'badge badge-red';
            
            const icon = document.createElement('i');
            icon.setAttribute('data-lucide', item.result === 'green' ? 'check' : 'x');
            icon.className = 'icon-tiny';
            
            badge.appendChild(icon);
            badge.appendChild(document.createTextNode(` ${item.result === 'green' ? 'Green' : 'Red'}`));
            resultCell.appendChild(badge);
            row.appendChild(resultCell);
            
            // Gales
            const galesCell = document.createElement('td');
            galesCell.textContent = item.gales;
            row.appendChild(galesCell);
            
            simulationsBodyEl.appendChild(row);
        });
        
        // Re-initialize Lucide icons for the new content
        lucide.createIcons();
    }
    
    // FUNÇÃO CORRIGIDA: Render results table with pagination
    function renderResultsTable() {
        // Limpa a tabela
        resultsBodyEl.innerHTML = '';
        
        // Verifica se há dados
        if (!resultsData || resultsData.length === 0) {
            resultsBodyEl.innerHTML = '<tr><td colspan="5" class="text-center">Nenhum dado disponível</td></tr>';
            resultsPageInfoEl.textContent = '0 / 0';
            resultsPrevBtnEl.disabled = true;
            resultsNextBtnEl.disabled = true;
            return;
        }
        
        // Calcula a paginação
        const totalPages = Math.max(1, Math.ceil(resultsData.length / resultsRowsPerPage));
        
        // Garante que a página atual está dentro dos limites
        if (resultsPage > totalPages) {
            resultsPage = totalPages;
        }
        
        const startIndex = (resultsPage - 1) * resultsRowsPerPage;
        const endIndex = Math.min(startIndex + resultsRowsPerPage, resultsData.length);
        const paginatedData = resultsData.slice(startIndex, endIndex);
        
        // Atualiza informações de paginação
        resultsPageInfoEl.textContent = `${resultsPage} / ${totalPages}`;
        
        // Habilita/desabilita botões de paginação
        resultsPrevBtnEl.disabled = resultsPage <= 1;
        resultsNextBtnEl.disabled = resultsPage >= totalPages;
        
        // Renderiza as linhas
        paginatedData.forEach(item => {
            const row = document.createElement('tr');
            
            // Date & Time
            const dateCell = document.createElement('td');
            dateCell.textContent = item.date;
            dateCell.className = 'font-medium';
            row.appendChild(dateCell);
            
            // Asset
            const assetCell = document.createElement('td');
            assetCell.textContent = item.asset;
            row.appendChild(assetCell);
            
            // Return/Loss
            const returnCell = document.createElement('td');
            returnCell.textContent = `R$ ${item.returnValue.toFixed(2)}`;
            returnCell.className = item.result === 'green' ? 'text-green' : 'text-red';
            row.appendChild(returnCell);
            
            // Result
            const resultCell = document.createElement('td');
            const badge = document.createElement('span');
            badge.className = item.result === 'green' ? 'badge badge-green' : 'badge badge-red';
            
            const icon = document.createElement('i');
            icon.setAttribute('data-lucide', item.result === 'green' ? 'check' : 'x');
            icon.className = 'icon-tiny';
            
            badge.appendChild(icon);
            badge.appendChild(document.createTextNode(` ${item.result === 'green' ? 'Green' : 'Red'}`));
            resultCell.appendChild(badge);
            row.appendChild(resultCell);
            
            // Gales
            const galesCell = document.createElement('td');
            galesCell.textContent = item.gales;
            row.appendChild(galesCell);
            
            resultsBodyEl.appendChild(row);
        });
        
        // Re-initialize Lucide icons for the new content
        lucide.createIcons();
    }
    
    // Set up event listeners
    function setupEventListeners() {
        // Payout and entry value change
        payoutInputEl.addEventListener('input', updatePotentialReturn);
        entryValueEl.addEventListener('input', updatePotentialReturn);
        
        // Reset button
        resetButtonEl.addEventListener('click', function() {
            payoutInputEl.value = '86';
            entryValueEl.value = '';
            robotModeEl.checked = false;
            updatePotentialReturn();
            
            // Focus on entry value
            entryValueEl.focus();
        });
        
        // Execute trade button
        executeButtonEl.addEventListener('click', async function() {
            // Coleta os dados do formulário
            const tradeData = {
                asset: "USD/CAD OTC", // Fixo conforme solicitado
                entryValue: parseFloat(entryValueEl.value) || 0,
                payout: parseFloat(payoutInputEl.value) || 86,
                robotMode: robotModeEl.checked
            };
            
            // Verifica se o valor de entrada é válido
            if (tradeData.entryValue <= 0) {
                alert('Por favor, insira um valor de entrada válido.');
                return;
            }
            
            // Envia a operação para o backend
            const result = await executeTrade(tradeData);
            
            if (result.success) {
                alert('Operação executada com sucesso!');
                
                // Atualiza os dados
                await fetchData();
            } else {
                alert(`Erro ao executar operação: ${result.error || 'Erro desconhecido'}`);
            }
        });
        
        // CORRIGIDO: Simulations pagination
        simulationsRowsSelectEl.addEventListener('change', function() {
            simulationsRowsPerPage = parseInt(this.value);
            simulationsPage = 1; // Reset to first page
            renderSimulationsTable();
        });
        
        simulationsPrevBtnEl.addEventListener('click', function() {
            if (simulationsPage > 1) {
                simulationsPage--;
                renderSimulationsTable();
            }
        });
        
        simulationsNextBtnEl.addEventListener('click', function() {
            const totalPages = Math.ceil(simulationsData.length / simulationsRowsPerPage);
            if (simulationsPage < totalPages) {
                simulationsPage++;
                renderSimulationsTable();
            }
        });
        
        // CORRIGIDO: Results pagination
        resultsRowsSelectEl.addEventListener('change', function() {
            resultsRowsPerPage = parseInt(this.value);
            resultsPage = 1; // Reset to first page
            renderResultsTable();
        });
        
        resultsPrevBtnEl.addEventListener('click', function() {
            if (resultsPage > 1) {
                resultsPage--;
                renderResultsTable();
            }
        });
        
        resultsNextBtnEl.addEventListener('click', function() {
            const totalPages = Math.ceil(resultsData.length / resultsRowsPerPage);
            if (resultsPage < totalPages) {
                resultsPage++;
                renderResultsTable();
            }
        });
    }
    
    // Update potential return calculation
    function updatePotentialReturn() {
        const payout = parseFloat(payoutInputEl.value) || 0;
        const entryValue = parseFloat(entryValueEl.value) || 0;
        const potentialReturn = entryValue * (payout / 100);
        potentialReturnEl.textContent = `R$ ${potentialReturn.toFixed(2)}`;
    }
    
    // Initialize the dashboard
    initDashboard();
});
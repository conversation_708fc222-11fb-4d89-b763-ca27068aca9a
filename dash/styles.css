/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: #000;
    color: #fff;
    line-height: 1.5;
}

.container {
    /*max-width: 1200px;*/
    margin: 0 auto;
    padding: 1.5rem;
}

.dashboard-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

/* Card Styles */
.cards-container {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
    .cards-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .cards-container {
        grid-template-columns: repeat(4, 1fr);
    }
}

.card {
    background-color: #18181b;
    border: 1px solid #27272a;
    border-radius: 0.5rem;
    overflow: hidden;
}

.card-header {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #27272a;
}

.card-title {
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.card-description {
    font-size: 0.75rem;
    color: #a1a1aa;
    margin-top: 0.25rem;
}

.card-content {
    padding: 1rem;
}

.card-footer {
    padding: 1rem;
    border-top: 1px solid #27272a;
    display: flex;
    justify-content: space-between;
}

.flex-column {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* Main Content Layout */
.main-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
    .main-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

.full-width {
    grid-column: 1 / -1;
}

/* Time Display */
.time-display {
    font-size: 1.5rem;
    font-weight: 700;
}

/* Balance Display */
.balance-display {
    font-size: 1.5rem;
    font-weight: 700;
    color: #10b981;
}

/* Payout Input */
.payout-input {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.payout-symbol {
    font-size: 1.25rem;
    font-weight: 700;
    color: #10b981;
}

/* Form Elements */
.form-group {
    margin-bottom: 1rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.input {
    width: 100%;
    padding: 0.5rem;
    background-color: #27272a;
    border: 1px solid #3f3f46;
    border-radius: 0.25rem;
    color: #fff;
    font-size: 0.875rem;
}

.input:focus {
    outline: none;
    border-color: #10b981;
}

.asset-display {
    padding: 0.75rem;
    background-color: #27272a;
    border: 1px solid #3f3f46;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.input-with-prefix {
    position: relative;
}

.input-prefix {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #a1a1aa;
    font-size: 0.875rem;
}

.input-with-prefix .input {
    padding-left: 2.25rem;
}

/* Potential Return */
.potential-return {
    margin-top: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.return-label {
    color: #a1a1aa;
    font-size: 0.875rem;
}

.return-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #10b981;
}

/* Robot Toggle */
.robot-toggle {
    margin-top: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.toggle-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    cursor: pointer;
}

/* Switch Toggle */
.switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #3f3f46;
    transition: .4s;
    border-radius: 20px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #10b981;
}

input:focus + .slider {
    box-shadow: 0 0 1px #10b981;
}

input:checked + .slider:before {
    transform: translateX(20px);
}

/* Buttons */
.button {
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: background-color 0.2s;
}

.button-outline {
    background-color: transparent;
    border: 1px solid #3f3f46;
    color: #d4d4d8;
}

.button-outline:hover {
    background-color: #27272a;
    color: #fff;
}

.button-primary {
    background-color: #10b981;
    border: 1px solid #10b981;
    color: #fff;
}

.button-primary:hover {
    background-color: #059669;
}

/* Table Styles */
.table-container {
    overflow-x: auto;
    flex-grow: 1;
}

table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.75rem;
}

th, td {
    padding: 0.25rem 0.5rem;
    text-align: left;
    border-bottom: 1px solid #27272a;
}

th {
    color: #a1a1aa;
    font-weight: normal;
}

tbody tr:last-child td {
    border-bottom: none;
}

.badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0 0.5rem;
    height: 1rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.badge-green {
    background-color: #10b981;
    color: #fff;
}

.badge-red {
    background-color: #ef4444;
    color: #fff;
}

.text-green {
    color: #10b981;
}

.text-red {
    color: #ef4444;
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 0.5rem;
    margin-top: auto;
    border-top: 1px solid #27272a;
}

.rows-per-page {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: #a1a1aa;
}

.rows-per-page select {
    background-color: #27272a;
    border: 1px solid #3f3f46;
    color: #fff;
    padding: 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.pagination-button {
    width: 1.75rem;
    height: 1.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #27272a;
    border: 1px solid #3f3f46;
    border-radius: 0.25rem;
    color: #fff;
    cursor: pointer;
}

.pagination-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-button:not(:disabled):hover {
    background-color: #3f3f46;
}

.pagination-info {
    padding: 0 0.5rem;
    font-size: 0.75rem;
    color: #a1a1aa;
}

/* Icons */
.icon-small {
    width: 1rem;
    height: 1rem;
    margin-right: 0.5rem;
}

.icon-tiny {
    width: 0.75rem;
    height: 0.75rem;
}

/* Table Card */
.table-card .card-content {
    padding: 0;
}

.table-card .table-container {
    padding: 0.5rem;
}

.table-card .pagination-container {
    padding: 0.5rem;
}
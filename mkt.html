<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Implementação de Marketplace</title>
    <style>
        :root {
            --primary-color: #4a6fa5;
            --secondary-color: #166088;
            --accent-color: #4caf50;
            --bg-color: #f9f9f9;
            --text-color: #333;
            --border-color: #ddd;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background-color: var(--bg-color);
            color: var(--text-color);
            line-height: 1.6;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: var(--secondary-color);
            text-align: center;
            margin-bottom: 30px;
            font-size: 28px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        input[type="text"],
        input[type="date"] {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 16px;
        }
        
        .step {
            background-color: #f5f7fa;
            border-left: 4px solid var(--primary-color);
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0 8px 8px 0;
        }
        
        .step-title {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 10px;
            color: var(--primary-color);
        }
        
        .checkbox-item {
            margin: 10px 0;
            display: flex;
            align-items: flex-start;
        }
        
        .checkbox-item input[type="checkbox"] {
            margin-right: 10px;
            margin-top: 5px;
            transform: scale(1.2);
        }
        
        .sub-items {
            margin-left: 30px;
        }
        
        .submit-btn {
            background-color: var(--accent-color);
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 18px;
            border-radius: 4px;
            cursor: pointer;
            display: block;
            margin: 30px auto 0;
            transition: background-color 0.3s;
        }
        
        .submit-btn:hover {
            background-color: #45a049;
        }
        
        .progress-bar {
            height: 10px;
            background-color: #e0e0e0;
            border-radius: 5px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }
        
        .progress {
            height: 100%;
            background-color: var(--accent-color);
            width: 0;
            transition: width 0.5s;
            border-radius: 5px;
        }
        
        .percentage {
            text-align: center;
            font-weight: bold;
            margin-top: 5px;
            color: var(--secondary-color);
        }
        
        @media screen and (max-width: 600px) {
            .container {
                padding: 15px;
            }
            
            h1 {
                font-size: 22px;
            }
            
            .step-title {
                font-size: 16px;
            }
            
            .checkbox-item {
                font-size: 14px;
            }
            
            .submit-btn {
                width: 100%;
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Processo de Implementação de Marketplace</h1>
        
        <div class="progress-bar">
            <div class="progress" id="progress"></div>
        </div>
        <div class="percentage" id="percentage">0% Concluído</div>
        
        <form id="marketplaceForm">
            <div class="form-group">
                <label for="marketplaceName">Nome do Marketplace:</label>
                <input type="text" id="marketplaceName" required placeholder="Digite o nome do marketplace">
            </div>
            
            <div class="form-group">
                <label for="implementationDate">Data de Implementação:</label>
                <input type="date" id="implementationDate" required>
            </div>
            
            <div class="steps-container">
                <!-- Step 1 -->
                <div class="step">
                    <div class="step-title">1. Envio para os Marketplaces</div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="step1_1" class="step-checkbox">
                        <label for="step1_1">Realizar o envio dos produtos e informações para os marketplaces conforme os padrões exigidos.</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="step1_2" class="step-checkbox">
                        <label for="step1_2">Garantir que todos os dados estejam corretamente mapeados e validados.</label>
                    </div>
                </div>
                
                <!-- Step 2 -->
                <div class="step">
                    <div class="step-title">2. Adição de Novo Marketplace ao Dashboard</div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="step2_1" class="step-checkbox">
                        <label for="step2_1">Adicionar o novo marketplace na interface do dashboard, ao lado do Kleiton.</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="step2_2" class="step-checkbox">
                        <label for="step2_2">Assegurar que todas as funcionalidades e indicadores estejam corretamente configurados.</label>
                    </div>
                </div>
                
                <!-- Step 3 -->
                <div class="step">
                    <div class="step-title">3. Integração com o Sistema de Repasse</div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="step3_1" class="step-checkbox">
                        <label for="step3_1">Adicionar o novo marketplace ao sistema de repasse.</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="step3_2" class="step-checkbox">
                        <label for="step3_2">Se for Mirakl: O fluxo já está pronto, apenas configurar a nova entrada.</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="step3_3" class="step-checkbox">
                        <label for="step3_3">Se não for Mirakl: Necessário realizar o mapeamento e a configuração do repasse.</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="step3_4" class="step-checkbox">
                        <label for="step3_4">Caso um novo repasse seja criado futuramente para um marketplace com estrutura similar, não será necessário um novo mapeamento.</label>
                    </div>
                </div>
                
                <!-- Step 4 -->
                <div class="step">
                    <div class="step-title">4. Validação Inicial</div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="step4_1" class="step-checkbox">
                        <label for="step4_1">Realizar duas compras-teste para validar:</label>
                    </div>
                    <div class="sub-items">
                        <div class="checkbox-item">
                            <input type="checkbox" id="step4_1_1" class="step-checkbox">
                            <label for="step4_1_1">Valores e cálculos.</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="step4_1_2" class="step-checkbox">
                            <label for="step4_1_2">Comissões.</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="step4_1_3" class="step-checkbox">
                            <label for="step4_1_3">Morada (endereço de entrega e faturamento).</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="step4_1_4" class="step-checkbox">
                            <label for="step4_1_4">Outras informações relevantes.</label>
                        </div>
                    </div>
                </div>
                
                <!-- Step 5 -->
                <div class="step">
                    <div class="step-title">5. Verificações no Frontend</div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="step5_1" class="step-checkbox">
                        <label for="step5_1">Conferir no frontend da aplicação se todas as informações e funcionalidades estão operando corretamente. Validação da lista de Carriers nos detalhes da order.</label>
                    </div>
                </div>
                
                <!-- Step 6 -->
                <div class="step">
                    <div class="step-title">6. Validação dos Carriers</div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="step6_1" class="step-checkbox">
                        <label for="step6_1">Confirmar que os carriers (transportadoras e logística) estão corretamente configurados para o novo marketplace.</label>
                    </div>
                </div>
                
                <!-- Step 7 -->
                <div class="step">
                    <div class="step-title">7. Inserir o Novo Marketplace no THOR</div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="step7_1" class="step-checkbox">
                        <label for="step7_1">Validar os cálculos das orders.</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="step7_2" class="step-checkbox">
                        <label for="step7_2">Verificar se todas as orders estão corretamente filtradas dentro do THOR.</label>
                    </div>
                </div>
                
                <!-- Step 8 -->
                <div class="step">
                    <div class="step-title">8. Monitoramento Inicial</div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="step8_1" class="step-checkbox">
                        <label for="step8_1">Iniciar as vendas e repasses, mantendo um monitoramento ativo nas 5 a 8 primeiras vendas.</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="step8_2" class="step-checkbox">
                        <label for="step8_2">Validar vendas com um ou mais produtos para garantir o correto funcionamento do fluxo.</label>
                    </div>
                </div>
                
                <!-- Step 9 -->
                <div class="step">
                    <div class="step-title">9. Operacionalização Completa</div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="step9_1" class="step-checkbox">
                        <label for="step9_1">Após a fase de monitoramento, o marketplace passa a operar de forma autônoma dentro do sistema.</label>
                    </div>
                </div>
            </div>
            
            <button type="submit" class="submit-btn">Enviar e Gerar JSON</button>
        </form>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Progress bar functionality
            const checkboxes = document.querySelectorAll('.step-checkbox');
            const progressBar = document.getElementById('progress');
            const percentageText = document.getElementById('percentage');
            const form = document.getElementById('marketplaceForm');
            
            // Update progress when checkboxes are clicked
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateProgress);
            });
            
            function updateProgress() {
                const total = checkboxes.length;
                let checked = 0;
                
                checkboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        checked++;
                    }
                });
                
                const percentage = Math.round((checked / total) * 100);
                progressBar.style.width = percentage + '%';
                percentageText.textContent = percentage + '% Concluído';
            }
            
            // Form submission
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const marketplaceName = document.getElementById('marketplaceName').value.trim();
                const implementationDate = document.getElementById('implementationDate').value;
                
                if (!marketplaceName || !implementationDate) {
                    alert('Por favor, preencha todos os campos obrigatórios.');
                    return;
                }
                
                // Collect form data
                const formData = {
                    marketplaceName: marketplaceName,
                    implementationDate: implementationDate,
                    steps: {}
                };
                
                // Add steps data
                for (let i = 1; i <= 9; i++) {
                    formData.steps[`step${i}`] = {};
                    
                    // Get all checkboxes for this step
                    const stepCheckboxes = document.querySelectorAll(`[id^="step${i}_"]`);
                    stepCheckboxes.forEach(checkbox => {
                        const checkboxId = checkbox.id;
                        formData.steps[`step${i}`][checkboxId] = checkbox.checked;
                    });
                }
                
                // Generate JSON file and prompt download
                const jsonData = JSON.stringify(formData, null, 2);
                const blob = new Blob([jsonData], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                
                // Safe filename - replace spaces and special characters
                const fileName = marketplaceName.replace(/[^a-z0-9]/gi, '_').toLowerCase() + '_implementacao.json';
                
                const a = document.createElement('a');
                a.download = fileName;
                a.href = url;
                a.click();
                
                URL.revokeObjectURL(url);
            });
        });
    </script>
# Referência do Projeto Apolus

Este arquivo serve como referência centralizada para todas as solicitações, configurações e informações importantes do projeto.

## Data de criação
**11 de Agosto de 2025**

## Estrutura do Projeto
- **cloud/** - A<PERSON><PERSON><PERSON>vel principal
- **dash/** - Dashboard frontend (HTML/JS/CSS)
- **service.srv/laravel/** - Serviço Laravel adicional
- **mkt.html** - Página de marketing
- **docker-compose.yml** - Configuração Docker

## Tecnologias Identificadas
- Laravel (PHP)
- Docker
- Tailwind CSS
- JavaScript
- SQLite

## Histórico de Solicitações
*Este histórico será atualizado a cada nova solicitação*

### 2025-08-11
1. **Criação do arquivo de referência** - Arquivo criado para centralizar informações do projeto
2. **Análise de tratamento de erros no OrderController** - Identificação de problemas que quebram foreach em caso de dados inválidos

#### Problemas Identificados no Sistema:
**Controller Principal:**
- `/service.srv/laravel/app/app/Http/Controllers/OrderController.php` - Múltiplos pontos de falha nos foreach

**Modelos que precisam de tratamento:**
- `/service.srv/laravel/app/app/Models/FlowBighub.php` - Problemas em foreach e acesso a propriedades
- `/service.srv/laravel/app/app/Models/FlowFnac.php` - Foreach sem tratamento (linha 48)
- `/service.srv/laravel/app/app/Models/FlowCdiscount.php` - Foreach sem tratamento (linha 40) 
- `/service.srv/laravel/app/app/Models/FlowDocMorris.php` - Foreach aninhados sem tratamento (linhas 47-48)

**Serviços:**
- `/service.srv/laravel/app/app/Services/OrderFlowService.php` - Métodos que podem falhar com dados inválidos

#### Pontos Críticos Identificados:
1. **Acesso a propriedades sem verificação:** `$order->marketplace->name`, `$order->channel->code`
2. **Foreach sem try-catch:** Linhas 169, 182, 419, 430, 728, 740, 864, 876 no OrderController
3. **Chamadas de API sem tratamento:** getCountryMarketplace(), getEanCode()
4. **Propriedades aninhadas:** `$orderResp->customer->shipping_address->country`
5. **Die/Exit que param o fluxo:** FlowBighub linha 208, FlowFnac linha 131

---

## Notas Importantes
- Este arquivo será atualizado automaticamente conforme novas solicitações forem feitas
- Mantém o histórico de mudanças para referência futura
- Serve como backup das configurações e decisões do projeto
# Modificação do Array additional_info

## Data da Alteração
05/09/2025

## Arquivo Modificado
- `service.srv/laravel/app/app/Models/FlowManoMano.php` (linhas 152-158)

## Descrição da Alteração
Foi adicionada funcionalidade para incluir o `marketplace_id` no array `$additional_info`, que anteriormente continha apenas informações do canal quando `account > 1`.

## Estrutura Original
```php
$additional_info = [];
if (isset($order->marketplace->account) && $order->marketplace->account > 1) {
    $additional_info[] = [
        'key' => $channel,
        'value' => 'channel-' . $order->marketplace->account
    ];
}
```

Resultado: `[{"key":"leroymerlin","value":"channel-2"}]`

## Estrutura Modificada
```php
$additional_info = [];
if (isset($order->marketplace->account) && $order->marketplace->account > 1) {
    $additional_info[] = [
        'key' => $channel,
        'value' => 'channel-' . $order->marketplace->account
    ];
}

// Adicionar marketplace_id
if (isset($order->marketplace->id)) {
    $additional_info[] = [
        'key' => 'marketplace_id',
        'value' => (string) $order->marketplace->id
    ];
}
```

Resultado: `[{"key":"leroymerlin","value":"channel-2"},{"key":"marketplace_id","value":"77"}]`

## Detalhes Técnicos

### Fonte dos Dados
- Os dados do marketplace vêm de `$order->marketplace`
- Estrutura disponível:
  ```json
  "marketplace": {
      "id": 77,
      "name": "manomano",
      "account": 1,
      "country": "ES",
      "email": "<EMAIL>",
      "model": "Manomano",
      "enterprise_id": 2,
      "enterprise": {
          "id": 2,
          "name": "Goldenjumper SA",
          "country": "PT",
          "nif": "*********",
          "model": "Moloni",
          "is_active": false
      }
  }
  ```

### Problema Encontrado e Solução
- **Erro:** "Object of class stdClass could not be converted to string"
- **Causa:** `$order->marketplace->id` retorna objeto stdClass
- **Solução:** Conversão explícita para string usando `(string) $order->marketplace->id`

## Aplicação em Outros Arquivos

### Arquivos Candidatos para a Mesma Modificação
Esta alteração deve ser aplicada em outros arquivos Flow similares que processam pedidos de marketplace:
- FlowAmazon.php
- FlowEbay.php
- FlowLeroyMerlin.php
- Outros arquivos Flow*

### Padrão para Implementação
1. Localizar o array `$additional_info` (geralmente entre linhas 140-160)
2. Adicionar o bloco de código após a verificação do `account`:
   ```php
   // Adicionar marketplace_id
   if (isset($order->marketplace->id)) {
       $additional_info[] = [
           'key' => 'marketplace_id',
           'value' => (string) $order->marketplace->id
       ];
   }
   ```
3. Verificar se a conversão `(string)` é necessária testando primeiro

### Backup
Sempre criar backup antes da modificação:
```bash
cp arquivo.php arquivo.php.backup
```

## Observações
- A modificação mantém a compatibilidade com o código existente
- O marketplace_id é adicionado independentemente da condição `account > 1`
- A conversão para string previne erros de tipo em operações futuras
# Apolus Service - Documentação Completa

## 📋 Visão Geral

O **Apolus Service** é um sistema de processamento de pedidos que atua como middleware entre marketplaces e sistemas internos de gerenciamento de pedidos. O sistema automatiza a correspondência de vendedores, processamento de pedidos e integração com múltiplas plataformas de marketplace europeias.

### 🎯 Objetivos Principais

- **Processamento Automático de Pedidos**: Correspondência inteligente de vendedores baseada em EAN, canal e preço
- **Integração Multi-Marketplace**: Suporte para 30+ marketplaces europeus incluindo Fnac, Cdiscount, DocMorris, ManoMano e plataformas Mirakl
- **Cálculo Dinâmico de Comissão**: Sistema inteligente de cálculo e validação de comissões entre BigHub e marketplaces
- **Gerenciamento de Ciclo de Vida**: Controle completo do estado dos pedidos desde criação até envio
- **Processamento de Produtos Especiais**: Tratamento especial para produtos de relógio e outros itens específicos

## 🏗️ Arquitetura do Sistema

### Stack Tecnológico

| Componente | Tecnologia | Versão | Propósito |
|------------|------------|---------|-----------|
| **Framework** | Laravel | 11.31 | Framework web principal |
| **Linguagem** | PHP | ^8.2 | Linguagem de programação backend |
| **Banco de Dados** | SQLite (dev) / MySQL (prod) | - | Persistência de dados |
| **Servidor Web** | Nginx | - | Servidor HTTP e proxy reverso |
| **Gerenciador de Processo** | PHP-FPM | - | Gerenciamento de processos PHP |
| **Cache** | Redis (produção) | - | Cache de sessão e aplicação |
| **Processamento de Fila** | Laravel Queue | - | Processamento assíncrono |
| **Testes** | PHPUnit | 11.0.1 | Testes unitários e de integração |
| **Qualidade de Código** | Laravel Pint | 1.13 | Formatação de código |

### Diagrama de Arquitetura

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Marketplaces  │───▶│  Apolus Service │───▶│   BigHub API    │
│   (30+ platforms)│    │   (Laravel)     │    │   (Order Mgmt)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   Database       │
                       │   (SQLite/MySQL) │
                       └─────────────────┘
```

### Componentes Principais

- **OrderController**: Lógica principal de processamento de pedidos
- **FlowBighub**: Integração com API BigHub
- **OrderFlowService**: Serviços de processamento de pedidos
- **ProductChannel**: Gerenciamento de produtos por canal
- **UserClient**: Gerenciamento de clientes vendedores
- **Flow Classes**: Lógica específica para cada marketplace (FlowFnac, FlowCdiscount, etc.)

## 🚀 Instalação e Configuração

### Pré-requisitos

- PHP 8.2+
- Composer 2.0+
- Node.js 18+
- SQLite (desenvolvimento) ou MySQL (produção)
- Redis (produção)

### Instalação Rápida

```bash
# 1. Clonar repositório
git clone <repository-url> apolus-service
cd apolus-service/laravel/app

# 2. Instalar dependências
composer install
npm install

# 3. Configurar ambiente
cp .env.example .env
php artisan key:generate

# 4. Configurar banco de dados
touch database/database.sqlite
php artisan migrate

# 5. Iniciar servidor de desenvolvimento
php artisan serve
```

### Variáveis de Ambiente Essenciais

```bash
# Aplicação
APP_NAME="Apolus Service"
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost:8000

# Banco de Dados
DB_CONNECTION=sqlite
DB_DATABASE=/caminho/para/database/database.sqlite

# APIs Externas
BIGHUB_API_KEY=sua-chave-api-bighub
BIGHUB_BEARER_TOKEN=seu-bearer-token
BIGHUB_BASE_URL=https://app.bighub.store/

# Marketplaces
FNAC_API_KEY=sua-chave-api-fnac
CDISCOUNT_API_KEY=sua-chave-api-cdiscount
DOCMORRIS_API_KEY=sua-chave-api-docmorris
MANOMANO_API_KEY=sua-chave-api-manomano
STOCKLY_API_KEY=sua-chave-api-stockly
```

## 🔧 Funcionalidades Principais

### Processamento de Pedidos

#### Pipeline de Processamento
1. **Ingestão de Pedidos**: Recebe pedidos das APIs de marketplace via endpoints webhook
2. **Correspondência de Vendedores**: Corresponde pedidos a vendedores baseado em códigos EAN, preços e disponibilidade
3. **Cálculo de Comissão**: Calcula comissões BigHub vs comissões de marketplace
4. **Atribuição de Pedidos**: Atribui pedidos a vendedores apropriados ou encaminha para suporte
5. **Notificação**: Envia notificações por email para vendedores sobre novos pedidos

#### Critérios de Correspondência
- Correspondência exata de preço
- Tolerância de preço (≤ 5% maior)
- Validação de estoque disponível
- Verificação de vendedores ativos
- Fallback para suporte quando necessário

### Integração com Marketplaces

#### Marketplaces Suportados
- **Mirakl**: 25+ plataformas baseadas em Mirakl (Pixmania, ShopApotheke, etc.)
- **Especiais**: Fnac, Cdiscount, DocMorris, ManoMano, Stockly
- **Lógica por País**: Tratamento de diferentes códigos de país e variações de marketplace

#### Funcionalidades de Integração
- Extração automática de dados de pedidos
- Normalização de dados entre diferentes formatos
- Tratamento de erros e exceções
- Processamento específico por tipo de marketplace

### Lógica de Negócio Especial

#### Produtos de Relógio
- Tratamento especial para produtos de relógio
- Atribuição automática a vendedor específico
- Validação de preços específicos

#### Pedidos Multi-Produto
- Processamento de pedidos com múltiplos produtos
- Cálculo de preços complexos
- Validação de disponibilidade por produto

## 🔒 Segurança e Conformidade

### Autenticação e Autorização

#### Autenticação de API
```php
// Autenticação Bearer Token BigHub
const BEAR = 'bCZpeQAcwEIcm7y1hYCK3VspKQfWJ5IlgalrUqq2f60552dd';
$request_headers[] = 'Authorization: Bearer '.self::BEAR;

// Autenticação por Chave de API
const XAPIKEY = '7e0913c6-015d-4c60-9c49-3e4837922e53';
$request_headers[] = 'x-api-key:'.self::XAPIKEY;
```

#### Autenticação de Usuário
```php
class User extends Authenticatable
{
    protected $fillable = ['name', 'email', 'password'];
    protected $hidden = ['password', 'remember_token'];
    
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }
}
```

### Medidas de Segurança

- **HTTPS**: Comunicação criptografada
- **Validação de Entrada**: Sanitização de todos os inputs
- **Rate Limiting**: Limitação de taxa de requisições
- **Headers Seguros**: Configuração de headers de segurança
- **Sessões Seguras**: Gerenciamento seguro de sessões
- **Logs de Auditoria**: Registro de todas as operações críticas

## 📡 API Reference

### Endpoints Principais

#### Processar Pedidos
```bash
# Processar todos os pedidos não atribuídos
GET /set-user-to-user

# Processar pedido específico
GET /set-order-user/{order_id}/{user_id}

# Processar pedidos do suporte
POST /support-assign-orders
```

#### Validação e Tokens
```bash
# Validar token de link
GET /check-token?token={token}

# Obter token CSRF
GET /csrf-token
```

#### Webhooks
```bash
# Webhook PIX
POST /pix_webhook.php

# Tracking FTP
POST /ftp_tracking.php
```

### Exemplos de Uso

#### Processar Pedido Específico
```bash
curl -X GET "http://localhost:8000/set-order-user/12345/67890" \
  -H "Authorization: Bearer seu-token" \
  -H "Content-Type: application/json"
```

#### Verificar Token
```bash
curl -X GET "http://localhost:8000/check-token?token=abc123def456" \
  -H "Content-Type: application/json"
```

## 🗄️ Banco de Dados

### Estrutura Principal

#### Tabelas Principais
- **users**: Usuários do sistema
- **user_clients**: Clientes vendedores
- **user_products**: Produtos dos vendedores
- **product_channels**: Canais de produtos
- **links**: Links de tracking
- **flow_orders**: Pedidos processados

#### Migrações
```bash
# Executar migrações
php artisan migrate

# Rollback de migrações
php artisan migrate:rollback

# Status das migrações
php artisan migrate:status
```

### Seeders
```bash
# Executar seeders
php artisan db:seed

# Seeders específicos
php artisan db:seed --class=UserSeeder
```

## 🧪 Testes

### Estratégia de Testes

#### Tipos de Testes
- **Testes Unitários**: Testam componentes individuais
- **Testes de Integração**: Testam integração entre componentes
- **Testes de API**: Testam endpoints da API
- **Testes de Funcionalidade**: Testam fluxos completos

#### Executar Testes
```bash
# Todos os testes
php artisan test

# Testes específicos
php artisan test --filter=OrderControllerTest

# Testes com cobertura
php artisan test --coverage
```

### Exemplo de Teste
```php
<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class OrderControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_process_order()
    {
        $response = $this->get('/set-user-to-user');
        
        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
    }
}
```

## 🚀 Deploy e Produção

### Configuração de Produção

#### Servidor Web (Nginx)
```nginx
server {
    listen 80;
    server_name apolus-service.com;
    root /var/www/apolus-service/laravel/app/public;
    
    index index.php;
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

#### Otimizações de Produção
```bash
# Otimizar autoloader
composer install --no-dev --optimize-autoloader

# Cache de configuração
php artisan config:cache

# Cache de rotas
php artisan route:cache

# Cache de views
php artisan view:cache

# Otimizar banco de dados
php artisan optimize
```

### Monitoramento

#### Logs
- **Laravel Log**: `/storage/logs/laravel.log`
- **Nginx Log**: `/var/log/nginx/access.log` e `/var/log/nginx/error.log`
- **PHP-FPM Log**: `/var/log/php8.2-fpm.log`

#### Métricas Importantes
- Taxa de processamento de pedidos
- Tempo de resposta da API
- Uso de memória e CPU
- Erros de integração com marketplaces
- Disponibilidade do sistema

## 🔧 Operações e Manutenção

### Comandos Úteis

#### Desenvolvimento
```bash
# Limpar cache
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Regenerar chaves
php artisan key:generate

# Verificar configuração
php artisan config:show
```

#### Produção
```bash
# Manutenção
php artisan down
php artisan up

# Queue workers
php artisan queue:work
php artisan queue:restart

# Backup do banco
php artisan db:backup
```

### Troubleshooting

#### Problemas Comuns
1. **Erro de conexão com BigHub**: Verificar chaves de API e conectividade
2. **Pedidos não processados**: Verificar logs e status dos vendedores
3. **Problemas de performance**: Verificar cache e otimizações
4. **Erros de integração**: Verificar webhooks e endpoints

#### Logs de Debug
```bash
# Ativar debug
APP_DEBUG=true

# Ver logs em tempo real
tail -f storage/logs/laravel.log

# Filtrar logs por nível
grep "ERROR" storage/logs/laravel.log
```

## 📁 Estrutura do Projeto

```
apolus-service/
├── laravel/
│   ├── app/
│   │   ├── Http/Controllers/
│   │   │   ├── OrderController.php
│   │   │   └── LinkController.php
│   │   ├── Models/
│   │   │   ├── User.php
│   │   │   ├── ProductChannel.php
│   │   │   ├── FlowBighub.php
│   │   │   ├── FlowOrders.php
│   │   │   ├── FlowFnac.php
│   │   │   ├── FlowCdiscount.php
│   │   │   ├── FlowDocMorris.php
│   │   │   ├── FlowManoMano.php
│   │   │   └── FlowStockly.php
│   │   └── Services/
│   │       └── OrderFlowService.php
│   ├── config/
│   ├── database/
│   │   ├── migrations/
│   │   └── seeders/
│   ├── routes/
│   │   └── web.php
│   ├── public/
│   │   ├── index.php
│   │   ├── pix_webhook.php
│   │   └── ftp_tracking.php
│   └── storage/
│       └── logs/
├── nginx.conf
├── srv.conf
└── document/
    └── README.md
```

## 🤝 Contribuição e Suporte

### Padrões de Código
- Siga os padrões PSR-12 para PHP
- Use Laravel Pint para formatação de código
- Escreva testes para novas funcionalidades
- Documente mudanças significativas

### Processo de Contribuição
1. Fork o repositório
2. Crie uma branch para sua feature (`git checkout -b feature/nova-funcionalidade`)
3. Commit suas mudanças (`git commit -am 'Adiciona nova funcionalidade'`)
4. Push para a branch (`git push origin feature/nova-funcionalidade`)
5. Abra um Pull Request

### Contato
- **Email**: <EMAIL>
- **Documentação**: Consulte este arquivo
- **Issues**: Reporte problemas através do sistema de issues do repositório

## 📄 Licença

Este projeto é proprietário e confidencial. Todos os direitos reservados.

---

*Última atualização: Janeiro 2025*  
*Versão da documentação: 2.0*  
*Sistema: Apolus Service v1.0*


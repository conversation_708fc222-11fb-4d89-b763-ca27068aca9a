# Apolus Service - Complete Technical Documentation

## 📋 Table of Contents

1. [System Overview](#system-overview)
2. [Architecture and Design](#architecture-and-design)
3. [Code Structure](#code-structure)
4. [Controllers and Endpoints](#controllers-and-endpoints)
5. [Models and Entities](#models-and-entities)
6. [Services and Business Logic](#services-and-business-logic)
7. [External API Integration](#external-api-integration)
8. [Order Processing](#order-processing)
9. [Supported Marketplaces](#supported-marketplaces)
10. [Database](#database)
11. [Configuration and Deployment](#configuration-and-deployment)
12. [Security](#security)
13. [Monitoring and Logs](#monitoring-and-logs)
14. [Troubleshooting](#troubleshooting)

---

## 🎯 System Overview

### Purpose
**Apolus Service** is a Laravel-based middleware system that automates order processing and assignment between multiple European marketplaces and the BigHub system. The system functions as an intelligent hub that:

- Receives orders from 30+ different marketplaces
- Processes and normalizes order data
- Automatically assigns orders to sellers based on business criteria
- Manages commissions and price calculations
- Integrates with external APIs for tracking and notifications

### Main Flow
```
Marketplace APIs → Apolus Service → BigHub API → Sellers
                      ↓
                 Database + Logs
```

---

## 🏗️ Architecture and Design

### Architectural Patterns

#### 1. **Controller-Service-Model Pattern**
- **Controllers**: Handle HTTP requests and validation
- **Services**: Contain complex business logic
- **Models**: Represent data entities and business rules

#### 2. **Strategy Pattern for Marketplaces**
Each marketplace has its own `Flow{Marketplace}` class with specific logic:
- `FlowBighub` - Main BigHub integration
- `FlowFnac` - Fnac-specific processing
- `FlowCdiscount` - Cdiscount-specific processing
- `FlowDocMorris` - DocMorris-specific processing
- `FlowManoMano` - ManoMano-specific processing
- `FlowStockly` - Stockly-specific processing

#### 3. **Factory Pattern for Payloads**
Different marketplaces require different data formats, implemented through specific methods in each Flow class.

### Directory Structure
```
laravel/app/
├── app/
│   ├── Http/Controllers/
│   │   ├── OrderController.php      # Main controller
│   │   └── FtpTrackingController.php # Tracking upload
│   ├── Models/
│   │   ├── FlowBighub.php          # BigHub integration
│   │   ├── FlowFnac.php            # Fnac processing
│   │   ├── FlowCdiscount.php       # Cdiscount processing
│   │   ├── FlowDocMorris.php       # DocMorris processing
│   │   ├── FlowManoMano.php        # ManoMano processing
│   │   ├── FlowStockly.php         # Stockly processing
│   │   ├── FlowOrders.php          # Order entities
│   │   ├── User.php                # System users
│   │   ├── UserClient.php          # Seller clients
│   │   ├── UserProduct.php         # Seller products
│   │   └── ProductChannel.php      # Product channels
│   └── Services/
│       └── OrderFlowService.php    # Processing services
```

---

## 🎮 Controllers and Endpoints

### OrderController

#### Responsibilities
- Main order processing
- Order assignment to sellers
- Integration with multiple marketplaces
- Commission management

#### Main Endpoints

##### 1. `GET /set-user-to-user`
**Purpose**: Process all unassigned orders
```php
public function processUnassignedOrders($orderIdTbl = null, $userId = null)
```

**Flow**:
1. Fetch unassigned orders via BigHub API
2. For each order, identify the marketplace
3. Apply marketplace-specific logic
4. Find corresponding seller
5. Assign order to seller
6. Return operation result

##### 2. `GET /set-order-user/{order_id}/{user_id}`
**Purpose**: Process specific order
```php
public function processUnassignedOrders($orderIdTbl = null, $userId = null)
```

**Parameters**:
- `order_id`: ID of order to be processed
- `user_id`: ID of user for assignment

##### 3. `POST /support-assign-orders`
**Purpose**: Support-requested processing
```php
public function processOrdersRequestedBySupport(Request $request)
```

**Payload**:
```json
{
    "channel": "fnac",
    "id_usuario": 123,
    "order_type": "manual",
    "order_id": "ORD-12345",
    "client_id": 456
}
```

#### Marketplace Configurations

```php
// Marketplaces using MIRAKL processor
protected $miraklMarketplaces = [
    'pixmania', 'shopapotheke', 'planetahuerto', 'rueducommerce',
    'elcorteingles', 'phonehouse', 'leroymerlin', 'macway',
    'tiendanimal', 'eprice', 'bulevip', 'empik', 'clubefashion',
    'carrefour', 'leclerc', 'quirumedes', 'pccomponentes',
    'conrad', 'bigbang', 'ventunique', 'worten', 'conforama',
    'ubaldi', 'mediamarkt', 'bricodepot', 'zooplus', 'truffaut',
    'alltricks', 'castorama', 'conforamaiberia', 'culturafr', 'tdmp'
];

// Marketplaces with specific processing
protected $specialMarketplaces = [
    'fnac', 'cdiscount', 'docmorris', 'manomano', 'makro', 
    'mktbighub', 'stockly'
];

// Marketplaces for future migration
protected $marketplacesAmigrar = [
    'castorama', 'xxxlgroup', 'axelspringerde', 'iberia',
    'wook', 'miravia', 'aliexpress', 'makro', 'kaufland'
];
```

### FtpTrackingController

#### Responsibilities
- Upload tracking information
- Generate CSV files
- FTP system integration

#### Endpoints

##### `POST /api/tracking/upload`
**Purpose**: Upload tracking information

**Payload**:
```json
{
    "order": "ORD-12345",
    "tracking_nr": "TRK789456123",
    "carrier": "DHL",
    "carrier_url": "https://www.dhl.com/tracking",
    "date": "15/01/2025"
}
```

**Validations**:
- `order`: required, string
- `tracking_nr`: required, string
- `carrier`: required, string
- `carrier_url`: required, valid URL
- `date`: optional, string (uses current date if not provided)

---

## 🗃️ Models and Entities

### FlowBighub

#### Purpose
Main integration with BigHub API for order management.

#### Important Constants
```php
const XAPIKEY = '7e0913c6-015d-4c60-9c49-3e4837922e53';
const URL_DATA_ORDERS = 'https://connect-big.bighub.store/api/orders/user/';
const BEAR = 'bCZpeQAcwEIcm7y1hYCK3VspKQfWJ5IlgalrUqq2f60552dd';
const SKU_PREFIX = 'BIGHUB';
const USER_ID = 33;
const URL_APIBIGHUB = 'https://app.bighub.store/';
const URL_PUSHTOQUEUE = 'https://app.bighub.store/queue/';
```

#### Main Methods

##### `curl_get_orders_no_user($id)`
Fetch unassigned orders for a specific user.

**Headers**:
```php
$request_headers[] = 'Content-Type: application/json';
$request_headers[] = 'Authorization: Bearer '.self::BEAR;
```

##### `curl_put_update_order_user($order_id, $user_id)`
Update order assignment to a specific user.

**URL**: `https://connect-big.bighub.store/api/orders/{order_id}/update-user/{user_id}`

### FlowOrders

#### Purpose
Represents order entities in the system.

#### Relationships
- Belongs to a `UserClient` (seller)
- Has multiple `UserProduct` (products)
- Connects with `ProductChannel` (channels)

### UserClient

#### Purpose
Represents seller clients in the system.

#### Main Fields
- `id`: Unique identifier
- `name`: Client name
- `email`: Contact email
- `active`: Active/inactive status
- `commission_rate`: Commission rate

### UserProduct

#### Purpose
Represents seller products.

#### Main Fields
- `ean`: Product EAN code
- `sku`: Product SKU
- `price`: Product price
- `stock`: Available quantity
- `channel_id`: Channel ID
- `user_id`: Seller ID

### ProductChannel

#### Purpose
Manages product channels and their configurations.

#### Main Fields
- `id`: Unique identifier
- `name`: Channel name
- `marketplace`: Associated marketplace
- `commission_rate`: Channel commission rate

---

## ⚙️ Services and Business Logic

### OrderFlowService

#### Purpose
Centralizes order processing logic and seller matching.

#### Main Methods

##### `removeLetters($string)`
Remove all letters from a string, keeping only numbers and other characters.

```php
public function removeLetters($string)
{
    return preg_replace('/[A-Za-z]/', '', $string);
}
```

##### `getEanCode($sku, $order, $marketplace)`
Extract EAN code based on SKU and order information.

**Logic**:
1. If SKU contains no letters, return original SKU (already EAN)
2. If contains letters, extract substring from position 6
3. Apply marketplace-specific rules

##### `findSellerByEanAndPrice($ean, $price, $channelId)`
Find seller based on EAN and price.

**Search Criteria**:
1. Exact price match
2. Price tolerance (≤ 5% higher)
3. Available stock
4. Active seller

##### `calculateCommission($sellerPrice, $marketplacePrice, $commissionRate)`
Calculate commission between seller price and marketplace.

**Formula**:
```php
$commission = ($marketplacePrice - $sellerPrice) * $commissionRate;
```

---

## 🔌 External API Integration

### BigHub API

#### Authentication
```php
// Bearer Token
const BEAR = 'bCZpeQAcwEIcm7y1hYCK3VspKQfWJ5IlgalrUqq2f60552dd';

// API Key
const XAPIKEY = '7e0913c6-015d-4c60-9c49-3e4837922e53';
```

#### Used Endpoints

##### 1. Fetch Unassigned Orders
```
GET https://connect-big.bighub.store/api/orders/user/{user_id}
```

**Headers**:
```
Content-Type: application/json
Authorization: Bearer {token}
```

##### 2. Update Order Assignment
```
PUT https://connect-big.bighub.store/api/orders/{order_id}/update-user/{user_id}
```

##### 3. Send to Queue
```
POST https://app.bighub.store/queue/
```

### Marketplace APIs

#### Fnac
- **Endpoint**: Fnac-specific
- **Authentication**: Specific API Key
- **Format**: Custom JSON

#### Cdiscount
- **Endpoint**: Cdiscount-specific
- **Authentication**: Specific API Key
- **Format**: Custom JSON

#### DocMorris
- **Endpoint**: DocMorris-specific
- **Authentication**: Specific API Key
- **Format**: Custom JSON

#### ManoMano
- **Endpoint**: ManoMano-specific
- **Authentication**: Specific API Key
- **Format**: Custom JSON

#### Stockly
- **Endpoint**: Stockly-specific
- **Authentication**: Specific API Key
- **Format**: Custom JSON

---

## 📦 Order Processing

### Main Flow

#### 1. Order Reception
```php
// OrderController::processUnassignedOrders()
$orders = $this->flowBighub->curl_get_orders_no_user($userId);
```

#### 2. Marketplace Identification
```php
$marketplace = $order->marketplace->name;
$isMirakl = in_array($marketplace, $this->miraklMarketplaces);
$isSpecial = in_array($marketplace, $this->specialMarketplaces);
```

#### 3. Marketplace-Specific Processing

##### Mirakl Marketplaces
```php
if ($isMirakl) {
    // Standardized processing for Mirakl
    $this->processMiraklOrder($order);
}
```

##### Special Marketplaces
```php
if ($isSpecial) {
    switch ($marketplace) {
        case 'fnac':
            $this->flowFnac->processOrder($order);
            break;
        case 'cdiscount':
            $this->flowCdiscount->processOrder($order);
            break;
        // ... other marketplaces
    }
}
```

#### 4. Seller Matching
```php
foreach ($order->products as $product) {
    $ean = $this->orderFlowService->getEanCode($product->sku, $order, $marketplace);
    $seller = $this->orderFlowService->findSellerByEanAndPrice(
        $ean, 
        $product->price, 
        $product->channel_id
    );
    
    if ($seller) {
        $this->assignOrderToSeller($order, $seller);
    }
}
```

#### 5. Order Assignment
```php
$result = $this->flowBighub->curl_put_update_order_user($orderId, $userId);
```

### Matching Logic

#### Search Criteria
1. **Exact EAN**: Exact EAN code match
2. **Price**: Exact match or 5% tolerance
3. **Stock**: Available quantity > 0
4. **Status**: Active seller
5. **Channel**: Correct marketplace channel

#### Matching Algorithm
```php
public function findSellerByEanAndPrice($ean, $price, $channelId)
{
    // 1. Search for exact price match
    $seller = UserProduct::where('ean', $ean)
        ->where('price', $price)
        ->where('stock', '>', 0)
        ->where('channel_id', $channelId)
        ->whereHas('user', function($query) {
            $query->where('active', true);
        })
        ->first();
    
    if ($seller) {
        return $seller;
    }
    
    // 2. Search with price tolerance (5%)
    $tolerancePrice = $price * 1.05;
    $seller = UserProduct::where('ean', $ean)
        ->where('price', '<=', $tolerancePrice)
        ->where('price', '>=', $price)
        ->where('stock', '>', 0)
        ->where('channel_id', $channelId)
        ->whereHas('user', function($query) {
            $query->where('active', true);
        })
        ->orderBy('price', 'asc')
        ->first();
    
    return $seller;
}
```

### Special Product Handling

#### Watch Products
```php
if ($this->isWatchProduct($product)) {
    // Automatically assign to specific watch seller
    $watchSellerId = $this->getWatchSellerId();
    $this->assignOrderToSeller($order, $watchSellerId);
    return;
}
```

#### Multi-Product Orders
```php
if (count($order->products) > 1) {
    // Process each product individually
    foreach ($order->products as $product) {
        $this->processProduct($product, $order);
    }
}
```

---

## 🏪 Supported Marketplaces

### Mirakl Marketplaces (25+)

#### Characteristics
- Standardized processing
- Consistent data format
- Similar APIs

#### Complete List
```php
$miraklMarketplaces = [
    'pixmania', 'shopapotheke', 'planetahuerto', 'rueducommerce',
    'elcorteingles', 'phonehouse', 'leroymerlin', 'macway',
    'tiendanimal', 'eprice', 'bulevip', 'empik', 'clubefashion',
    'carrefour', 'leclerc', 'quirumedes', 'pccomponentes',
    'conrad', 'bigbang', 'ventunique', 'worten', 'conforama',
    'ubaldi', 'mediamarkt', 'bricodepot', 'zooplus', 'truffaut',
    'alltricks', 'castorama', 'conforamaiberia', 'culturafr', 'tdmp'
];
```

### Special Marketplaces

#### Fnac
- **Country**: France
- **Specialties**: Electronics, books, culture
- **Processing**: Customized for French products

#### Cdiscount
- **Country**: France
- **Specialties**: Electronics, home, gardening
- **Processing**: Specific commission system

#### DocMorris
- **Country**: Germany/France
- **Specialties**: Pharmacy, health
- **Processing**: Specific validations for pharmaceutical products

#### ManoMano
- **Country**: France/Spain/Italy
- **Specialties**: DIY, gardening
- **Processing**: Specific calculations for DIY products

#### Stockly
- **Country**: France
- **Specialties**: Fashion, lifestyle
- **Processing**: Dynamic stock system

### Marketplaces in Migration

#### Next to Implement
```php
$marketplacesAmigrar = [
    'castorama', 'xxxlgroup', 'axelspringerde', 'iberia',
    'wook', 'miravia', 'aliexpress', 'makro', 'kaufland'
];
```

---

## 🗄️ Database

### Configuration

#### Development
```php
'default' => env('DB_CONNECTION', 'sqlite'),
'sqlite' => [
    'driver' => 'sqlite',
    'database' => env('DB_DATABASE', database_path('database.sqlite')),
    'prefix' => '',
    'foreign_key_constraints' => env('DB_FOREIGN_KEYS', true),
]
```

#### Production
```php
'mysql' => [
    'driver' => 'mysql',
    'host' => env('DB_HOST', '127.0.0.1'),
    'port' => env('DB_PORT', '3306'),
    'database' => env('DB_DATABASE', 'laravel'),
    'username' => env('DB_USERNAME', 'root'),
    'password' => env('DB_PASSWORD', ''),
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
]
```

### Main Tables

#### users
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255),
    email VARCHAR(255) UNIQUE,
    email_verified_at TIMESTAMP,
    password VARCHAR(255),
    token VARCHAR(255),
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

#### user_clients
```sql
CREATE TABLE user_clients (
    id BIGINT PRIMARY KEY,
    user_id BIGINT,
    name VARCHAR(255),
    email VARCHAR(255),
    commission_rate DECIMAL(5,2),
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### user_products
```sql
CREATE TABLE user_products (
    id BIGINT PRIMARY KEY,
    user_id BIGINT,
    ean VARCHAR(255),
    sku VARCHAR(255),
    price DECIMAL(10,2),
    stock INTEGER,
    channel_id BIGINT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (channel_id) REFERENCES product_channels(id)
);
```

#### product_channels
```sql
CREATE TABLE product_channels (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255),
    marketplace VARCHAR(255),
    commission_rate DECIMAL(5,2),
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

#### flow_orders
```sql
CREATE TABLE flow_orders (
    id BIGINT PRIMARY KEY,
    order_id VARCHAR(255),
    user_id BIGINT,
    marketplace VARCHAR(255),
    status VARCHAR(50),
    total_price DECIMAL(10,2),
    commission DECIMAL(10,2),
    processed_at TIMESTAMP,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### Relationships

#### Simplified ER Diagram
```
users (1) ←→ (N) user_clients
users (1) ←→ (N) user_products
users (1) ←→ (N) flow_orders
product_channels (1) ←→ (N) user_products
```

### Recommended Indexes

```sql
-- Performance indexes
CREATE INDEX idx_user_products_ean ON user_products(ean);
CREATE INDEX idx_user_products_price ON user_products(price);
CREATE INDEX idx_user_products_channel ON user_products(channel_id);
CREATE INDEX idx_user_products_stock ON user_products(stock);
CREATE INDEX idx_flow_orders_marketplace ON flow_orders(marketplace);
CREATE INDEX idx_flow_orders_status ON flow_orders(status);
```

---

## ⚙️ Configuration and Deployment

### Environment Variables

#### Application
```bash
APP_NAME="Apolus Service"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://apolus-service.com
APP_KEY=base64:your-app-key-here
```

#### Database
```bash
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=apolus_service
DB_USERNAME=apolus_user
DB_PASSWORD=secure_password
```

#### External APIs
```bash
# BigHub
BIGHUB_API_KEY=7e0913c6-015d-4c60-9c49-3e4837922e53
BIGHUB_BEARER_TOKEN=bCZpeQAcwEIcm7y1hYCK3VspKQfWJ5IlgalrUqq2f60552dd
BIGHUB_BASE_URL=https://app.bighub.store/

# Marketplaces
FNAC_API_KEY=your-fnac-api-key
CDISCOUNT_API_KEY=your-cdiscount-api-key
DOCMORRIS_API_KEY=your-docmorris-api-key
MANOMANO_API_KEY=your-manomano-api-key
STOCKLY_API_KEY=your-stockly-api-key
```

#### Cache and Session
```bash
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
```

### Nginx Configuration

```nginx
server {
    listen 80;
    server_name apolus-service.com;
    root /var/www/apolus-service/laravel/app/public;
    
    index index.php;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Handle requests
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # PHP processing
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        
        # Security
        fastcgi_hide_header X-Powered-By;
    }
    
    # Static files
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Deny access to sensitive files
    location ~ /\. {
        deny all;
    }
    
    location ~ /(storage|bootstrap/cache) {
        deny all;
    }
}
```

### Deployment Script

```bash
#!/bin/bash
# deploy.sh

echo "Starting deployment..."

# Pull latest code
git pull origin main

# Install dependencies
composer install --no-dev --optimize-autoloader

# Clear caches
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Run migrations
php artisan migrate --force

# Cache configuration
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Restart services
sudo systemctl reload nginx
sudo systemctl restart php8.2-fpm

echo "Deployment completed!"
```

---

## 🔒 Security

### Authentication

#### API Authentication
```php
// Bearer Token for BigHub
const BEAR = 'bCZpeQAcwEIcm7y1hYCK3VspKQfWJ5IlgalrUqq2f60552dd';

// Request headers
$request_headers[] = 'Authorization: Bearer '.self::BEAR;
$request_headers[] = 'x-api-key:'.self::XAPIKEY;
```

#### Input Validation
```php
// FtpTrackingController
$validator = Validator::make($request->all(), [
    'order' => 'required|string',
    'tracking_nr' => 'required|string',
    'carrier' => 'required|string',
    'carrier_url' => 'required|string|url',
    'date' => 'nullable|string',
]);
```

### Implemented Security Measures

#### 1. **Data Validation**
- Input validation on all endpoints
- Data sanitization before processing
- Type and format validation

#### 2. **Security Headers**
```php
// Nginx configuration
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
```

#### 3. **Rate Limiting**
```php
// Implement rate limiting in controllers
Route::middleware(['throttle:60,1'])->group(function () {
    Route::get('/set-user-to-user', [OrderController::class, 'processUnassignedOrders']);
});
```

#### 4. **Audit Logs**
```php
// Log all critical operations
Log::info('Order processed', [
    'order_id' => $orderId,
    'user_id' => $userId,
    'marketplace' => $marketplace,
    'timestamp' => now()
]);
```

#### 5. **Sensitive File Protection**
```nginx
# Deny access to sensitive files
location ~ /\. {
    deny all;
}

location ~ /(storage|bootstrap/cache) {
    deny all;
}
```

### Security Recommendations

#### 1. **Key Rotation**
- Implement automatic API key rotation
- Monitor key usage for anomaly detection

#### 2. **Monitoring**
- Implement alerts for unauthorized access attempts
- Monitor error logs and exceptions

#### 3. **Backup and Recovery**
- Regular database backups
- Test recovery procedures

---

## 📊 Monitoring and Logs

### Log Structure

#### Application Logs
```php
// storage/logs/laravel.log
[2025-01-15 10:30:45] local.INFO: Order processed successfully
[2025-01-15 10:30:46] local.ERROR: Failed to connect to BigHub API
[2025-01-15 10:30:47] local.WARNING: No seller found for EAN: 1234567890123
```

#### System Logs
```bash
# Nginx logs
/var/log/nginx/access.log
/var/log/nginx/error.log

# PHP-FPM logs
/var/log/php8.2-fpm.log

# System logs
/var/log/syslog
```

### Important Metrics

#### 1. **Performance**
- API response time
- Order processing rate
- Memory and CPU usage

#### 2. **Business**
- Orders processed per hour
- Seller matching success rate
- Calculated commissions

#### 3. **System**
- Service availability
- External API integration errors
- Server resource usage

### Recommended Alerts

#### 1. **Critical**
- BigHub API connection failure
- Database error
- Server unavailable

#### 2. **Important**
- Error rate > 5%
- Response time > 5 seconds
- Order processing failure

#### 3. **Informational**
- Order processing completed
- Backup completed successfully
- Deployment completed

### Monitoring Dashboard

#### Real-time Metrics
```php
// Example metrics endpoint
Route::get('/api/metrics', function () {
    return response()->json([
        'orders_processed_today' => FlowOrders::whereDate('created_at', today())->count(),
        'active_sellers' => UserClient::where('active', true)->count(),
        'api_response_time' => $this->getApiResponseTime(),
        'system_uptime' => $this->getSystemUptime(),
        'last_error' => $this->getLastError(),
    ]);
});
```

---

## 🔧 Troubleshooting

### Common Issues

#### 1. **BigHub API Connection Error**

**Symptoms**:
```
cURL error 7: Failed to connect to connect-big.bighub.store
```

**Solutions**:
```bash
# Check connectivity
curl -I https://connect-big.bighub.store/api/orders/user/33

# Check DNS
nslookup connect-big.bighub.store

# Check firewall
sudo ufw status
```

#### 2. **Orders Not Processed**

**Symptoms**:
- Orders remain in "unassigned" status
- Logs show "No seller found"

**Diagnosis**:
```php
// Check if there are active sellers
$activeSellers = UserClient::where('active', true)->count();

// Check available products
$availableProducts = UserProduct::where('stock', '>', 0)->count();

// Check EAN matching
$eanMatch = UserProduct::where('ean', $targetEan)->first();
```

**Solutions**:
- Verify sellers are active
- Check available stock
- Validate EAN codes
- Adjust price tolerance

#### 3. **Performance Issues**

**Symptoms**:
- Slow response time
- Request timeouts
- High CPU/memory usage

**Diagnosis**:
```bash
# Check resource usage
top
htop
free -h
df -h

# Check error logs
tail -f /var/log/nginx/error.log
tail -f storage/logs/laravel.log
```

**Solutions**:
- Optimize database queries
- Implement Redis cache
- Adjust PHP-FPM settings
- Scale server resources

#### 4. **Marketplace Integration Errors**

**Symptoms**:
- Failure to process orders from specific marketplace
- External API authentication error

**Diagnosis**:
```php
// Check API keys
$apiKey = env('FNAC_API_KEY');
$bearerToken = env('BIGHUB_BEARER_TOKEN');

// Test connectivity
$response = $this->testApiConnection($marketplace);
```

**Solutions**:
- Verify API keys
- Validate endpoint URLs
- Check data format
- Contact marketplace support

### Diagnostic Commands

#### 1. **Check System Status**
```bash
# Service status
sudo systemctl status nginx
sudo systemctl status php8.2-fpm
sudo systemctl status redis

# Check ports
sudo netstat -tlnp | grep :80
sudo netstat -tlnp | grep :3306
```

#### 2. **Check Logs**
```bash
# Application logs
tail -f storage/logs/laravel.log

# System logs
sudo journalctl -u nginx -f
sudo journalctl -u php8.2-fpm -f
```

#### 3. **Test Connectivity**
```bash
# Test BigHub API
curl -H "Authorization: Bearer bCZpeQAcwEIcm7y1hYCK3VspKQfWJ5IlgalrUqq2f60552dd" \
     https://connect-big.bighub.store/api/orders/user/33

# Test database
php artisan tinker
>>> DB::connection()->getPdo();
```

### Recovery Procedures

#### 1. **Database Recovery**
```bash
# Backup before any operation
mysqldump -u root -p apolus_service > backup_$(date +%Y%m%d_%H%M%S).sql

# Restore backup
mysql -u root -p apolus_service < backup_20250115_103045.sql
```

#### 2. **Application Recovery**
```bash
# Rollback to previous version
git log --oneline
git checkout <commit-hash>

# Reinstall dependencies
composer install --no-dev --optimize-autoloader

# Clear cache
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

#### 3. **Server Recovery**
```bash
# Check disk space
df -h

# Clean old logs
sudo find /var/log -name "*.log" -mtime +30 -delete

# Restart services
sudo systemctl restart nginx
sudo systemctl restart php8.2-fpm
sudo systemctl restart redis
```

---

## 📚 Conclusion

This complete technical documentation covers all aspects of the Apolus Service system, from architecture to operational procedures. The system is robust and scalable, with support for multiple marketplaces and intelligent order processing.

### Strengths
- Modular and extensible architecture
- Support for 30+ marketplaces
- Intelligent automatic processing
- Robust external API integration
- Comprehensive logging and monitoring system

### Areas for Improvement
- Implement more comprehensive automated tests
- Add more monitoring metrics
- Implement more aggressive caching
- Add more security validations

### Next Steps
1. Implement CI/CD pipeline
2. Add more marketplaces
3. Improve alert system
4. Implement automated backup

---

*Technical Documentation v2.0 - January 2025*  
*System: Apolus Service v1.0*  
*Last updated: 15/01/2025*
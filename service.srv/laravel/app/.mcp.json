{"mcpServers": {"mcp-mysql-bighub": {"command": "/home/<USER>/Projects/mcp/mcp-mysql/mcp-mysql", "args": ["serve", "-c", "/home/<USER>/Projects/mcp/mcp-mysql/config.yml", "-i", "mysql-server-bighub"]}, "mcp-mysql-forex": {"command": "/home/<USER>/Projects/mcp/mcp-mysql/mcp-mysql", "args": ["serve", "-c", "/home/<USER>/Projects/mcp/mcp-mysql/config.yml", "-i", "mysql-server-forex"]}, "mcp-postgres-bighub": {"command": "/home/<USER>/Projects/mcp/mcp-postgres/mcp-postgres", "args": ["serve", "-c", "/home/<USER>/Projects/mcp/mcp-postgres/config.yml", "-i", "postgres-server-bighub"]}}}
# 🚀 Instruções de Deploy - FTP Tracking API

## 📦 Arquivos para Enviar para Nuvem

### 1. **Controller**
```
app/Http/Controllers/FtpTrackingController.php
```

### 2. **Rotas**
```
routes/web.php
```

### 3. **Middleware CSRF** ⚠️ IMPORTANTE
```
app/Http/Middleware/VerifyCsrfToken.php
```

### 4. **Variáveis de Ambiente**
Adicionar no arquivo `.env` da produção:
```bash
# FTP Configuration
FTP_HOST=ftp.bigsales.pt
FTP_PORT=21
FTP_USER=u931739318.stockly
FTP_PASSWORD="&RNBd7rV7kK#/Yk8"
```

---

## ⚙️ Comandos para Executar na Nuvem

Após fazer upload dos arquivos, execute na nuvem:

```bash
# Navegar para o diretório da aplicação
cd /caminho/do/projeto/service.srv/laravel/app

# Limpar todos os caches
php artisan route:clear
php artisan config:clear
php artisan cache:clear
php artisan view:clear

# Recriar caches (opcional, mas recomendado)
php artisan config:cache
php artisan route:cache

# Reiniciar serviços
sudo systemctl restart php8.2-fpm  # ou php-fpm
sudo systemctl restart nginx
```

---

## ✅ Testes Pós-Deploy

### 1. **Testar rota de health check**
```bash
curl https://apolus.bighub.store/api/tracking/test
```

**Resposta esperada:**
```json
{
  "status": "ok",
  "message": "FTP Tracking API is working",
  "timestamp": "2025-10-13 15:30:00"
}
```

### 2. **Testar upload de tracking**
```bash
curl -X POST https://apolus.bighub.store/api/tracking/upload \
  -H "Content-Type: application/json" \
  -d '{
    "order": "01",
    "tracking_nr": "TEST123",
    "carrier": "DHL",
    "carrier_url": "https://www.dhl.com/pt-pt/home/<USER>/tracking-express.html?submit=1&tracking-id=TEST123",
    "date": "17/02/2025"
  }'
```

**Resposta esperada (sucesso):**
```json
{
  "success": true,
  "message": "Tracking information uploaded successfully",
  "data": {
    "order": "01",
    "tracking_nr": "TEST123",
    "carrier": "DHL",
    "carrier_url": "https://www.dhl.com/pt-pt/home/<USER>/tracking-express.html?submit=1&tracking-id=TEST123",
    "date": "17/02/2025",
    "filename": "tracking-01-17022025.csv"
  }
}
```

---

## 🔍 Troubleshooting

### Erro 404 - Not Found

**Causa:** Rotas não foram atualizadas ou cache não foi limpo.

**Solução:**
```bash
php artisan route:clear
php artisan route:cache
php artisan route:list | grep tracking
```

### Erro 419 - CSRF Token Mismatch

**Causa:** Middleware CSRF não foi atualizado.

**Solução:**
1. Verificar se o arquivo `app/Http/Middleware/VerifyCsrfToken.php` foi enviado
2. Confirmar que contém as exclusões:
```php
protected $except = [
    'support-assign-orders',
    '/support-assign-orders',
    'api/tracking/upload',
    '/api/tracking/upload'
];
```
3. Limpar cache: `php artisan config:clear`

### Erro 500 - Internal Server Error

**Causa:** Erro no código ou credenciais FTP incorretas.

**Solução:**
1. Ver logs do Laravel:
```bash
tail -f storage/logs/laravel.log
```

2. Ver logs do Nginx:
```bash
tail -f /var/log/nginx/error.log
```

3. Verificar credenciais FTP no `.env`

### Arquivo não aparece no FTP

**Causa:** Credenciais FTP incorretas ou permissões.

**Solução:**
1. Testar conexão FTP manualmente:
```bash
ftp ftp.bigsales.pt
# Usar as credenciais do .env
# Tentar acessar /orders/tracking/
```

2. Verificar logs: `tail -f storage/logs/laravel.log`

---

## 📝 Checklist de Deploy

- [ ] Fazer backup dos arquivos atuais da produção
- [ ] Enviar `FtpTrackingController.php`
- [ ] Enviar `web.php`
- [ ] Enviar `VerifyCsrfToken.php` ⚠️ **IMPORTANTE**
- [ ] Adicionar variáveis FTP no `.env`
- [ ] Executar `php artisan route:clear`
- [ ] Executar `php artisan config:clear`
- [ ] Executar `php artisan cache:clear`
- [ ] Executar `php artisan route:cache`
- [ ] Reiniciar PHP-FPM
- [ ] Reiniciar Nginx
- [ ] Testar rota GET `/api/tracking/test`
- [ ] Testar rota POST `/api/tracking/upload`
- [ ] Verificar se arquivo CSV foi criado no FTP
- [ ] Verificar logs para erros

---

## 📞 Suporte

Se após seguir todos os passos o erro persistir:

1. Capture os logs:
```bash
tail -100 storage/logs/laravel.log > ~/tracking-api-error.log
tail -100 /var/log/nginx/error.log >> ~/tracking-api-error.log
```

2. Liste as rotas:
```bash
php artisan route:list > ~/tracking-routes.log
```

3. Verifique a configuração:
```bash
php artisan config:show | grep -i ftp > ~/tracking-config.log
```

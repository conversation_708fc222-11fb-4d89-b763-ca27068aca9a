# API de Upload de Tracking para FTP

## Endpoint
```
POST /ftp_tracking.php
```

**URL Completa:** `https://apolus.bighub.store/ftp_tracking.php`

⚠️ **Nota:** Este endpoint é um arquivo PHP direto na pasta `public/`, não usa rotas do Laravel (evita problemas de cache/permissão).

## Nomenclatura do Arquivo CSV

⚠️ **IMPORTANTE:** O nome do arquivo é gerado **automaticamente** baseado nos campos do payload.

**Padrão:** `tracking-{order}-{ddmmyyyy}.csv`

Onde:
- `{order}` = valor enviado no campo "order" do payload
- `{ddmmyyyy}` = data sem barras (exemplo: 17/02/2025 → 17022025)

**Exemplo:**
- Payload com `"order": "01"` e `"date": "17/02/2025"` → gera `tracking-01-17022025.csv`
- Payload com `"order": "STKLY-001"` e `"date": "20/03/2025"` → gera `tracking-STKLY-001-20032025.csv`

## Estrutura do CSV Gerado

### Header
```
order,tracking_nr,carrier,carrier_url,date
```

### Exemplo de Conteúdo
```csv
order,tracking_nr,carrier,carrier_url,date
01,TK123456789,DHL,https://www.dhl.com/pt-pt/home/<USER>/tracking-express.html?submit=1&tracking-id=TK123456789,17/02/2025
```

## Payload da Requisição

### Campos Obrigatórios
```json
{
  "order": "01",
  "tracking_nr": "TK123456789",
  "carrier": "DHL",
  "carrier_url": "https://www.dhl.com/pt-pt/home/<USER>/tracking-express.html?submit=1&tracking-id=TK123456789"
}
```

### Campos Opcionais
```json
{
  "date": "17/02/2025"
}
```
**Nota:** Se `date` não for enviado, será usado a data atual no formato DD/MM/YYYY

## Exemplos de Requisições

### Exemplo 1: Com data especificada
```bash
curl -X POST https://apolus.bighub.store/ftp_tracking.php \
  -H "Content-Type: application/json" \
  -d '{
    "order": "01",
    "tracking_nr": "TK123456789",
    "carrier": "DHL",
    "carrier_url": "https://www.dhl.com/pt-pt/home/<USER>/tracking-express.html?submit=1&tracking-id=TK123456789",
    "date": "17/02/2025"
  }'
```

**Arquivo gerado:** `tracking-01-17022025.csv`

**Resposta de Sucesso:**
```json
{
  "success": true,
  "message": "Tracking information uploaded successfully",
  "data": {
    "order": "01",
    "tracking_nr": "TK123456789",
    "carrier": "DHL",
    "carrier_url": "https://www.dhl.com/pt-pt/home/<USER>/tracking-express.html?submit=1&tracking-id=TK123456789",
    "date": "17/02/2025",
    "filename": "tracking-01-17022025.csv"
  }
}
```

### Exemplo 2: Sem data (usa data atual)
```bash
curl -X POST https://apolus.bighub.store/ftp_tracking.php \
  -H "Content-Type: application/json" \
  -d '{
    "order": "02",
    "tracking_nr": "ABC987654321",
    "carrier": "CTT",
    "carrier_url": "https://www.ctt.pt/feapl_2/app/open/postalObjectSearch.jspx?lang=01&objects=ABC987654321"
  }'
```

**Arquivo gerado:** `tracking-02-13102025.csv` (onde 13102025 = data atual do servidor)

### Exemplo 3: Com FedEx
```bash
curl -X POST https://apolus.bighub.store/ftp_tracking.php \
  -H "Content-Type: application/json" \
  -d '{
    "order": "03",
    "tracking_nr": "FDX555666777",
    "carrier": "FedEx",
    "carrier_url": "https://www.fedex.com/en-pt/tracking.html?trknbr=FDX555666777",
    "date": "20/02/2025"
  }'
```

**Arquivo gerado:** `tracking-03-20022025.csv`

### Exemplo 4: Múltiplos pedidos (mesmo dia)
```bash
# Pedido 1 - Order: 01
curl -X POST https://apolus.bighub.store/ftp_tracking.php \
  -H "Content-Type: application/json" \
  -d '{"order": "01", "tracking_nr": "TK111", "carrier": "DHL", "carrier_url": "https://www.dhl.com/pt-pt/home/<USER>/tracking-express.html?submit=1&tracking-id=TK111", "date": "17/02/2025"}'

# Pedido 2 - Order: 02
curl -X POST https://apolus.bighub.store/ftp_tracking.php \
  -H "Content-Type: application/json" \
  -d '{"order": "02", "tracking_nr": "TK222", "carrier": "UPS", "carrier_url": "https://www.ups.com/track?loc=pt_PT&tracknum=TK222", "date": "17/02/2025"}'

# Pedido 3 - Order: 03
curl -X POST https://apolus.bighub.store/ftp_tracking.php \
  -H "Content-Type: application/json" \
  -d '{"order": "03", "tracking_nr": "TK333", "carrier": "TNT", "carrier_url": "https://www.tnt.com/express/en_pt/site_tools/tracking.html?searchType=con&cons=TK333", "date": "17/02/2025"}'
```

**Arquivos gerados:** (todos com a mesma data pois o campo "date" é o mesmo)
- `tracking-01-17022025.csv` (order=01, date=17/02/2025)
- `tracking-02-17022025.csv` (order=02, date=17/02/2025)
- `tracking-03-17022025.csv` (order=03, date=17/02/2025)

## Respostas de Erro

### Erro 422: Validação
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "carrier_url": ["The carrier_url field is required."]
  }
}
```

### Erro 500: Falha no Upload FTP
```json
{
  "success": false,
  "message": "Failed to upload tracking information",
  "error": "FTP login failed. Please check credentials."
}
```

## Localização do Arquivo no FTP
Os arquivos CSV são enviados para:
```
/orders/tracking/{filename}
```

**Estrutura no FTP:**
```
/ (raiz)
└── orders/
    └── tracking/
        ├── tracking-01-17022025.csv    ← order=01, date=17/02/2025
        ├── tracking-02-17022025.csv    ← order=02, date=17/02/2025
        ├── tracking-03-20022025.csv    ← order=03, date=20/02/2025
        └── tracking-04-13102025.csv    ← order=04, date=13/10/2025
```

**Nota:** O nome do arquivo depende **exclusivamente** dos valores enviados nos campos `order` e `date` do payload.

## Configuração do FTP (.env)
```bash
FTP_HOST=ftp.bigsales.pt
FTP_PORT=21
FTP_USER=u931739318.stockly
FTP_PASSWORD="&RNBd7rV7kK#/Yk8"
```

## Logs
Todos os uploads são registrados no log do Laravel:
```bash
# Ver logs em tempo real
php artisan pail

# Ver logs do arquivo
tail -f storage/logs/laravel.log
```

**Exemplo de Log de Sucesso:**
```
[2025-02-17 10:30:45] local.INFO: FTP Upload Success {"host":"ftp.bigsales.pt","directory":"/orders/tracking","filename":"tracking-01-17022025.csv"}
```

## Testes

### Testar localmente com servidor de desenvolvimento
```bash
# Iniciar servidor Laravel
cd service.srv/laravel/app
php artisan serve

# Em outro terminal, fazer requisição
curl -X POST http://localhost:8000/api/tracking/upload \
  -H "Content-Type: application/json" \
  -d '{"order": "01", "tracking_nr": "TEST123", "carrier": "DHL", "carrier_url": "https://www.dhl.com/pt-pt/home/<USER>/tracking-express.html?submit=1&tracking-id=TEST123", "date": "17/02/2025"}'
```

### Testar com Docker
```bash
# Subir containers
docker-compose up -d

# Fazer requisição
curl -X POST http://localhost:8082/api/tracking/upload \
  -H "Content-Type: application/json" \
  -d '{"order": "01", "tracking_nr": "TEST123", "carrier": "DHL", "carrier_url": "https://www.dhl.com/pt-pt/home/<USER>/tracking-express.html?submit=1&tracking-id=TEST123", "date": "17/02/2025"}'
```

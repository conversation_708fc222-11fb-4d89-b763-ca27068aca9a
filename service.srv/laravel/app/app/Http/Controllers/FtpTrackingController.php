<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Exception;

class FtpTrackingController extends Controller
{
    /**
     * Upload tracking information to FTP
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadTracking(Request $request)
    {
        try {
            // Validate incoming request
            $validator = Validator::make($request->all(), [
                'order' => 'required|string',
                'tracking_nr' => 'required|string',
                'carrier' => 'required|string',
                'carrier_url' => 'required|string|url',
                'date' => 'nullable|string', // Optional, will use current date if not provided
            ]);

            if ($validator->fails()) {
                Log::error('FTP Tracking Upload - Validation failed', [
                    'errors' => $validator->errors()->toArray(),
                    'payload' => $request->all()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();

            // Format date (use provided date or current date)
            $date = isset($data['date']) ? $data['date'] : date('d/m/Y');

            // Generate CSV content
            $csvContent = $this->generateCsvContent(
                $data['order'],
                $data['tracking_nr'],
                $data['carrier'],
                $data['carrier_url'],
                $date
            );

            // Generate filename: tracking-{order_number}-{ddmmyyyy}.csv
            $filename = $this->generateFilename($data['order'], $date);

            // Upload to FTP
            $this->uploadToFtp($csvContent, $filename);

            Log::info('FTP Tracking Upload - Success', [
                'order' => $data['order'],
                'tracking_nr' => $data['tracking_nr'],
                'carrier' => $data['carrier'],
                'carrier_url' => $data['carrier_url'],
                'filename' => $filename
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Tracking information uploaded successfully',
                'data' => [
                    'order' => $data['order'],
                    'tracking_nr' => $data['tracking_nr'],
                    'carrier' => $data['carrier'],
                    'carrier_url' => $data['carrier_url'],
                    'date' => $date,
                    'filename' => $filename
                ]
            ], 200);

        } catch (Exception $e) {
            Log::error('FTP Tracking Upload - Error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'payload' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to upload tracking information',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate filename in format: tracking-{order_number}-{ddmmyyyy}.csv
     * Example: tracking-01-17022025.csv
     *
     * @param string $order
     * @param string $date Date in format DD/MM/YYYY
     * @return string
     */
    private function generateFilename(string $order, string $date): string
    {
        // Convert date from DD/MM/YYYY to DDMMYYYY
        $dateParts = explode('/', $date);
        $dateFormatted = $dateParts[0] . $dateParts[1] . $dateParts[2]; // DDMMYYYY

        // Format: tracking-{order_number}-{ddmmyyyy}.csv
        return "tracking-{$order}-{$dateFormatted}.csv";
    }

    /**
     * Generate CSV content
     *
     * @param string $order
     * @param string $trackingNr
     * @param string $carrier
     * @param string $carrierUrl
     * @param string $date
     * @return string
     */
    private function generateCsvContent(
        string $order,
        string $trackingNr,
        string $carrier,
        string $carrierUrl,
        string $date
    ): string {
        $csv = "order,tracking_nr,carrier,carrier_url,date\n";
        $csv .= sprintf(
            "%s,%s,%s,%s,%s\n",
            $order,
            $trackingNr,
            $carrier,
            $carrierUrl,
            $date
        );

        return $csv;
    }

    /**
     * Upload CSV file to FTP server
     *
     * @param string $content
     * @param string $filename
     * @return void
     * @throws Exception
     */
    private function uploadToFtp(string $content, string $filename): void
    {
        // Get FTP credentials from environment
        $ftpHost = env('FTP_HOST');
        $ftpPort = env('FTP_PORT', 21);
        $ftpUser = env('FTP_USER');
        $ftpPassword = env('FTP_PASSWORD');

        // Validate FTP configuration
        if (!$ftpHost || !$ftpUser || !$ftpPassword) {
            throw new Exception('FTP configuration is incomplete. Please check .env file.');
        }

        // Create temporary file
        $tempFile = tempnam(sys_get_temp_dir(), 'ftp_tracking_');
        file_put_contents($tempFile, $content);

        // Connect to FTP server
        $ftpConnection = ftp_connect($ftpHost, $ftpPort, 30);

        if (!$ftpConnection) {
            unlink($tempFile);
            throw new Exception("Failed to connect to FTP server: {$ftpHost}:{$ftpPort}");
        }

        try {
            // Login to FTP server
            $login = ftp_login($ftpConnection, $ftpUser, $ftpPassword);

            if (!$login) {
                throw new Exception('FTP login failed. Please check credentials.');
            }

            // Enable passive mode
            ftp_pasv($ftpConnection, true);

            // Navigate to target directory: /orders/tracking/
            $targetDir = '/orders/tracking';

            // Create directory structure if it doesn't exist
            $this->ensureFtpDirectory($ftpConnection, '/orders');
            $this->ensureFtpDirectory($ftpConnection, '/orders/tracking');

            // Change to target directory
            if (!@ftp_chdir($ftpConnection, $targetDir)) {
                throw new Exception("Failed to change to directory: {$targetDir}");
            }

            // Upload file
            $upload = ftp_put($ftpConnection, $filename, $tempFile, FTP_ASCII);

            if (!$upload) {
                throw new Exception("Failed to upload file: {$filename}");
            }

            Log::info('FTP Upload Success', [
                'host' => $ftpHost,
                'directory' => $targetDir,
                'filename' => $filename
            ]);

        } finally {
            // Close FTP connection and remove temp file
            ftp_close($ftpConnection);
            unlink($tempFile);
        }
    }

    /**
     * Ensure FTP directory exists, create if not
     *
     * @param resource $ftpConnection
     * @param string $directory
     * @return void
     */
    private function ensureFtpDirectory($ftpConnection, string $directory): void
    {
        if (!@ftp_chdir($ftpConnection, $directory)) {
            // Directory doesn't exist, try to create it
            @ftp_mkdir($ftpConnection, $directory);
            @ftp_chdir($ftpConnection, $directory);
        }

        // Return to root
        @ftp_chdir($ftpConnection, '/');
    }
}

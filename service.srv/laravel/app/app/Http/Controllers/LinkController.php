<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Link;
use Illuminate\Support\Facades\DB;

class LinkController extends Controller
{
    public function __construct()
    {
        # $this->middleware('language');  // Aplica o middleware LanguageMiddleware em todos os métodos
    }

    public function checkToken($token)
    {
        $data_landingpage = DB::table('links')
            ->join('users', 'links.user_token', '=', 'users.token')
            ->join('agent_landing_page', 'links.user_token', '=', 'agent_landing_page.agent_token')
            ->where('links.token_link', $token)
            ->select('links.*', 'agent_landing_page.*', 'users.*')
            ->first();

        if (isset($data_landingpage->properties)) {
            // Decodifica o JSON em um array associativo
            $properties = json_decode($data_landingpage->properties, true);

            if (is_array($properties)) {
                // Inicializa o array para armazenar os dados de all_properties
                $all_properties = [];

                foreach ($properties as $key => $property) {
                    // Consulta a tabela crawler_properties para cada token
                    $property_data = DB::table('crawler_properties')
                        ->where('token', $property)
                        ->first(); // Use first() se espera apenas um resultado

                    // Adiciona o resultado no array
                    if ($property_data) {
                        $all_properties[] = $property_data; // Adiciona ao array apenas se encontrado
                    }
                }

                // Adiciona o array de all_properties ao objeto $data_landingpage
                $data_landingpage->all_properties = $all_properties;
            } else {
                dd('Erro: properties não é um JSON válido ou não pode ser decodificado.');
            }
        } else {
            dd('Erro: properties não está definido no objeto $data_landingpage.');
        }

        // Exibe os dados no dump para verificar se all_properties foi adicionado corretamente
        dd($data_landingpage);



        if ($data_landingpage) {
            // Renderizar uma view específica se o token for encontrado
            return view('link_view', ['data' => $data_landingpage]);
        } else {
            // Retornar um erro ou redirecionar se o token não for encontrado
            return response()->json(['error' => 'Token não encontrado'], 404);
        }
    }
}

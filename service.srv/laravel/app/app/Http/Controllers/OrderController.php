<?php

namespace App\Http\Controllers;

use App\Models\FlowBighub;
use App\Models\FlowFnac;
use App\Models\FlowCdiscount;
use App\Models\FlowDocMorris;
use App\Models\FlowManoMano;
use App\Models\FlowStockly;
use App\Models\ProductChannel;
use App\Models\UserClient;

use Illuminate\Http\Request;

class OrderController extends Controller
{
    protected $flowBighub;
    private $debug;
    protected $flowFnac;
    protected $flowCdiscount;
    protected $flowDocMorris;
    protected $flowManoMano;
    protected $flowStockly;
    private $user_id_debug;
    private $order_bySupport;

    /**
     * List of marketplaces that use the MIRAKL processor
     */
    protected $miraklMarketplaces = [
        'pixmania', 'shopapotheke', 'planetahuerto', 'rueducommerce', 'elcorteingles', 'phonehouse',
        'leroymerlin', 'macway', 'tiendanimal', 'eprice', 'bulevip', 'empik', 'clubefashion', 'carrefour',
        'leclerc', 'quirumedes', 'pccomponentes', 'conrad', 'bigbang', 'ventunique', 'worten', 'conforama',
        'ubaldi', 'mediamarkt', 'bricodepot', 'zooplus', 'truffaut', 'alltricks', 'castorama', 'conforamaiberia',
        'culturafr', 'tdmp'
    ];

    protected $marketplacesAmigrar = [
        'xxxlgroup', 'axelspringerde',
        'iberia', 'wook', 'miravia', 'aliexpress', 'makro', 'kaufland'
    ];


    /**
     * List of marketplaces that use specific processing
     */
    protected $specialMarketplaces = [
        'fnac','cdiscount','docmorris','manomano','makro', 'mktbighub','stockly'
    ];

    protected $id_marketplace_exception = [28, 57, 58];

    /**
     * Constructor with dependency injection
     *
     * @param FlowBighub $flowBighub
     * @param FlowFnac $flowFnac
     * @param FlowCdiscount $flowCdiscount
     * @param FlowDocMorris $flowDocMorris
     * @param FlowManoMano $flowManoMano
     * @param FlowStockly $flowStockly
     */
    public function __construct(FlowBighub $flowBighub, FlowFnac $flowFnac, FlowCdiscount $flowCdiscount, FlowDocMorris $flowDocMorris, FlowManoMano $flowManoMano, FlowStockly $flowStockly)
    {
        $this->flowBighub = $flowBighub;
        $this->flowFnac = $flowFnac;
        $this->flowCdiscount = $flowCdiscount;
        $this->flowDocMorris = $flowDocMorris;
        $this->flowManoMano = $flowManoMano;
        $this->flowStockly = $flowStockly;
        $this->debug = false;
        $this->user_id_debug = null;
        $this->order_bySupport = false;
    }


    public function processOrdersRequestedBySupport(Request $request)
    {
        try {
            $this->order_bySupport = true;

            // Access values sent in the request
            $channel = $request->input('channel');
            $idUsuario = $request->input('id_usuario');
            $orderType = $request->input('order_type');

            $orderIdTbl = $request->input('order_id');
            $userId = $request->input('client_id');

            \Log::info('processOrdersRequestedBySupport - Iniciando', [
                'order_id' => $orderIdTbl,
                'client_id' => $userId,
                'channel' => $channel,
                'id_usuario' => $idUsuario,
                'order_type' => $orderType
            ]);

            // Process the request...
            $resp = $this->processUnassignedOrders($orderIdTbl, $userId);

            \Log::info('processOrdersRequestedBySupport - Concluído com sucesso', [
                'order_id' => $orderIdTbl,
                'response' => $resp
            ]);

            // Return response (without manual CORS headers)
            return response()->json([
                'status' => 'success',
                'message' => 'Order successfully assigned to user',
                'resp' => $resp
            ]);
        } catch (\Exception $e) {
            \Log::error('processOrdersRequestedBySupport - Exception', [
                'order_id' => $request->input('order_id'),
                'client_id' => $request->input('client_id'),
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to process order: ' . $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ], 500);
        }
    }

    /**
     * Process orders without user or a specific order
     *
     * @param int|null $order_id Specific order ID (optional)
     * @param int|null $user_id User ID (optional)
     * @return \Illuminate\Http\Response
     */
    public function processUnassignedOrders($order_idTbl = null, $user_id = null)
    {
        $this->debug = true;
        $this->user_id_debug = $user_id; //user here

        \Log::info("Starting order processing");

        // Case 1: Specific order
        if ($order_idTbl !== null) {
            \Log::info("Processing specific order: $order_idTbl");

            // Search for specific order data by endpoint
            $order_data = $this->flowBighub->getOrdersByTableId($order_idTbl);

            if (!$order_data || empty($order_data)) {
                return response()->json(['message' => 'Order not found'], 404);
            }

            // Process only this specific order
            $result = $this->processSingleOrder($order_data->data[0], $this->user_id_debug);

            return response()->json([
                'message' => 'Specific order processing completed',
                'order_id' => $order_idTbl,
                'result' => $result
            ]);
        }

        // Case 2: Normal processing (all orders without user)
        \Log::info("Fetching orders to be processed.");

        if($this->debug){
            $orders_no_user_support = $this->flowBighub->curl_get_orders_no_user(25);
            $orders_no_user = $this->flowBighub->curl_get_orders_no_user(33);
            \Log::info("Validating orders in debug mode - ACTIVE - Development Team.");
        }else{
            $orders_no_user = $this->flowBighub->curl_get_orders_no_user(33);
            \Log::info("Validating orders in production mode.");
        }

        // Status counters
        $processed = 0;
        $failed = 0;
        $special_processed = 0;
        $waiting_acceptance = [];
        $waiting_debit_payment = [];
        $staging = [];
        $exceptions_mkt = [];
        $orders_support = [];
        $created = [];

        echo json_encode($orders_no_user);
        die();

        if (isset($orders_no_user_support->data)) {
            foreach ($orders_no_user_support->data as $order) {
                $orders_support[] = [
                    'order' => $order->order_id,
                    'channel' => $order->channel->code,
                    'eans' => $order->sku,
                    'price' => $order->price,
                    'order_created' => $order->order_created_at
                ];
                continue;
            }
        }


        if (!is_null($orders_no_user) && isset($orders_no_user->data)) {
            \Log::info("Total orders received: ".count($orders_no_user->data));
            foreach ($orders_no_user->data as $order) {

                // echo json_encode($order);
                // die();

                $result = $this->exploreCode($order->channel->code);
                if (in_array($order->marketplace->id, $this->id_marketplace_exception)) {
                    $exceptions_mkt[] = [
                        'order' => $order->order_id,
                        'channel' => $result['mkt'].'-'.$result['code'],
                        'price' => $order->price,
                        'order_created' => $order->order_created_at
                    ];
                    \Log::info("Skipped - Exception MKT: {$order->order_id}");
                    continue;
                }

                if($order->status == 'WAITING_ACCEPTANCE' || $order->status == 'WaitingAcceptance'){
                    $waiting_acceptance[] = [
                        'order' => $order->order_id,
                        'channel' => $result['mkt'].'-'.$result['code'],
                        'price' => $order->price,
                        'order_created' => $order->order_created_at
                    ];
                    \Log::info("Skipped - WAITING_ACCEPTANCE: {$order->order_id}");
                    continue;
                }

                if($order->status == 'WAITING_DEBIT_PAYMENT' || $order->status == 'WAITING_DEBIT' ){
                    $waiting_debit_payment[] = [
                        'order' => $order->order_id,
                        'channel' => $result['mkt'].'-'.$result['code'],
                        'price' => $order->price,
                        'order_created' => $order->order_created_at
                    ];
                    \Log::info("Skipped - WAITING_DEBIT: {$order->order_id}");
                    continue;
                }


                if($order->status == 'STAGING'){
                    $staging[] = [
                        'order' => $order->order_id,
                        'channel' => $result['mkt'].'-'.$result['code'],
                        'price' => $order->price,
                        'order_created' => $order->order_created_at
                    ];
                    \Log::info("Skipped - STAGING: {$order->order_id}");
                    continue;
                }

                if($order->status == 'Created'){
                    $created[] = [
                        'order' => $order->order_id,
                        'channel' => $result['mkt'].'-'.$result['code'],
                        'price' => $order->price,
                        'order_created' => $order->order_created_at
                    ];
                    \Log::info("Skipped - Created: {$order->order_id}");
                    continue;
                }


                // Skip invalid orders
                if (empty($order)) {
                    $failed++;
                    continue;
                }

                // Standardize order format
                if (is_array($order)) {
                    $order = json_decode(json_encode($order));
                }

                $marketplace = $order->marketplace->name;

                \Log::info("Processing order: {$order->order_id} - Marketplace: {$marketplace} - Status: {$order->status}");

               // Check which group the marketplace belongs to
                if (in_array($marketplace, $this->miraklMarketplaces)) {
                    // Process using unified function for standard marketplaces
                    \Log::info("MIRAKL Marketplace - ".$marketplace);
                    if ($this->processMarketplaceMirakls($order)) {
                        $processed++;
                    } else {
                        $failed++;
                    }
                } elseif (in_array($marketplace, $this->specialMarketplaces)) {
                    // Process using specific functions for special marketplaces
                    if ($this->processSpecialMarketplace($marketplace, $order)) {
                        $special_processed++;
                    } else {
                        $failed++;
                    }
                } else {
                    // Unrecognized marketplace
                    \Log::warning("Unsupported marketplace: {$marketplace} - Order ID: {$order->order_id}");
                    $failed++;
                }
            }
        } else {
            // Optional: log or return a friendly message
            echo json_encode([
                'message' => 'No orders found or invalid response.',
                'status' => 'warning'
            ]);
            die();
        }

        return response()->json([
            'message' => 'Processing completed',
            'marketplaces_actives' => $this->miraklMarketplaces,
            'especial_marketplaces_actives' => $this->specialMarketplaces,
            'mirakls_processed' => $processed,
            'special_processed' => $special_processed,
            'waiting_acceptance' => $waiting_acceptance,
            'waiting_debit_payment' => $waiting_debit_payment,
            'staging' => $staging,
            'support_25' => $orders_support,
            'exceptions_mkt' => $exceptions_mkt,
            'failed' => $failed
        ]);
    }


    private function exploreCode($code) {
        // Check if there is a hyphen in the code
        if (strpos($code, '-') !== false) {
            // Split the string using hyphen as separator
            $partes = explode('-', $code);

            // Retorna um array com as duas partes
            return [
                'mkt' => $partes[0],
                'code' => $partes[1]
            ];
        }

        // Return empty values if there is no hyphen
        return [
            'mkt' => '',
            'code' => ''
        ];
    }


    /**
     * Process a specific order
     *
     * @param object $order Dados do pedido
     * @param int|null $user_id ID do usuário (opcional)
     * @return array Resultado do processamento
     */
    private function processSingleOrder($order)
    {
        // Standardize order format
        if (is_array($order)) {
            $order = json_decode(json_encode($order));
        }

        $marketplace = $order->marketplace->name;
        $result = [];

        // Check which group the marketplace belongs to
        if (in_array($marketplace, $this->miraklMarketplaces)) {
            // Process using unified function for standard marketplaces
            \Log::info("Processing specific order for MIRAKL Marketplace - ".$marketplace);
            $success = $this->processMarketplaceMirakls($order);
            $result = [
                'success' => $success,
                'type' => 'mirakl',
                'marketplace' => $marketplace
            ];
        } elseif (in_array($marketplace, $this->specialMarketplaces)) {
            // Process using specific functions for special marketplaces
            \Log::info("Processing specific order for Special Marketplace - ".$marketplace);
            $success = $this->processSpecialMarketplace($marketplace, $order);
            $result = [
                'success' => $success,
                'type' => 'special',
                'marketplace' => $marketplace
            ];
        } else {
            // Unrecognized marketplace
            \Log::warning("Unsupported marketplace: {$marketplace} - Order ID: {$order->order_id}");
            $result = [
                'success' => false,
                'error' => 'Unsupported marketplace',
                'marketplace' => $marketplace
            ];
        }

        return $result;
    }


    /**
     * Process orders from different marketplaces in a simplified way
     */
    private function processMarketplaceMirakls($order, $users_id = [])
    {
        $start = microtime(true);
        \Log::info("Starting processMarketplaceMirakls for order: ".$order->order_id);

        $result = $this->exploreCode($order->channel->code);
        if($result['code'] == 'INIT'){
            $result['code'] = $order->marketplace->country;
        }

        // Extract basic order data
        $tblId = $order->id;
        $order_id = $order->order_id;
        $marketplace = $order->marketplace->name;

        $stepStart = microtime(true);
        $country_mkt = $this->flowBighub->getCountryMarketplace($result['code']);
        \Log::info("getCountryMarketplace took: ".(microtime(true) - $stepStart)."s");

        $valor_adicional_portes = 0;

        // Normalize marketplace name
        $channel = $this->normalizeMarketplaceName($marketplace);

        // Extract EAN according to marketplace
        $stepStart = microtime(true);
        $ean = $this->flowBighub->getEanCode($order->sku[0], $order, $channel);
        $eanTime = microtime(true) - $stepStart;
        \Log::info("getEanCode took: " . $eanTime . "s");
        \Log::info("Mirakl - After getEanCode, before accessing ean variable");
        \Log::info("Mirakl - EAN variable exists: " . (isset($ean) ? 'yes' : 'no'));
        if (isset($ean)) {
            \Log::info("Mirakl - Returned EAN: " . var_export($ean, true));
        }

        // Local variable to control if it's a watch (doesn't affect other orders)
        \Log::info("Mirakl - About to set is_watch_order and current_user_id");
        $is_watch_order = false;
        $current_user_id = $this->user_id_debug; // Preserve original support user_id
        \Log::info("Mirakl - Variables set, product_quantity: " . $order->product_quantity);

        // Check if it's an order with multiple products
        \Log::info("Mirakl - Checking product quantity condition");
        if ($order->product_quantity > 1) {
            \Log::info("Mirakl - Multiple products path");

            // Logic for multiple products
            $products = $order->response->order_lines;
            #$origin = $order->code_marketplace->code;

            foreach ($products as $prod) {

                // Search for EAN and validate if it's a watch
                if (isset($prod->product_title) && stripos($prod->product_title, 'relógio') !== false) {
                    $is_watch_order = true;
                    break; // If found a watch, no need to continue checking
                }
            }

            // If it's not a watch and doesn't have user_id defined by support, search for sellers
            if (!$is_watch_order && !$current_user_id) {
                \Log::info("Starting seller search for ".count($products)." products");
                foreach ($products as $prod) {
                    // Calculate price
                    #$price = round($this->aplicarImposto($prod->total_price, $origin) - $valor_adicional_portes, 2);
                    $price = $prod->total_price;

                    // Adjustment for quantity
                    if ($prod->quantity > 1) {
                        $price = ($prod->price / $prod->quantity) - $valor_adicional_portes;
                    }

                    // Search for seller
                    $price = number_format($price, 2, '.', '');
                    $stepStart = microtime(true);
                    $found_seller = $this->getSeller($ean, $channel, $price, $order_id, $tblId);
                    \Log::info("getSeller took: ".(microtime(true) - $stepStart)."s");

                    if (isset($found_seller) && $this->isUserActive($found_seller)) {
                        $users_id[] = $found_seller->user_id;
                    }
                }
            }

            // Define final user_id
            if ($is_watch_order) {
                $user_id = 843;
            } elseif ($current_user_id) {
                $user_id = $current_user_id;
            } else {
                $user_id = $this->getSingleSellerId($users_id);
            }

        } else {
            \Log::info("Mirakl - Single product path (else block)");

            #var_dump("{$channel} = 1 produto");

            // Logic for single product
            $price = $order->price - $valor_adicional_portes;
            \Log::info("Mirakl - Single product - Price calculated: " . $price);

            // Adjustment for quantity
            if (isset($order->response_first->order_lines[0]->quantity) && $order->response_first->order_lines[0]->quantity > 1) {
                $price = ($order->price / $order->response_first->order_lines[0]->quantity) - $valor_adicional_portes;
            }

            // Search for EAN and validate if it's a watch
            \Log::info("Mirakl - Single product - Checking if watch order");
            if (isset($order->response_first->order_lines[0]->product_title) && stripos($order->response_first->order_lines[0]->product_title, 'relógio') !== false) {
                $is_watch_order = true;
                \Log::info("Mirakl - Single product - IS a watch order");
            } else {
                \Log::info("Mirakl - Single product - NOT a watch order");
            }
            // var_dump($ean, $channel, $price, $order_id, $tblId);
            // die();

            // Define final user_id
            \Log::info("Mirakl - Single product - Determining user_id. is_watch: " . ($is_watch_order ? 'yes' : 'no') . ", current_user_id: " . ($current_user_id ?? 'null'));
            if ($is_watch_order) {
                $user_id = 843;
                \Log::info("Mirakl - Single product - Watch order, user_id = 843");
            } elseif ($current_user_id) {
                $user_id = $current_user_id;
                \Log::info("Mirakl - Single product - Using current_user_id: " . $user_id);
            } else {
                // Search for seller only if it's not a watch and doesn't have user_id defined
                \Log::info("Mirakl - Single product - Starting getSeller with EAN: {$ean}, channel: {$channel}, price: {$price}");
                $stepStart = microtime(true);
                $found_seller = $this->getSeller($ean, $channel, $price, $order_id, $tblId);
                \Log::info("getSeller took: " . (microtime(true) - $stepStart) . "s");
                $user_id = isset($found_seller) ? $found_seller->user_id : null;
                \Log::info("Mirakl - Single product - getSeller returned user_id: " . ($user_id ?? 'null'));
            }
        }

        // Finalize sending the order to the seller
        \Log::info("Mirakl - Checking if should send to seller. user_id: " . ($user_id ?? 'null'));
        if ($user_id && !in_array($user_id, [999999])) {
            \Log::info("Mirakl - Proceeding to send order to seller: " . $user_id);

            // echo json_encode($order);
            // die();

            $stepStart = microtime(true);
            $payload = $this->flowBighub->generatePayloadMirakls($channel, $order, $user_id, $country_mkt, $found_seller ?? null);
            \Log::info("generatePayloadMirakls took: ".(microtime(true) - $stepStart)."s");

            if($this->debug && !$this->order_bySupport){
                echo json_encode($payload);
                #die();
            }

            $stepStart = microtime(true);
            $insertResult = $this->flowBighub->insert_tbl_order($payload);
            \Log::info("insert_tbl_order took: ".(microtime(true) - $stepStart)."s");

            if($this->debug && !$this->order_bySupport){
                echo json_encode($insertResult);
            }

            if ($insertResult->status == 200) {
                \Log::info("Order: ".$order_id." inserida na tbl_order");

                $stepStart = microtime(true);
                $this->flowBighub->curl_put_update_order_user($tblId, $user_id);
                \Log::info("curl_put_update_order_user took: ".(microtime(true) - $stepStart)."s");
                \Log::info("Repassando a order ".$order_id." para o user_id: ".$user_id);

                $this->flowBighub->createSendEmail($user_id, $order_id, $channel.'-'.$country_mkt);
                \Log::info("Notificando o seller sobre uma venda, order: ".$order_id.", email enviado para o user_id: ".$user_id);

                $elapsed = microtime(true) - $start;
                \Log::info("processMarketplaceMirakls completed in {$elapsed}s for order: ".$order_id);

                return true;
            }else{
                \Log::info("Error ao inserir order na tbl_orders - ".$insertResult);
                echo 'Error ao inserir na tbl_orders';
                return  null;
            }
        } else {
            \Log::info("Mirakl - NOT sending to seller. user_id is null or 999999");
            echo 'Seller not found';
            return null;
        }

        return false;
    }


    /**
     * Normaliza o nome do marketplace
     */
    private function normalizeMarketplaceName($marketplace)
    {
        $marketplaceMap = [
            'carrefourfr' => 'carrefour',
            'shopapothekeat' => 'shopapotheke',
            'pccompes' => 'pccomp',
            'pccompit' => 'pccomp',
            'pccompfr' => 'pccomp',
            'pccomponentes' => 'pccomp',
            'leclerc' => 'eleclerc',
            'conforamaiberia' => 'conforama',
            'stockly' => 'mktbighub',
            'culturafr' => 'cultura'
        ];

        return $marketplaceMap[$marketplace] ?? $marketplace;
    }

    /**
     * Verifica se o usuário está ativo
     */
    private function isUserActive($seller)
    {
        return !isset($seller->user_active) || $seller->user_active == 1;
    }

    /**
     * Retorna um único ID de vendedor se todos forem iguais
     */
    private function getSingleSellerId($users_id)
    {
        if (empty($users_id)) {
            return null;
        }

        return $this->validarValoresIguais($users_id) ? $users_id[0] : 999999;
    }

	private function validarValoresIguais($array)
	{
		// Check if array is empty
		if (empty($array)) {
			return true;
		}

		// Get first value from array
		$primeiro_valor = reset($array);

		// Check if all values are equal to the first value
		foreach ($array as $valor) {
			if ($valor !== $primeiro_valor) {
				return false;
			}
		}

		// If all values are equal, return true
		return true;
	}

    public function getSeller($ean, $channel, $price, $order_id = null, $tbl_id = null)
    {
        // var_dump($ean, $channel, $price, $order_id, $tbl_id);
        //

        if($channel == 'stockly'){
             // Search for seller with exact price
            $offer = ProductChannel::where('ean', $ean)
                ->where('user_id', '!=', 120)
                ->where('is_active', 1)
                ->where('stock', '>', 0)
                ->where('price', $price)
                ->orderByDesc('price')
                ->first();

            if ($offer) {
                return $this->getSellerDetails($offer);
            }

            // Search for seller with price less than or equal
            $offer = ProductChannel::where('ean', $ean)
                ->where('user_id', '!=', 120)
                ->where('is_active', 1)
                ->where('stock', '>', 0)
                ->where('price', '<=', $price)
                ->orderBy('price')
                ->first();

            if ($offer) {
                return $this->getSellerDetails($offer);
            }

            // Search for seller with price up to 5% higher
            $priceMax = $price * 1.05;
            $offer = ProductChannel::where('ean', $ean)
                ->where('user_id', '!=', 120)
                ->where('is_active', 1)
                ->where('stock', '>', 0)
                ->where('price', '<=', $priceMax)
                ->orderBy('price')
                ->first();

            if ($offer) {
                return $this->getSellerDetails($offer);
            }
        }


        // Search for seller with exact price
        $offer = ProductChannel::where('ean', $ean)
            ->where('channel', $channel)
            ->where('user_id', '!=', 120)
            ->where('is_active', 1)
            ->where('stock', '>', 0)
            ->where('price', $price)
            ->orderByDesc('price')
            ->first();

        if ($offer) {
            return $this->getSellerDetails($offer);
        }

        // Search for seller with price less than or equal
        $offer = ProductChannel::where('ean', $ean)
            ->where('channel', $channel)
            ->where('user_id', '!=', 120)
            ->where('is_active', 1)
            ->where('stock', '>', 0)
            ->where('price', '<=', $price)
            ->orderBy('price')
            ->first();

        if ($offer) {
            return $this->getSellerDetails($offer);
        }

        // Search for seller with price up to 5% higher
        $priceMax = $price * 1.05;
        $offer = ProductChannel::where('ean', $ean)
            ->where('channel', $channel)
            ->where('user_id', '!=', 120)
            ->where('is_active', 1)
            ->where('stock', '>', 0)
            ->where('price', '<=', $priceMax)
            ->orderBy('price')
            ->first();

        if ($offer) {
            return $this->getSellerDetails($offer);
        }

        // echo 'vai update';
        // die();

        // Special case to forward to support
        if ($channel != 'Miravia' && $this->debug) {
            $this->flowBighub->curl_put_update_order_user($tbl_id, 25);
            // Colocar active
            \Log::info("Forwarding order ".$order_id." to support.");
            return null;
        }
    }

    /**
     * Adiciona detalhes do vendedor ao objeto de oferta
     */
    private function getSellerDetails($offer)
    {
        $seller = UserClient::select('delivery_by', 'user_active')
            ->find($offer->user_id);

        if ($seller) {
            $offer->delivery_by = $seller->delivery_by;
            $offer->user_active = $seller->user_active;
        } else {
            $offer->delivery_by = null;
            $offer->user_active = null;
        }

        return $offer;
    }

    /**
     * Process special marketplaces that need different logic
     *
     * @param string $marketplace
     * @param object $order
     * @return bool
     */
    private function processSpecialMarketplace($marketplace, $order)
    {

        // Route to specific method based on marketplace
        switch ($marketplace) {
            case 'fnac':
                return $this->processFnacOrder($order);

            case 'cdiscount':
                return $this->processCdiscountOrder($order);

            case 'docmorris':
                return $this->processDocMorrisOrder($order);

            case 'manomano':
                return $this->processManomanoOrder($order);

            case 'stockly':
                return $this->processStocklyOrder($order);

            default:
                return false;
        }
    }

    /**
     * Process Cdiscount orders
     */
    private function processCdiscountOrder($order)
    {
        $result = $this->exploreCode($order->channel->code);
        if ($result['code'] == 'INIT') {
            $result['code'] = $order->marketplace->country;
        }

        // Extract basic order data
        $tblId = $order->id;
        $order_id = $order->order_id;
        $marketplace = $order->marketplace->name;
        $country_mkt = $this->flowBighub->getCountryMarketplace($order->channel->code);
        $valor_adicional_portes = 0;

        // Normalize marketplace name
        $channel = $this->normalizeMarketplaceName($marketplace);

        // Extract EAN according to marketplace
        $ean = $this->flowBighub->getEanCode($order->sku[0], $order, $channel);

        #Log para debug
        // var_dump("entrou {$channel} - order: {$order_id} - country: {$country_mkt}");
        // die();

        // Local variable to control if it's a watch (doesn't affect other orders)
        $is_watch_order = false;
        $current_user_id = $this->user_id_debug; // Preserve original support user_id

        // Check if it's an order with multiple products
        if ($order->product_quantity > 1) {

            // Logic for multiple products
            $products = $order->response->response->lines;

            // First check if any product is a watch
            foreach ($products as $prod) {
                if (isset($prod->label) && stripos($prod->label, 'relógio') !== false) {
                    $is_watch_order = true;
                    break; // If found a watch, no need to continue checking
                }
            }

            // If it's not a watch and doesn't have user_id defined by support, search for sellers
            if (!$is_watch_order && !$current_user_id) {
                foreach ($products as $prod) {
                    // Calculate price
                    #$price = round($this->aplicarImposto($prod->total_price, $origin) - $valor_adicional_portes, 2);
                    $price = $prod->totalPrice->sellingPrice;

                    // Adjustment for quantity
                    if ($prod->quantity > 1) {
                        $price = ($prod->totalPrice->sellingPrice / $prod->quantity) - $valor_adicional_portes;
                    }

                    // Search for seller
                    $price = number_format($price, 2, '.', '');

                     var_dump($ean, $channel, $price, $order_id, $tblId);
                    // die();
                    $found_seller = $this->getSeller($ean, $channel, $price, $order_id, $tblId);

                    if (isset($found_seller) && $this->isUserActive($found_seller)) {
                        $users_id[] = $found_seller->user_id;
                    }
                }
            }

            // Define final user_id
            if ($is_watch_order) {
                $user_id = 843;
            } elseif ($current_user_id) {
                $user_id = $current_user_id;
            } else {
                $user_id = $this->getSingleSellerId($users_id);
            }

        } else {
            #var_dump("{$channel} = 1 produto");

            // Logic for single product
            $price = $order->price - $valor_adicional_portes;

            // Check if it's a watch (assuming structure may vary)
            if (isset($order->response->response->lines[0]->label) && stripos($order->response->response->lines[0]->label, 'relógio') !== false) {
                $is_watch_order = true;
            }

            // Define final user_id
            if ($is_watch_order) {
                $user_id = 843;
            } elseif ($current_user_id) {
                $user_id = $current_user_id;
            } else {
                // Search for seller only if it's not a watch and doesn't have user_id defined
                $found_seller = $this->getSeller($ean, $channel, $price, $order_id, $tblId);
                $user_id = isset($found_seller) ? $found_seller->user_id : null;
            }
        }


        // Finalize sending the order to the seller
        if ($user_id && !in_array($user_id, [999999])) {

            $payload = $this->flowCdiscount->generatePayloadCdiscount($channel, $order, $user_id, $country_mkt, $found_seller ?? null);
            if ($this->debug && !$this->order_bySupport) {
                echo json_encode($payload);
                //die();
            }

            $insertResult = $this->flowBighub->insert_tbl_order($payload);
            if ($this->debug && !$this->order_bySupport) {
                echo json_encode($insertResult);
            }

            if ($insertResult->status == 200) {
                \Log::info("Order: " . $order_id . " inserida na tbl_order");

                $this->flowBighub->curl_put_update_order_user($tblId, $user_id);
                \Log::info("Repassando a order " . $order_id . " para o user_id: " . $user_id);

                $this->flowBighub->createSendEmail($user_id, $order_id, $channel . '-' . $country_mkt);
                \Log::info("Notificando o seller sobre uma venda, order: " . $order_id . ", email enviado para o user_id: " . $user_id);

                return true;
            } else {
                \Log::info("Error ao inserir order na tbl_orders - " . $insertResult);
                echo 'Error ao inserir na tbl_orders';
                return  null;
            }
        } else {
            echo 'Seller not found';
            return null;
        }

        return false;
    }

    /**
     * Process Fnac orders
     */
    private function processFnacOrder($order)
    {
        $start = microtime(true);
        \Log::info("Starting processFnacOrder for order: ".$order->order_id);

        // echo json_encode($order);
        // die();
        $result = $this->exploreCode($order->channel->code);
        if ($result['code'] == 'INIT') {
            $result['code'] = $order->marketplace->country;
        }

        // Extract basic order data
        $tblId = $order->id;
        $order_id = $order->order_id;
        $marketplace = $order->marketplace->name;

        $stepStart = microtime(true);
        $country_mkt = $this->flowBighub->getCountryMarketplace($result['code']);
        \Log::info("getCountryMarketplace took: ".(microtime(true) - $stepStart)."s");

        $valor_adicional_portes = 0;

        // Normalize marketplace name
        $channel = $this->normalizeMarketplaceName($marketplace);

        // Extract EAN according to marketplace
        $stepStart = microtime(true);
        $ean = $this->flowBighub->getEanCode($order->sku[0], $order, $channel);
        \Log::info("getEanCode took: ".(microtime(true) - $stepStart)."s");
        \Log::info("Fnac - Returned EAN: {$ean}");

        // Log for debug
        #var_dump("entrou {$channel} - order: {$order_id}");

        // Local variable to control if it's a watch (doesn't affect other orders)
        \Log::info("Fnac - About to set is_watch_order and current_user_id");
        $is_watch_order = false;
        $current_user_id = $this->user_id_debug; // Preserve original support user_id
        \Log::info("Fnac - Variables set successfully");

        \Log::info("Fnac - Checking product quantity and user_id");
        \Log::info("Fnac - Product quantity: " . $order->product_quantity);
        \Log::info("Fnac - user_id_debug: " . var_export($current_user_id, true));

        // Check if it's an order with multiple products
        if ($order->product_quantity > 1) {
            \Log::info("Fnac - Multiple products detected");

            // Logic for multiple products
            $products = $order->response->order_lines;
            #$origin = $order->code_marketplace->code;

            // First check if any product is a watch
            foreach ($products as $prod) {
                if (isset($prod->product_title) && stripos($prod->product_title, 'relógio') !== false) {
                    $is_watch_order = true;
                    break; // If found a watch, no need to continue checking
                }
            }

            // If it's not a watch and doesn't have user_id defined by support, search for sellers
            if (!$is_watch_order && !$current_user_id) {
                foreach ($products as $prod) {
                    // Calculate price
                    #$price = round($this->aplicarImposto($prod->total_price, $origin) - $valor_adicional_portes, 2);
                    $price = $prod->price;

                    // Adjustment for quantity
                    if ($prod->quantity > 1) {
                        $price = ($prod->price / $prod->quantity) - $valor_adicional_portes;
                    }

                    // Search for seller
                    $price = number_format($price, 2, '.', '');
                    $found_seller = $this->getSeller($ean, $channel, $price, $order_id, $tblId);

                    if (isset($found_seller) && $this->isUserActive($found_seller)) {
                        $users_id[] = $found_seller->user_id;
                    }
                }
            }

            // Define final user_id
            if ($is_watch_order) {
                $user_id = 843;
            } elseif ($current_user_id) {
                $user_id = $current_user_id;
            } else {
                $user_id = $this->getSingleSellerId($users_id);
            }
        } else {
            \Log::info("Fnac - Single product detected");
            #var_dump("{$channel} = 1 produto");

            // Logic for single product
            $price = $order->price - $valor_adicional_portes;
            \Log::info("Fnac - Price calculated: {$price}");

            // Check if it's a watch
            if (isset($order->response->order_lines[0]->product_title) && stripos($order->response->order_lines[0]->product_title, 'relógio') !== false) {
                $is_watch_order = true;
                \Log::info("Fnac - Watch order detected");
            }

            // Define final user_id
            if ($is_watch_order) {
                $user_id = 843;
                \Log::info("Fnac - Assigned to watch user: 843");
            } elseif ($current_user_id) {
                $user_id = $current_user_id;
                \Log::info("Fnac - Using debug user_id: {$current_user_id}");
            } else {
                // Search for seller only if it's not a watch and doesn't have user_id defined
                \Log::info("Fnac - Searching for seller with EAN: {$ean}, Channel: {$channel}, Price: {$price}");
                $stepStart = microtime(true);
                $found_seller = $this->getSeller($ean, $channel, $price, $order_id, $tblId);
                \Log::info("Fnac - getSeller took: ".(microtime(true) - $stepStart)."s");
                $user_id = isset($found_seller) ? $found_seller->user_id : null;
                \Log::info("Fnac - Found user_id: ".($user_id ?? 'null'));
            }
        }


        // Finalize sending the order to the seller
        if ($user_id && !in_array($user_id, [999999])) {

            $payload = $this->flowFnac->generatePayloadFnac($channel, $order, $user_id, $country_mkt, $found_seller ?? null);
            if ($this->debug && !$this->order_bySupport) {
                echo json_encode($payload);
               # die();
            }

            $insertResult = $this->flowBighub->insert_tbl_order($payload);
            if ($this->debug && !$this->order_bySupport) {
                echo json_encode($insertResult);
            }

            if ($insertResult->status == 200) {
                \Log::info("Order: " . $order_id . " inserida na tbl_order");

                $this->flowBighub->curl_put_update_order_user($tblId, $user_id);
                \Log::info("Repassando a order " . $order_id . " para o user_id: " . $user_id);

                $this->flowBighub->createSendEmail($user_id, $order_id, $channel . '-' . $country_mkt);
                \Log::info("Notificando o seller sobre uma venda, order: " . $order_id . ", email enviado para o user_id: " . $user_id);

                return true;
            } else {
                #\Log::info("Error ao inserir order na tbl_orders - " . $insertResult);
                echo 'Error ao inserir na tbl_orders';
                return  null;
            }
        } else {
            echo 'Seller not found';
            return null;
        }

        return false;
    }

    /**
     * Process Doc Morris orders
     */
    private function processDocMorrisOrder($order)
    {
        $result = $this->exploreCode($order->channel->code);
        if ($result['code'] == 'INIT') {
            $result['code'] = $order->marketplace->country;
        }
        // Extract basic order data
        $tblId = $order->id;
        $order_id = $order->order_id;
        $marketplace = $order->marketplace->name;
        $country_mkt = $this->flowBighub->getCountryMarketplace($order->channel->code);
        $valor_adicional_portes = 0;

        // var_dump($order->channel->code);
        // die();

        // Normalize marketplace name
        $channel = $this->normalizeMarketplaceName($marketplace);

        // Extract EAN according to marketplace
        $ean = $this->flowBighub->getEanCode($order->sku[0], $order, $channel);

        // Local variable to control if it's a watch (doesn't affect other orders)
        $is_watch_order = false;
        $current_user_id = $this->user_id_debug; // Preserve original support user_id
        // Check if it's an order with multiple products
        if ($order->product_quantity > 1) {
            // Logic for multiple products do DocMorris
            $users_id = [];

            // Iterate through subOrders and their items
            if (isset($order->response->subOrders)) {
                foreach ($order->response->subOrders as $subOrder) {
                    if (isset($subOrder->items)) {
                        foreach ($subOrder->items as $item) {
                            // Check if it's a watch
                            if (isset($item->product->name) && stripos($item->product->name, 'relógio') !== false) {
                                $is_watch_order = true;
                                break 2; // Exit both loops
                            }

                            // If it's not a watch and doesn't have user_id defined by support, search for sellers
                            if (!$is_watch_order && !$current_user_id) {
                                // Calculate price per unit
                                $price = isset($item->unitPrice->amount) ? $item->unitPrice->amount - $valor_adicional_portes : 0;

                                // Search for specific product EAN
                                $product_ean = $this->flowBighub->getEanCode($item->product->eans[0]->code ?? $ean, $order, $channel);

                                // Search for seller
                                $price = number_format($price, 2, '.', '');
                                $found_seller = $this->getSeller($product_ean, 'shopapotheke', $price, $order_id, $tblId);

                                if (isset($found_seller) && $this->isUserActive($found_seller)) {
                                    $users_id[] = $found_seller->user_id;
                                }
                            }
                        }
                    }
                }
            }

            // Define final user_id
            if ($is_watch_order) {
                $user_id = 843;
            } elseif ($current_user_id) {
                $user_id = $current_user_id;
            } else {
                $user_id = $this->getSingleSellerId($users_id);
            }
        } else {
            #var_dump("{$channel} = 1 produto");
            // Logic for single product
            $price = $order->price - $valor_adicional_portes;

            // Check if it's a watch (assuming structure may vary)
            if (isset($order->response->response->lines[0]->label) && stripos($order->response->response->lines[0]->label, 'relógio') !== false) {
                $is_watch_order = true;
            }

            // Define final user_id
            if ($is_watch_order) {
                $user_id = 843;
            } elseif ($current_user_id) {
                $user_id = $current_user_id;
            } else {
                // Search for seller only if it's not a watch and doesn't have user_id defined
                $found_seller = $this->getSeller($ean, 'shopapotheke', $price, $order_id, $tblId);
                $user_id = isset($found_seller) ? $found_seller->user_id : null;
            }
        }
        // Finalize sending the order to the seller
        if ($user_id && !in_array($user_id, [999999])) {
            $payload = $this->flowDocMorris->generatePayloadDocMorris($channel, $order, $user_id, $country_mkt, $found_seller ?? null);
            if ($this->debug && !$this->order_bySupport) {
                echo json_encode($payload);
                #die();
            }
            if ($this->debug && !$this->order_bySupport) {
                echo json_encode($payload);
                #die();
            }
            $insertResult = $this->flowBighub->insert_tbl_order($payload);
            if ($this->debug && !$this->order_bySupport) {
                echo json_encode($insertResult);
            }
            if ($insertResult->status == 200) {
                \Log::info("Order: " . $order_id . " inserida na tbl_order");
                $this->flowBighub->curl_put_update_order_user($tblId, $user_id);
                \Log::info("Repassando a order " . $order_id . " para o user_id: " . $user_id);
                $this->flowBighub->createSendEmail($user_id, $order_id, $channel . '-' . $country_mkt);
                \Log::info("Notificando o seller sobre uma venda, order: " . $order_id . ", email enviado para o user_id: " . $user_id);
                return true;
            } else {
                #\Log::info("Error ao inserir order na tbl_orders - " . $insertResult);
                echo 'Error ao inserir na tbl_orders';
                return  null;
            }
        } else {
            echo 'Seller not found';
            return null;
        }
        return false;
    }

    /**
     * Process ManoMano orders
     */
    private function processManomanoOrder($order) {
        $result = $this->exploreCode($order->channel->code);
        if ($result['code'] == 'INIT') {
            $result['code'] = $order->marketplace->country;
        }
        // Extract basic order data
        $tblId = $order->id;
        $order_id = $order->order_id;
        $marketplace = $order->marketplace->name;
        $country_mkt = $this->flowBighub->getCountryMarketplace($result['code']);
        $valor_adicional_portes = 0;

        // Normalize marketplace name
        $channel = $this->normalizeMarketplaceName($marketplace);

        // Extract EAN according to marketplace
        $ean = $this->flowBighub->getEanCode($order->sku[0]->seller_sku, $order, $channel);
        // Log for debug
        #var_dump("entrou {$channel} - order: {$order_id}");
        // die();

        // Local variable to control if it's a watch (doesn't affect other orders)
        $is_watch_order = false;
        $current_user_id = $this->user_id_debug; // Preserve original support user_id
        // Check if it's an order with multiple products
        if ($order->product_quantity > 1) {
            // Logic for multiple products do ManoMano
            $users_id = [];

            // Iterate through products (ManoMano structure: response->content->products)
            if (isset($order->response->response->content->products)) {
                foreach ($order->response->response->content->products as $product) {
                    // Check if it's a watch
                    if (isset($product->product_title) && stripos($product->product_title, 'relógio') !== false) {
                        $is_watch_order = true;
                        break; // Exit loop
                    }

                    // If it's not a watch and doesn't have user_id defined by support, search for sellers
                    if (!$is_watch_order && !$current_user_id) {
                        // Calculate price per unit
                        $price = $product->product_price->amount - $valor_adicional_portes;

                        // Search for specific product EAN
                        $product_ean = $this->flowBighub->getEanCode($product->seller_sku ?? $ean, $order, $channel);

                        // Search for seller
                        $price = number_format($price, 2, '.', '');
                        $found_seller = $this->getSeller($product_ean, 'manomano', $price, $order_id, $tblId);

                        if (isset($found_seller) && $this->isUserActive($found_seller)) {
                            $users_id[] = $found_seller->user_id;
                        }
                    }
                }
            }

            // Define final user_id
            if ($is_watch_order) {
                $user_id = 843;
            } elseif ($current_user_id) {
                $user_id = $current_user_id;
            } else {
                $user_id = $this->getSingleSellerId($users_id);
            }
        } else {
            #var_dump("{$channel} = 1 produto");
            // Logic for single product
            $price = $order->price - $valor_adicional_portes;

            // Check if it's a watch (assuming structure may vary)
            if (isset($order->response->response->lines[0]->label) && stripos($order->response->response->lines[0]->label, 'relógio') !== false) {
                $is_watch_order = true;
            }

            // Define final user_id
            if ($is_watch_order) {
                $user_id = 843;
            } elseif ($current_user_id) {
                $user_id = $current_user_id;
            } else {
                // Search for seller only if it's not a watch and doesn't have user_id defined
                $found_seller = $this->getSeller($ean, 'manomano', $price, $order_id, $tblId);
                $user_id = isset($found_seller) ? $found_seller->user_id : null;
            }
        }
        // Finalize sending the order to the seller
        if ($user_id && !in_array($user_id, [999999])) {
            $payload = $this->flowManoMano->generatePayloadManoMano($channel, $order, $user_id, $country_mkt, $found_seller ?? null);
            if ($this->debug && !$this->order_bySupport) {
                echo json_encode($payload);
                #die();
            }
            $insertResult = $this->flowBighub->insert_tbl_order($payload);
            if ($this->debug && !$this->order_bySupport) {
                echo json_encode($insertResult);
            }
            if ($insertResult->status == 200) {
                \Log::info("Order: " . $order_id . " inserida na tbl_order");

                $this->flowBighub->curl_put_update_order_user($tblId, $user_id);
                \Log::info("Repassando a order " . $order_id . " para o user_id: " . $user_id);

                $this->flowBighub->createSendEmail($user_id, $order_id, $channel . '-' . $country_mkt);
                \Log::info("Notificando o seller sobre uma venda, order: " . $order_id . ", email enviado para o user_id: " . $user_id);

                return true;
            } else {
                \Log::info("Error ao inserir order na tbl_orders - " . $insertResult);
                echo 'Error ao inserir na tbl_orders';
                return  null;
            }
        } else {
            echo 'Seller not found';
            return null;
        }

        return false;
    }

    /**
     * Process Stockly orders
     */
    private function processStocklyOrder($order) {
        \Log::info('processStocklyOrder - Iniciando', [
            'order_id' => $order->order_id ?? 'unknown',
            'tbl_id' => $order->id ?? 'unknown'
        ]);

        try {
            $result = $this->exploreCode($order->channel->code);
            if ($result['code'] == 'INIT') {
                $result['code'] = $order->marketplace->country;
            }

            // Extract basic order data
            $tblId = $order->id;
            $order_id = $order->order_id;
            $marketplace = $order->marketplace->name;

            \Log::info('processStocklyOrder - Dados básicos extraídos', [
                'order_id' => $order_id,
                'tbl_id' => $tblId,
                'marketplace' => $marketplace,
                'channel_code' => $order->channel->code
            ]);

            $country_mkt = $this->flowBighub->getCountryMarketplace($result['code']);

            // Normalize marketplace name
            $channel = $this->normalizeMarketplaceName($marketplace);

            // Extract EAN from first item
            $orderFirst = $order->response_first;

            if (!$orderFirst || !isset($orderFirst->items_line) || empty($orderFirst->items_line)) {
                \Log::error('processStocklyOrder - response_first vazio ou inválido', [
                    'order_id' => $order_id,
                    'response_first' => $orderFirst
                ]);
                echo json_encode(['error' => 'Invalid order structure - response_first empty']);
                return null;
            }

            $ean = $orderFirst->items_line[0]->ean ?? null;

            \Log::info('processStocklyOrder - EAN extraído', [
                'order_id' => $order_id,
                'ean' => $ean,
                'product_quantity' => $order->product_quantity
            ]);

            $current_user_id = $this->user_id_debug; // Preserve original support user_id

        // Check if it's an order with multiple products
        if ($order->product_quantity > 1) {
            // Logic for multiple products from Stockly
            $users_id = [];

            // Iterate through items
            foreach ($orderFirst->items_line as $item) {
                // If doesn't have user_id defined by support, search for sellers
                if (!$current_user_id) {
                    // Get price per unit
                    $price = floatval($item->price_unit);

                    // Search for specific product EAN
                    $product_ean = $item->ean;

                    // Search for seller
                    $price = number_format($price, 2, '.', '');
                    $found_seller = $this->getSeller($product_ean, 'stockly', $price, $order_id, $tblId);

                    if (isset($found_seller) && $this->isUserActive($found_seller)) {
                        $users_id[] = $found_seller->user_id;
                    }
                }
            }

            // Define final user_id
            if ($current_user_id) {
                $user_id = $current_user_id;
            } else {
                $user_id = $this->getSingleSellerId($users_id);
            }
        } else {
            // Logic for single product
            $price = floatval($orderFirst->items_line[0]->price_unit);

            // Define final user_id
            if ($current_user_id) {
                $user_id = $current_user_id;
            } else {
                // Search for seller
                $found_seller = $this->getSeller($ean, 'stockly', $price, $order_id, $tblId);
                $user_id = isset($found_seller) ? $found_seller->user_id : null;
            }
        }

            // Finalize sending the order to the seller
            \Log::info('processStocklyOrder - Verificando user_id para envio', [
                'order_id' => $order_id,
                'user_id' => $user_id ?? 'null'
            ]);

            if ($user_id && !in_array($user_id, [999999])) {
                \Log::info('processStocklyOrder - Gerando payload', [
                    'order_id' => $order_id,
                    'user_id' => $user_id
                ]);

                $payload = $this->flowStockly->generatePayloadStockly($channel, $order, $user_id, $country_mkt, $found_seller ?? null);
                if ($this->debug && !$this->order_bySupport) {
                    echo json_encode($payload);
                   #die();
                }

                \Log::info('processStocklyOrder - Inserindo na tbl_order');
                $insertResult = $this->flowBighub->insert_tbl_order($payload);
                if ($this->debug && !$this->order_bySupport) {
                    echo json_encode($insertResult);
                }

                if ($insertResult->status == 200) {
                    \Log::info("processStocklyOrder - Order inserida na tbl_order com sucesso", [
                        'order_id' => $order_id
                    ]);

                    $this->flowBighub->curl_put_update_order_user($tblId, $user_id);
                    \Log::info("processStocklyOrder - Order repassada", [
                        'order_id' => $order_id,
                        'user_id' => $user_id
                    ]);

                    $this->flowBighub->createSendEmail($user_id, $order_id, $channel . '-' . $country_mkt);
                    \Log::info("processStocklyOrder - Email enviado", [
                        'order_id' => $order_id,
                        'user_id' => $user_id
                    ]);

                    return true;
                } else {
                    \Log::error("processStocklyOrder - Erro ao inserir order na tbl_orders", [
                        'order_id' => $order_id,
                        'insert_result' => $insertResult
                    ]);
                    echo 'Error ao inserir na tbl_orders';
                    return  null;
                }
            } else {
                \Log::warning('processStocklyOrder - Seller not found', [
                    'order_id' => $order_id,
                    'user_id' => $user_id ?? 'null'
                ]);
                echo 'Seller not found';
                return null;
            }

        } catch (\Exception $e) {
            \Log::error('processStocklyOrder - Exception capturada', [
                'order_id' => $order->order_id ?? 'unknown',
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }

        return false;
    }

}

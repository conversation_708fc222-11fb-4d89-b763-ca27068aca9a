<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\UserClient;
use App\Models\UserProduct;


use Exception;

class FlowBighub extends Model
{

    const XAPIKEY = '7e0913c6-015d-4c60-9c49-3e4837922e53';

    const URL_DATA_ORDERS = 'https://connect-big.bighub.store/api/orders/user/';
    const BEAR = 'bCZpeQAcwEIcm7y1hYCK3VspKQfWJ5IlgalrUqq2f60552dd';

    const SKU_PREFIX = 'BIGHUB';
	const USER_ID = 33;
	const URL_APIBIGHUB = 'https://app.bighub.store/';
	const URL_PUSHTOQUEUE = 'https://app.bighub.store/queue/';


    public function curl_get_orders_no_user($id) {
        $url_api = self::URL_DATA_ORDERS . $id;
        $handle = curl_init($url_api);

        $request_headers = [];
        $request_headers[] = 'Content-Type: application/json';
        $request_headers[] = 'Authorization: Bearer '.self::BEAR;

        curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);

        $response = curl_exec($handle);
        return json_decode($response);
    }

    public function curl_put_update_order_user($order_id, $user_id)
    {

        $url_api = "https://connect-big.bighub.store/api/orders/{$order_id}/update-user/{$user_id}";
        $handle = curl_init($url_api);

        $request_headers = [];
        $request_headers[] = 'Content-Type: application/json';
        $request_headers[] = 'Authorization: Bearer '.self::BEAR;

        curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);
        curl_setopt($handle, CURLOPT_CUSTOMREQUEST, 'PUT');

        $response = curl_exec($handle);
        curl_close($handle);

        return json_decode($response);
    }


    public function getOrdersByTableId($tableId)
    {
        $url_api = "https://connect-big.bighub.store/api/orders/table/{$tableId}";
        $handle = curl_init($url_api);

        $request_headers = [];
        $request_headers[] = 'Content-Type: application/json';
        $request_headers[] = 'Authorization: Bearer '.self::BEAR;

        curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);

        // Opção para ver o HTTP status code
        curl_setopt($handle, CURLOPT_HEADER, 0);

        $response = curl_exec($handle);
        $http_code = curl_getinfo($handle, CURLINFO_HTTP_CODE);
        $error = curl_error($handle);
        curl_close($handle);

        // Verifica erros na requisição
        if ($error) {
            error_log("cURL Error: " . $error);
            return null;
        }

        // Verifica status HTTP
        if ($http_code != 200) {
            error_log("HTTP Error: " . $http_code . " for URL " . $url_api);
            error_log("Response: " . $response);
        }

        // Verifica se o response é válido antes de decodificar
        $result = json_decode($response);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("JSON Error: " . json_last_error_msg());
            error_log("Raw response: " . $response);
        }

        return $result;
    }


     /**
     * Gera o payload padronizado para qualquer marketplace Mirakl
     *
     * @param string $marketplace Nome do marketplace
     * @param object $order Objeto contendo os dados do pedido
     * @param int $user_id ID do usuário
     * @param string $channel Canal de origem
     * @param bool $yes Flag para cálculo de comissão alternativo
     * @param mixed $found_seller Dados do vendedor (opcional)
     * @return array Payload formatado
     */
    public function generatePayloadMirakls($channel, $order, $user_id, $country_mkt, $found_seller = null, $charge_commission_bighub = true)
    {
        $itens_line = [];

        // Valor padrão da comissão
        $percent_commission_bighub = 15;

        // Obtendo os objetos de resposta
        $orderResp = $order->response;
        $orderFirst = $order->response_first;


        // Processamento dos itens do pedido
        foreach ($orderFirst->order_lines as $item) {

            // Determinar o EAN
            $ean = $this->getEanCode($item->offer_sku, $order, $channel);

            // Obtendo a imagem do produto
            $image = $this->getImgProduct($ean, (int)$user_id);

            // Cálculo das comissões
            $percent_commission_bighub = $this->porcentagemaCobrar($item->total_price, $item->total_commission);

            $total_commission_item = $item->total_commission;
            if ($charge_commission_bighub) {
                $total_commission_item = round(($percent_commission_bighub / 100) * $item->total_price, 2);
            }

            $total_commission_bighub_item = $total_commission_item;
            $percent_commission_marketplace_item = $this->calcularPorcentagemProporcional($item->total_commission, $item->total_price);
            $total_commission_marketplace_item = $item->total_commission;

            // Construção do item para o array de itens
            $itens_line[] = [
                "title" => $item->product_title,
                "ean" => $ean,
                "sku" => $item->offer_sku,
                "image" => $image,
                "state" => $order->status,
                "shipping_price" => $item->shipping_price,
                "shipping_taxes" => [],
                "taxes" => [],
                "commission_fee" => $total_commission_item ?? 0,
                "commission_rate_vat" => $item->commission_rate_vat,
                "commission_taxes" => [
                    [
                        "amount" => $total_commission_item ?? 0,
                        "rate" => $item->commission_taxes[0]->rate ?? 0
                    ]
                ],
                "commission_vat" => $item->commission_vat,
                "total_commission" => $total_commission_item,
                "total_price" => $item->total_price,
                "price_unit" => $item->price_unit,
                "price" => $item->price,
                "quantity" => $item->quantity,
                "leadtime_to_ship" => $orderFirst->leadtime_to_ship ?? 0,
                "finance" => [
                    "percent_commission_bighub" => $percent_commission_bighub,
                    "total_commission_bighub" => $total_commission_bighub_item,
                    "percent_commission_marketplace" => round($percent_commission_marketplace_item, 2),
                    "total_commission_marketplace" => $total_commission_marketplace_item
                ]
            ];
        }

        // dd($itens_line);

        // Cálculo da comissão total para o pedido
        $order_price = isset($order->price) ?  (float)$order->price  : (float)$order->price ;
        $percent_commission_bighub = $this->porcentagemaCobrar($order_price, $orderResp->total_commission);

        // Calculo do total da comissão bighub
        $total_commission_bighub = $orderResp->total_commission;
        if ($charge_commission_bighub) {
            $total_commission_bighub = round(($percent_commission_bighub / 100) * $order_price, 2);
        }

        // Cálculo da comissão do marketplace
        $percent_commission_marketplace = $this->calcularPorcentagemProporcional($orderResp->total_commission, $order_price);
        $total_commission_marketplace = $orderResp->total_commission;

        // echo json_encode($orderResp);
        // die();

        // Obter país do cliente
        if (isset($orderResp->customer->shipping_address->country)) {
            $customer_country = $this->getCountry($orderResp->customer->shipping_address->country);
        }else{
            echo 'error country not found. OrderId: '.$order->order_id;
            \Log::warning("Error country not found");
            die();
        }


        // Informações adicionais (para casos com account > 1)
        $additional_info = [];
        if (isset($order->marketplace->account) && $order->marketplace->account > 1) {
            $additional_info[] = [
                'key' => $channel,
                'value' => 'channel-' . $order->marketplace->account
            ];
        }
        $assigned_offer = [];
        if (!empty($found_seller)) {
            $assigned_offer[] = $found_seller;
        }

        // Construção do payload
        $payload = [
            "user_id" => (int)$user_id,
            "reference" => $orderFirst->order_id,
            "channel" => $channel,
            "country" => $country_mkt,
            "state" => 'SHIPPING',
            "total_price" => $order_price,
            "total_commission" => $total_commission_bighub,
            "shipping_time" => $orderFirst->leadtime_to_ship ?? 0,
            "customer" => [
                "first_name" => $orderFirst->customer->firstname ? $orderFirst->customer->firstname : $orderResp->customer->shipping_address->firstname,
                "last_name" => $orderFirst->customer->lastname ? $orderFirst->customer->lastname : $orderResp->customer->shipping_address->lastname,
                "nif" => $this->getOrderAdditionalField($orderFirst->order_additional_fields, 'vatid'),
                "billing_address" => [
                    "city" => $orderResp->customer->billing_address->city,
                    "company" => $orderResp->customer->billing_address->company,
                    "country" => $customer_country,
                    "country_iso_code" => $orderResp->customer->billing_address->country_iso_code,
                    "first_name" => $orderResp->customer->billing_address->firstname,
                    "last_name" => $orderResp->customer->billing_address->lastname,
                    "phone" => $orderResp->customer->billing_address->phone ?? null,
                    "phone_secondary" => $orderResp->customer->billing_address->phone_secondary ?? null,
                    "street_1" => $orderResp->customer->billing_address->street_1,
                    "street_2" => $orderResp->customer->billing_address->street_2,
                    "zip_code" => $orderResp->customer->billing_address->zip_code,
                ],
                "shipping_address" => [
                    "city" => $orderResp->customer->shipping_address->city,
                    "company" => $orderResp->customer->shipping_address->company,
                    "country" => $customer_country,
                    "country_iso_code" => $orderResp->customer->shipping_address->country_iso_code,
                    "first_name" => $orderResp->customer->shipping_address->firstname,
                    "last_name" => $orderResp->customer->shipping_address->lastname,
                    "phone" => $orderResp->customer->shipping_address->phone ?? null,
                    "phone_secondary" => $orderResp->customer->shipping_address->phone_secondary ?? null,
                    "street_1" => $orderResp->customer->shipping_address->street_1,
                    "street_2" => $orderResp->customer->shipping_address->street_2,
                    "zip_code" => $orderResp->customer->shipping_address->zip_code,
                ]
            ],
            "payment" => [
                "type" => $orderResp->payment_type ?? ""
            ],
            "items" => $itens_line,
            "finance" => [
                "percent_commission_bighub" => $percent_commission_bighub,
                "total_commission_bighub" => $total_commission_bighub,
                "percent_commission_marketplace" => round($percent_commission_marketplace, 2),
                "total_commission_marketplace" => $total_commission_marketplace
            ],
            "created_date" => str_replace(['T', 'Z'], [' ', ''], $orderResp->created_date)
        ];

        // Adicionando informações específicas se existirem
        $payload["additional_info"] = $additional_info;
        $payload["assigned_offer"] = $assigned_offer;


        if ($channel != 'elcorteingles') {
			$resulValidate = $this->comparaComissoes($payload);
			if ($resulValidate) {
				return $payload;
			} else {
                \Log::warning("A comissão BigHub não é maior que a comissão Marketplace.");
				return false;
			}
		} else {

			return $payload;
		}
    }

    public function insert_tbl_order($payload)
	{
		$url_api = self::URL_APIBIGHUB . 'api/v2/sales/orders';

		$handle   = curl_init($url_api);

		$request_headers     = [];
		$request_headers[] = 'Accept: application/json';
		$request_headers[] = 'Content-Type: application/json';
		$request_headers[] = 'x-api-key:'.self::XAPIKEY;

		curl_setopt($handle, CURLOPT_POST, true);
		curl_setopt($handle, CURLOPT_POSTFIELDS, json_encode($payload));
		curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);

		$response = curl_exec($handle);
		return json_decode($response);
	}

    public function getEanCode($sku, $order, $marketplace)
    {
        $ean = substr($sku, 6); // Padrão

        if (isset($order->marketplace->account) && $order->marketplace->account == 2) {
            if ($marketplace == 'carrefour') {
                $ean = substr($sku, 10);
            } else {
                $ean = substr($sku, 7);
            }
        }

        return $ean;
    }

    private function comparaComissoes($payload)
	{

		// Verifica se os campos existem no array payload
		if (isset($payload['finance']['total_commission_bighub']) && isset($payload['finance']['total_commission_marketplace'])) {
			$bighubCommission = $payload['finance']['total_commission_bighub'];
			$marketplaceCommission = $payload['finance']['total_commission_marketplace'];

			//var_dump($bighubCommission, $marketplaceCommission);
			if ($bighubCommission > $marketplaceCommission || $bighubCommission == $marketplaceCommission) {
				return true;
			} else {
				echo "O valor da comissão BigHub $bighubCommission não é maior nem igual ao o valor da comissão do Marketplace $marketplaceCommission";
				die();
				return false;
			}
		} else {
			echo "Os campos necessários não existem no payload.";
			return false;
		}
	}

    private function getImgProduct($ean, $user_id)
    {
        // Definir imagem padrão
        $img = 'https://cdn.bighub.store/image/product-placeholder.png';

        $product = UserProduct::where('user_id', $user_id)
                            ->where('barCode', $ean)
                            ->first();

        if ($product && !empty($product->images)) {
            $img = $product->images;
        }

        return $img;
    }

	private function calcularPorcentagemProporcional($totalCommission, $totalPrice)
	{
		// Verifica se $totalCommission e $totalPrice são strings e converte para float
		if (is_string($totalCommission) && is_numeric($totalCommission)) {
			$totalCommission = floatval($totalCommission);
		}

		if (is_string($totalPrice) && is_numeric($totalPrice)) {
			$totalPrice = floatval($totalPrice);
		}

		// Verifica se o totalPrice é igual a zero para evitar divisão por zero
		if ($totalPrice == 0) {
			return 0; // Nesse caso, a porcentagem é zero
		}

		// Calcula a porcentagem proporcional
		$porcentagem = ($totalCommission / $totalPrice) * 100;

		return $porcentagem;
	}

	private function porcentagemaCobrar($valor_total, $valor_comission)
	{
		// Verifica e converte strings para floats
		$valor_total = is_string($valor_total) && is_numeric($valor_total) ? floatval($valor_total) : $valor_total;
		$valor_comission = is_string($valor_comission) && is_numeric($valor_comission) ? floatval($valor_comission) : $valor_comission;

		// Evita divisão por zero
		if ($valor_total == 0) {
			return 0; // Retorna 0 como valor padrão
		}

		// Calcula a porcentagem
		$porcentagem = round(($valor_comission / $valor_total) * 100, 2); // Mantém 2 casas decimais

		// var_dump($porcentagem);
		// die();

		// Verifica as faixas e retorna o valor correspondente
		if ($porcentagem <= 13) {
			return 15;
		} elseif ($porcentagem <= 15) {
			return 17;
		} elseif ($porcentagem <= 17) {
			return 19;
		} elseif ($porcentagem <= 19) {
			return 21;
		} elseif ($porcentagem <= 23) {
			return 25;
		} else {
			return 20; // Valor padrão se nenhuma faixa for atendida
		}
	}

	private function getCountry($country)
	{
		$customer_country = null;

		$countries_FR = [
            'France métropolitaine - Hors Corse',
            'France (métropolitaine)',
			'FR',
			'FRA', // Códigos de país
			'FRANCE',
			'france',
			'France', // Inglês
			'FRANÇA',
			'frança',
			'França', // Português
			'FRANZÖSISCH',
			'französisch',
			'Französisch', // Alemão (Adjetivo "Francês")
			'FRANKREICH',
			'frankreich',
			'Frankreich', // Alemão
			'PARIS',
			'paris',
			'Paris', // Cidades principais
			'MARSEILLE',
			'marseille',
			'Marseille',
			'LYON',
			'lyon',
			'Lyon',
			'TOULOUSE',
			'toulouse',
			'Toulouse',
			'NICE',
			'nice',
			'Nice',
			'BORDEAUX',
			'bordeaux',
			'Bordeaux',
			'LILLE',
			'lille',
			'Lille',
			'NANTES',
			'nantes',
			'Nantes',
			'STRASBOURG',
			'strasbourg',
			'Strasbourg'
		];

		$countries_AT = [
			'AT',
			'AUT', // Códigos de país
			'AUSTRIA',
			'austria',
			'Austria', // Inglês
			'ÁUSTRIA',
			'áustria',
			'Áustria', // Português
			'ÖSTERREICH',
			'österreich',
			'Österreich', // Alemão
			'VIENNA',
			'vienna',
			'Vienna',
			'WIEN',
			'wien',
			'Wien', // Capital
			'SALZBURG',
			'salzburg',
			'Salzburg',
			'INNSBRUCK',
			'innsbruck',
			'Innsbruck',
			'GRAZ',
			'graz',
			'Graz',
			'LINZ',
			'linz',
			'Linz',
			'KLAGENFURT',
			'klagenfurt',
			'Klagenfurt'
		];

		$countries_DE = [
			'DE',
			'DEU', // Códigos de país
			'GERMANY',
			'germany',
			'Germany', // Inglês
			'ALEMANHA',
			'alemanha',
			'Alemanha', // Português
			'DEUTSCHLAND',
			'deutschland',
			'Deutschland', // Alemão
			'BERLIN',
			'berlin',
			'Berlin',
			'FRANKFURT',
			'frankfurt',
			'Frankfurt',
			'HAMBURG',
			'hamburg',
			'Hamburg',
			'MUNICH',
			'munich',
			'Munich',
			'MÜNCHEN',
			'münchen',
			'München',
			'STUTTGART',
			'stuttgart',
			'Stuttgart',
			'COLOGNE',
			'cologne',
			'Cologne',
			'KÖLN',
			'köln',
			'Köln'
		];

		$countries_ES = [
			'ES',
			'ESP', // Códigos de país
			'SPAIN',
			'spain',
			'Spain', // Inglês
			'ESPAÑA',
			'españa',
			'Espana',
			'España', // Espanhol
			'ESPANHA',
			'espanha',
			'Espanha', // Português
			'SPANISCH',
			'spanisch',
			'Spanisch', // Alemão (Adjetivo "Espanhol")
			'SPANIEN',
			'spanien',
			'Spanien', // Alemão
			'MADRID',
			'madrid',
			'Madrid', // Cidades principais
			'BARCELONA',
			'barcelona',
			'Barcelona',
			'VALENCIA',
			'valencia',
			'Valencia',
			'SEVILLA',
			'sevilla',
			'Sevilla',
			'ZARAGOZA',
			'zaragoza',
			'Zaragoza',
			'MÁLAGA',
			'málaga',
			'Málaga',
			'BILBAO',
			'bilbao',
			'Bilbao',
			'GRANADA',
			'granada',
			'Granada'
		];

		$countries_PT = [
			'PT',
			'PRT', // Códigos de país
			'PORTUGAL',
			'portugal',
			'Portugal', // Inglês e Português
			'PORTUGUÊS',
			'português',
			'Português', // Adjetivo em Português
			'PORTUGIESISCH',
			'portugiesisch',
			'Portugiesisch', // Alemão (Adjetivo "Português")
			'LISBON',
			'lisbon',
			'Lisbon',
			'LISBOA',
			'lisboa',
			'Lisboa', // Capital
			'PORTO',
			'porto',
			'Porto',
			'COIMBRA',
			'coimbra',
			'Coimbra',
			'FARO',
			'faro',
			'Faro',
			'BRAGA',
			'braga',
			'Braga',
			'AVEIRO',
			'aveiro',
			'Aveiro',
			'GUIMARÃES',
			'guimarães',
			'Guimarães',
			'MADEIRA',
			'madeira',
			'Madeira',
			'AÇORES',
			'açores',
			'Açores'
		];

		$countries_IT = [
			'IT',
			'ITA', // Códigos de país
			'ITALY',
			'italy',
			'Italy', // Inglês
			'ITALIA',
			'italia',
			'Italia', // Italiano
			'ITALIANO',
			'italiano',
			'Italiano', // Adjetivo em Italiano
			'ITALIEN',
			'italien',
			'Italien', // Alemão
			'ROMA',
			'roma',
			'Roma',
			'ROME',
			'rome',
			'Rome', // Capital
			'MILANO',
			'milano',
			'Milano',
			'MILAN',
			'milan',
			'Milan',
			'NAPOLI',
			'napoli',
			'Napoli',
			'NAPLES',
			'naples',
			'Naples',
			'TORINO',
			'torino',
			'Torino',
			'TURIN',
			'turin',
			'Turin',
			'FIRENZE',
			'firenze',
			'Firenze',
			'FLORENCE',
			'florence',
			'Florence',
			'VENEZIA',
			'venezia',
			'Venezia',
			'VENICE',
			'venice',
			'Venice',
			'BOLOGNA',
			'bologna',
			'Bologna',
			'GENOVA',
			'genova',
			'Genova',
			'GENOA',
			'genoa',
			'Genoa',
			'PALERMO',
			'palermo',
			'Palermo'
		];

        $countries_IT = [
			'IT',
			'ITA', // Códigos de país
			'ITALY',
			'italy',
			'Italy', // Inglês
			'ITALIA',
			'italia',
			'Italia', // Italiano
			'ITALIANO',
			'italiano',
			'Italiano', // Adjetivo em Italiano
			'ITALIEN',
			'italien',
			'Italien', // Alemão
			'ROMA',
			'roma',
			'Roma',
			'ROME',
			'rome',
			'Rome', // Capital
			'MILANO',
			'milano',
			'Milano',
			'MILAN',
			'milan',
			'Milan',
			'NAPOLI',
			'napoli',
			'Napoli',
			'NAPLES',
			'naples',
			'Naples',
			'TORINO',
			'torino',
			'Torino',
			'TURIN',
			'turin',
			'Turin',
			'FIRENZE',
			'firenze',
			'Firenze',
			'FLORENCE',
			'florence',
			'Florence',
			'VENEZIA',
			'venezia',
			'Venezia',
			'VENICE',
			'venice',
			'Venice',
			'BOLOGNA',
			'bologna',
			'Bologna',
			'GENOVA',
			'genova',
			'Genova',
			'GENOA',
			'genoa',
			'Genoa',
			'PALERMO',
			'palermo',
			'Palermo'
		];

        $countries_SI = [
            'Slovenija',
            'SI',
            'SVN', // Códigos de país
            'SLOVENIA',
            'slovenia',
            'Slovenia', // Inglês
            'ESLOVÉNIA',
            'eslovénia',
            'Eslovénia', // Português (pt-PT)
            'ESLOVÊNIA',
            'eslovênia',
            'Eslovênia', // Português (pt-BR)
            'SLOWENIEN',
            'slowenien',
            'Slowenien', // Alemão
            'SLOVENSKO',
            'slovensko',
            'Slovensko', // Esloveno
            'SLOVENEC',
            'slovenec',
            'Slovenec', // Gentílico masculino (esloveno)
            'SLOVENKA',
            'slovenka',
            'Slovenka', // Gentílico feminino (esloveno)
            'LJUBLJANA',
            'ljubljana',
            'Ljubljana', // Capital
            'MARIBOR',
            'maribor',
            'Maribor',
            'CELJE',
            'celje',
            'Celje',
            'KOPER',
            'koper',
            'Koper',
            'KRANJ',
            'kranj',
            'Kranj',
            'NOVO MESTO',
            'novo mesto',
            'Novo Mesto',
            'PTUJ',
            'ptuj',
            'Ptuj',
            'PIRAN',
            'piran',
            'Piran',
            'BLED',
            'bled',
            'Bled'
        ];

        $countries_BE = [
            'BE',
            'BEL',
            'BELGIUM',
            'belgium',
            'Belgium',
            'BELGIO',
            'belgio',
            'Belgio',
            'BELGIQUE',
            'belgique',
            'Belgique',
            'BELGIEN',
            'belgien',
            'Belgien',
            'BRUXELLES',
            'bruxelles',
            'Bruxelles',
            'BRUSSELS',
            'brussels',
            'Brussels',
            'BRÜSSEL',
            'brüssel',
            'Brüssel',
            'ANTWERP',
            'antwerp',
            'Antwerp',
            'ANVERS',
            'anvers',
            'Anvers',
            'GENT',
            'gent',
            'Gent',
            'GHENT',
            'ghent',
            'Ghent',
            'LIEGE',
            'liege',
            'Liège',
            'LIÈGE',
            'liège',
            'CHARLEROI',
            'charleroi',
            'Charleroi',
            'NAMUR',
            'namur',
            'Namur',
            'MONS',
            'mons',
            'Mons',
            'LOUVAIN',
            'louvain',
            'Louvain',
            'LEUVEN',
            'leuven',
            'Leuven',
            'BRUGGE',
            'brugge',
            'Brugge',
            'BRUGES',
            'bruges',
            'Bruges',
        ];


		$countryInput = isset($country)
			? trim(strtolower($country))
			: '';

		if (!empty($countryInput)) {
			if (in_array($countryInput, array_map('strtolower', $countries_FR), true)) {
				$customer_country = 'FR';
			} elseif (in_array($countryInput, array_map('strtolower', $countries_AT), true)) {
				$customer_country = 'AT';
			} elseif (in_array($countryInput, array_map('strtolower', $countries_DE), true)) {
				$customer_country = 'DE';
			} elseif (in_array($countryInput, array_map('strtolower', $countries_ES), true)) {
				$customer_country = 'ES';
			} elseif (in_array($countryInput, array_map('strtolower', $countries_IT), true)) {
				$customer_country = 'IT';
			} elseif (in_array($countryInput, array_map('strtolower', $countries_PT), true)) {
				$customer_country = 'PT';
			}  elseif (in_array($countryInput, array_map('strtolower', $countries_SI), true)) {
				$customer_country = 'SI';
			} elseif (in_array($countryInput, array_map('strtolower', $countries_BE), true)) {
				$customer_country = 'BE';
			}
			return $customer_country;
		}

		// if($customer_country == null){
		// 	#cahamda para GPT para tratar do country
		// 	return $this->getCountryCodeGPT($country);
		// }


		return null;
	}

	private function getCountryCodeGPT($countryName)
	{
		$apiKey = 'sk-proj--W70sXtSHYnvHk7G33R10PEiT1yNN506QF3lcA'; // Substitua pela sua chave da OpenAI
		$url = 'https://api.openai.com/v1/chat/completions';

		$data = [
			'model' => 'gpt-4',
			'messages' => [
				['role' => 'system', 'content' => 'Você é um assistente útil.'],
				['role' => 'user', 'content' => "Tenho o country de um formulário que foi digitado $countryName. Poderia me dizer qual é esse country em duas letras? Não quero esse tipo de resposta: Acredito que houve um erro de digitação. O correto seria France', cujo código de duas letras é 'FR'. Quero apenas o FR como resposta nesse caso."],
			],
			'temperature' => 0.2,
		];

		$headers = [
			'Content-Type: application/json',
			'Authorization: Bearer ' . $apiKey,
		];

		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

		$response = curl_exec($ch);
		curl_close($ch);

		$decodedResponse = json_decode($response, true);
		return $decodedResponse['choices'][0]['message']['content'] ?? 'Erro ao obter código do país';
	}

    public function createSendEmail($user_id, $order_id, $channel)
    {
        // Obtendo o vendedor usando Eloquent
        $seller = UserClient::findOrFail($user_id);
        $emails = $seller->email;

        $payload = [
            "template_id" => 27,
            "sender" => "<EMAIL>",
            "receiver" => $emails,  // Usando os e-mails do banco de dados
            "is_active" => true,
            "variables" => [
                [
                    "key" => "name_seller",
                    "value" => $seller->name
                ],
                [
                    "key" => "order_id",
                    "value" => $order_id
                ],
                [
                    "key" => "channel",
                    "value" => $channel
                ]
            ]
        ];


		// URL da API
		$url_api = 'https://app.bighub.store/api/v2/communications/messages';

		// Inicializa o cURL
		$handle  = curl_init($url_api);

		// Configuração dos headers da requisição
		$request_headers = [];
		$request_headers[] = 'Accept: application/json';
		$request_headers[] = 'Content-Type: application/json';
        $request_headers[] = 'x-api-key:'.self::XAPIKEY;

		// Configurações do cURL para uma requisição POST
		curl_setopt($handle, CURLOPT_POST, true);
		curl_setopt($handle, CURLOPT_POSTFIELDS, json_encode($payload));
		curl_setopt($handle, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);

		// Executa a requisição e captura a resposta
		$response = curl_exec($handle);

		// Verifica se houve erro na execução da requisição
		if (curl_errno($handle)) {
			echo 'Erro na requisição: ' . curl_error($handle);
			return;
		}

		// Fecha a conexão cURL
		curl_close($handle);

		// Decodifica a resposta JSON para um array associativo
		$response_data = json_decode($response, true);

		//var_dump($response_data);
		// die();

		// Verifica se o campo "id" está presente na resposta e chama a função de GET
		if (isset($response_data['id'])) {

			// Chama a função de GET com o ID retornado
			$this->send_get_request($response_data['id']);
		} else {
			echo 'Erro: ID não encontrado na resposta da API.';
		}
	}

	private function send_get_request($message_id)
	{
		$url = "https://app.bighub.store/api/v2/communications/messages/{$message_id}/send";

		$handle = curl_init($url);

		$request_headers = [];
		$request_headers[] = 'Accept: application/json';
		$request_headers[] = 'Content-Type: application/json';
        $request_headers[] = 'x-api-key:'.self::XAPIKEY;

		curl_setopt($handle, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);

		$response = curl_exec($handle);

		if (curl_errno($handle)) {
			echo 'Erro na requisição: ' . curl_error($handle);
		} else {
			//echo $response;
            //die();
		}

		curl_close($handle);
	}

    /**
     * Busca os dados de um pedido específico
     *
     * @param int $order_id ID do pedido
     * @return object|null Dados do pedido ou null se não encontrado
     */
    public function curl_get_specific_order($order_id, $channel)
    {
        // Exemplo de endpoint. Substitua pelo seu endpoint real
        $endpoint = "http://localhost:4545/order/{$order_id}/{$channel}";

        // Configuração da requisição cURL
        $ch = curl_init($endpoint);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $this->api_token,
            'Content-Type: application/json'
        ]);

        // Executa a requisição
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        // Verifica se a requisição foi bem-sucedida
        if ($http_code == 200) {
            return json_decode($response);
        }

        \Log::error("Falha ao buscar pedido específico: {$order_id}. Código HTTP: {$http_code}");
        return null;
    }


    public function getCountryMarketplace($code)
	{
		switch ($code) {
			case "WRT_PT_ONLINE":
			case "FNAC_PRT_ONLINE":
            case "fnac-PT":
			case "WEB_PT":
			case "MAKRO_PT":
			case "cf":
			case "PT":
            case "channel_PT":
				return 'PT';
				break;
			case "WRT_ES_ONLINE":
			case "CRF_ES_ONLINE":
			case "MKT_ES_ONLINE":
			case "FNAC_ES_ONLINE":
			case "WEB_ES":
			case "LMES":
			case "MAKRO_ES":
			case "MANO":
			case "ES_B2C":
			case "BVIP":
			case "PIXMANIA_FR":
			case "eciStore":
			case "ES":
            case "002":
            case "MMES":
            case "fnac-ES":
				return 'ES';
				break;
			case "EPR_IT_ONLINE":
			case "WEB_IT":
			case "IT_B2C":
			case "IT":
				return 'IT';
				break;
			case "B2C":
			case "RAKUTEN":
			case "WEB_FR":
			case "RUE_FR":
			case "CDISFR":
			case "CRF_FR_ONLINE":
			case "macway":
			case "fnac-FR":
			case "mktbighub-FR":
            case "CA_FR":
            case "zooplus_fr":
			case "FR":
            case "001":
            case "cdiscount-CDISFR":
				return 'FR';
				break;
			case "MMDE":
			case "SEDE":
			case "DE":
            case "germany":
				return 'DE';
			case "AT":
				return 'AT';
				break;
            case "nl_BE":
            case "BE":
				return 'BE';
				break;
		}
	}

    /**
     * Obtém o valor de um campo adicional específico do pedido
     *
     * @param array $additionalFields Array de campos adicionais
     * @param string $codeToFind Código do campo a ser encontrado
     * @return string|null Valor do campo ou null se não encontrado
     */
    private function getOrderAdditionalField($additionalFields, $codeToFind)
    {
        if (empty($additionalFields) || !is_array($additionalFields)) {
            return null;
        }

        foreach ($additionalFields as $field) {
            if (isset($field->code) && $field->code == $codeToFind && isset($field->value)) {
                return $field->value;
            }
        }

        // Tenta encontrar por shipping-vat-id se não encontrou pelo código principal
        if ($codeToFind == 'vatid') {
            foreach ($additionalFields as $field) {
                if (isset($field->code) && $field->code == 'shipping-vat-id' && isset($field->value)) {
                    return $field->value;
                }
            }
        }

        return null;
    }

}

<?php

namespace App\Models;
use App\Services\OrderFlowService;

use Illuminate\Database\Eloquent\Model;

class FlowCdiscount extends Model
{

    protected $flowService;

    public function __construct(OrderFlowService $flowService)
    {
        $this->flowOrderService = $flowService;
    }


     /**
     * Gera o payload padronizado para Cdiscount
     *
     * @param string $marketplace Nome do marketplace
     * @param object $order Objeto contendo os dados do pedido
     * @param int $user_id ID do usuário
     * @param string $channel Canal de origem
     * @param bool $yes Flag para cálculo de comissão alternativo
     * @param mixed $found_seller Dados do vendedor (opcional)
     * @return array Payload formatado
     */
    public function generatePayloadCdiscount($channel, $order, $user_id, $country_mkt, $found_seller = null, $charge_commission_bighub = true)
    {
        $itens_line = [];
        $total_comission = 0;

        // Obtendo os objetos de resposta
        $orderResp = $order->response;
        $orderFirst = $order->response_first;

        // Processamento dos itens do pedido
        foreach ($orderFirst->lines as $item) {
            $ean = substr($item->offer->sellerProductId, 6);

            if (isset($order->marketplace->account) && $order->marketplace->account > 1) {
                $ean = substr($item->offer->sellerProductId, 7);
            }

            $total_comission += $item->offerPrice->commission->amountWithVat;

            $image = $this->flowOrderService->getImgProduct($ean, (int)$user_id);

            $percent_commission_bighub = $this->flowOrderService->porcentagemaCobrar($item->totalPrice->offerPrice, $total_comission);

            $total_commission_item = $item->offerPrice->commission->amountWithVat;
            if ($charge_commission_bighub) {
                $total_commission_item = round(($percent_commission_bighub / 100) * $item->totalPrice->offerPrice, 2);
            }

            $total_commission_bighub_item = $total_commission_item;
            $percent_commission_marketplace_item = $this->flowOrderService->calcularPorcentagemProporcional($item->offerPrice->commission->amountWithVat, $item->totalPrice->offerPrice);
            $total_commission_marketplace_item = $item->offerPrice->commission->amountWithVat;

            $itens_line[] = [
                "title" => $item->offer->productTitle,
                "ean" => $ean,
                "sku" => $item->offer->sellerProductId,
                "image" => $image,
                "state" => 'SHIPPING',
                "shipping_price" => $item->offerPrice->shippingCost,
                "shipping_taxes" => [],
                "taxes" => [],
                "commission_fee" => $total_commission_item ?? 0,
                "commission_rate_vat" => $item->offerPrice->commission->rate,
                "commission_taxes" => [
                    [
                        "amount" => $total_commission_item ?? 0,
                        "rate" => $item->offerPrice->taxes[0]->rate
                    ]
                ],
                "commission_vat" => $item->offerPrice->taxes[0]->amount,
                "total_commission" => $total_commission_item,
                "total_price" => $item->totalPrice->offerPrice,
                "price_unit" => $item->offerPrice->unitSalesPrice,
                "price" => $item->totalPrice->offerPrice,
                "quantity" => $item->quantity,
                "leadtime_to_ship" => 0,
                "finance" => [
                    "percent_commission_bighub" => $percent_commission_bighub,
                    "total_commission_bighub" => $total_commission_bighub_item,
                    "percent_commission_marketplace" => round($percent_commission_marketplace_item, 2),
                    "total_commission_marketplace" => $total_commission_marketplace_item
                ]
            ];
        }

        $percent_commission_bighub = $this->flowOrderService->porcentagemaCobrar($orderFirst->totalPrice->sellingPrice, $total_comission);

        // Calculo order
        $total_commission_bighub = $total_comission;
        if ($charge_commission_bighub) {
            $total_commission_bighub = round(($percent_commission_bighub / 100) * $orderFirst->totalPrice->sellingPrice, 2);
        }

        $percent_commission_marketplace = $this->flowOrderService->calcularPorcentagemProporcional($total_comission, $orderFirst->totalPrice->sellingPrice);
        $total_commission_marketplace = $total_comission;

        $customer_country = $this->flowOrderService->getCountry($orderFirst->billingAddress->countryCode);

        $line_product_zero = $orderFirst->lines[0];

        $additional_info = [];
        if (isset($order->marketplace->account) && $order->marketplace->account > 1) {
            $additional_info = [
                [
                    'key' => 'cdiscount',
                    'value' => 'channel-' . $order->marketplace->account
                ]
            ];
        }

        $assigned_offer = [];
        if (!empty($found_seller)) {
            $assigned_offer[] = $found_seller;
        }

        $payload = [
            "user_id" => (int)$user_id,
            "reference" => $orderFirst->orderId,
            "channel" => "cdiscount",
            "country" => $country_mkt,
            "state" => 'SHIPPING',
            "total_price" => $orderFirst->totalPrice->sellingPrice,
            "total_commission" => $total_commission_bighub,
            "shipping_time" => 0,
            "customer" => [
                "first_name" => $orderFirst->billingAddress->firstName ? $orderFirst->billingAddress->firstName : 'no name',
                "last_name" => $orderFirst->billingAddress->lastName ? $orderFirst->billingAddress->lastName : 'no name',
                "billing_address" => [
                    "city" => $orderFirst->billingAddress->city,
                    "company" => '',
                    "country" => $customer_country,
                    "country_iso_code" => $orderFirst->billingAddress->countryCode,
                    "first_name" => $orderFirst->billingAddress->firstName,
                    "last_name" => $orderFirst->billingAddress->lastName,
                    "phone" => $line_product_zero->shippingAddress->phone,
                    "phone_secondary" => null,
                    "street_1" => trim(($orderFirst->billingAddress->addressLine1 ?? '') . ' ' . ($orderFirst->billingAddress->addressLine2 ?? '')) ?: null,
                    "street_2" => $orderFirst->billingAddress->addressLine3 ?? null,
                    "zip_code" => $orderFirst->billingAddress->postalCode,
                ],
                "shipping_address" => [
                    "city" => $line_product_zero->shippingAddress->city,
                    "company" => '',
                    "country" => $customer_country,
                    "country_iso_code" => $line_product_zero->shippingAddress->countryCode,
                    "first_name" => $line_product_zero->shippingAddress->firstName,
                    "last_name" => $line_product_zero->shippingAddress->lastName,
                    "phone" => $line_product_zero->shippingAddress->phone,
                    "phone_secondary" => null,
                    "street_1" => trim(($line_product_zero->shippingAddress->addressLine1 ?? '') . ' ' . ($line_product_zero->shippingAddress->addressLine2 ?? '')) ?: null,
                    "street_2" => $line_product_zero->shippingAddress->addressLine3 ?? null,
                    "zip_code" => $line_product_zero->shippingAddress->postalCode,
                ]
            ],
            "payment" => [
                "type" => $orderFirst->payment->method ?? ''
            ],
            "items" => $itens_line,
            "finance" => [
                "percent_commission_bighub" => $percent_commission_bighub,
                "total_commission_bighub" => $total_commission_bighub,
                "percent_commission_marketplace" => round($percent_commission_marketplace, 2),
                "total_commission_marketplace" => $total_commission_marketplace
            ],
            "created_date" => str_replace(['T', 'Z'], [' ', ''], $orderFirst->createdAt)
        ];

        $payload["additional_info"] = $additional_info;
        $payload["assigned_offer"] = $assigned_offer;

        if ($channel != 'elcorteingles') {
            $resulValidate = $this->flowOrderService->comparaComissoes($payload);
            if ($resulValidate) {
                return $payload;
            } else {
                \Log::warning("A comissão BigHub não é maior que a comissão Marketplace.");
                return false;
            }
        } else {
            return $payload;
        }
    }


}

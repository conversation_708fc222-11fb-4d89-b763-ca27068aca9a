<?php

namespace App\Models;
use App\Services\OrderFlowService;

use Illuminate\Database\Eloquent\Model;

class FlowDocMorris extends Model
{

    protected $flowService;

    public function __construct(OrderFlowService $flowService)
    {
        $this->flowOrderService = $flowService;
    }


     /**
     * Gera o payload padronizado para DocMorris
     *
     * @param string $marketplace Nome do marketplace
     * @param object $order Objeto contendo os dados do pedido
     * @param int $user_id ID do usuário
     * @param string $channel Canal de origem
     * @param mixed $found_seller Dados do vendedor (opcional)
     * @param bool $charge_commission_bighub Flag para cálculo de comissão alternativo
     * @return array Payload formatado
     */
    public function generatePayloadDocMorris($channel, $order, $user_id, $country_mkt, $found_seller = null, $charge_commission_bighub = true)
    {
        $itens_line = [];

        // Valor padrão da comissão
        $percent_commission_bighub = 15;

        // Obtendo os objetos de resposta
        $orderResp = $order->response;
        $orderFirst = $order->response_first;

        $valor_total_dos_portes_da_ordem = 0;
        $valor_portes_cobrado_bighub = 0;
        $valor_comissao_portes_cobrado_bighub = 0;
        $total_sum_commision = 0;

        // Processamento dos itens do pedido DocMorris
        foreach ($orderResp->subOrders as $subOrder) {
            foreach ($subOrder->items as $item) {

                // Determinar o EAN
                $ean = isset($item->product->eans[0]->code) ? $item->product->eans[0]->code : $this->flowOrderService->getEanCode($order->sku[0], $order, $channel);

                // Obtendo a imagem do produto
                $image = $this->flowOrderService->getImgProduct($ean, (int)$user_id);

                // Cálculo do preço total do item
                $item_total_price = $item->unitPrice->amount * $item->unitsToSend;

                // Cálculo das comissões (DocMorris não tem commission_fee, então usamos padrão)
                $percent_commission_bighub = 15; // Padrão para DocMorris

                $total_commission_item = 0;
                if ($charge_commission_bighub) {
                    $total_commission_item = round(($percent_commission_bighub / 100) * $item_total_price, 2);
                }

                $total_commission_bighub_item = floatval($total_commission_item);
                $percent_commission_marketplace_item = 0; // DocMorris não fornece essa informação
                $total_commission_marketplace_item = 0;

                $valor_portes_cobrado_bighub_item = 0;
                $valor_comissao_portes_cobrado_bighub_item = 0;

                // Construção do item para o array de itens
                $itens_line[] = [
                    "title" => $item->product->name,
                    "ean" => $ean,
                    "sku" => $order->sku[0] ?? $ean, // DocMorris não tem SKU separado por item
                    "image" => $image,
                    "state" => 'SHIPPING',
                    "shipping_price" => 0, // DocMorris não cobra frete separado por item
                    "shipping_taxes" => [],
                    "taxes" => [
                        [
                            "amount" => ($item_total_price * $item->vat / 100),
                            "rate" => floatval($item->vat)
                        ]
                    ],
                    "commission_fee" => floatval($total_commission_item) ?? 0,
                    "commission_rate_vat" => $item->vat ?? 0,
                    "commission_taxes" => [
                        [
                            "amount" => floatval($total_commission_item) ?? 0,
                            "rate" => $item->vat ?? 0
                        ]
                    ],
                    "commission_vat" => ($total_commission_item * $item->vat / 100) ?? 0,
                    "total_commission" => floatval($total_commission_item),
                    "total_price" => floatval($item_total_price),
                    "price_unit" => floatval($item->unitPrice->amount),
                    "price" => floatval($item->unitPrice->amount),
                    "quantity" => floatval($item->unitsToSend),
                    "leadtime_to_ship" => 7, // Padrão para DocMorris
                    "finance" => [
                        "percent_commission_bighub" => $percent_commission_bighub,
                        "total_commission_bighub" => $total_commission_bighub_item,
                        "total_shipping_bighub" => $valor_portes_cobrado_bighub_item ?? 0,
                        "total_shipping_comission_bighub" => $valor_comissao_portes_cobrado_bighub_item ?? 0,
                        "total_sold_marketplace" => floatval($item_total_price),
                        "percent_commission_marketplace" => round($percent_commission_marketplace_item, 2),
                        "total_commission_marketplace" => $total_commission_marketplace_item
                    ]
                ];

                $total_sum_commision = $total_sum_commision + $total_commission_item;
            }
        }

        // Cálculo da comissão total para o pedido
        $order_price = isset($order->price) ? floatval($order->price) : floatval($order->price);
        $percent_commission_bighub = 15; // Padrão para DocMorris

        // Calculo do total da comissão bighub
        $total_commission_bighub = floatval($total_sum_commision);
        if ($charge_commission_bighub) {
            $total_commission_bighub = round(($percent_commission_bighub / 100) * $order_price, 2);
        }

        // Cálculo da comissão do marketplace
        $percent_commission_marketplace = $this->flowOrderService->calcularPorcentagemProporcional(floatval($total_sum_commision), $order_price);
        $total_commission_marketplace = floatval($total_sum_commision);

        // Obter país do cliente (DocMorris usa structure diferente)
        $customer_country = $this->flowOrderService->getCountry($orderResp->shippingAddress->country);

        // Informações adicionais (para casos com account > 1)
        $additional_info = [];
        if (isset($order->marketplace->account) && $order->marketplace->account > 1) {
            $additional_info[] = [
                'key' => $channel,
                'value' => 'channel-' . $order->marketplace->account
            ];
        }
        $assigned_offer = [];
        if (!empty($found_seller)) {
            $assigned_offer[] = $found_seller;
        }

        // Construção do payload adaptado para DocMorris
        $payload = [
            "user_id" => (int)$user_id,
            "reference" => $orderResp->code,
            "channel" => $channel,
            "country" => $country_mkt,
            "state" => 'SHIPPING',
            "total_price" => $order_price,
            "total_commission" => floatval($total_commission_bighub),
            "shipping_time" => 7, // Padrão para DocMorris
            "customer" => [
                "first_name" => explode(' ', $orderResp->shippingAddress->name)[0] ?? $orderResp->shippingAddress->name,
                "last_name" => $orderResp->shippingAddress->lastName ?? '',
                "billing_address" => [
                    "city" => $orderResp->billingAddress->city,
                    "company" => "",
                    "country" => $customer_country,
                    "country_iso_code" => $orderResp->billingAddress->country ?? '',
                    "first_name" => explode(' ', $orderResp->billingAddress->name)[0] ?? $orderResp->billingAddress->name,
                    "last_name" => $orderResp->billingAddress->lastName ?? '',
                    "phone" => $orderResp->billingAddress->phone ?? null,
                    "phone_secondary" => null,
                    "street_1" => $orderResp->billingAddress->street . ' ' . $orderResp->billingAddress->streetNumber,
                    "street_2" => $orderResp->billingAddress->addressDetails ?? null,
                    "zip_code" => $orderResp->billingAddress->postalCode,
                ],
                "shipping_address" => [
                    "city" => $orderResp->shippingAddress->city,
                    "company" => "",
                    "country" => $customer_country,
                    "country_iso_code" => $orderResp->shippingAddress->country ?? '',
                    "first_name" => explode(' ', $orderResp->shippingAddress->name)[0] ?? $orderResp->shippingAddress->name,
                    "last_name" => $orderResp->shippingAddress->lastName ?? '',
                    "phone" => $orderResp->shippingAddress->phone ?? null,
                    "phone_secondary" => null,
                    "street_1" => $orderResp->shippingAddress->street . ' ' . $orderResp->shippingAddress->streetNumber,
                    "street_2" => $orderResp->shippingAddress->addressDetails ?? null,
                    "zip_code" => $orderResp->shippingAddress->postalCode,
                ]
            ],
            "payment" => [
                "type" => "" // DocMorris não fornece tipo de pagamento
            ],
            "items" => $itens_line,
            "finance" => [
                "percent_commission_bighub" => $percent_commission_bighub,
                "total_commission_bighub" => floatval($total_commission_bighub),
                "total_shipping_bighub" => $valor_portes_cobrado_bighub ?? 0,
                "total_shipping_comission_bighub" => $valor_comissao_portes_cobrado_bighub ?? 0,
                "total_sold_marketplace" => $order_price,
                "percent_commission_marketplace" => round($percent_commission_marketplace, 2),
                "total_commission_marketplace" => $total_commission_marketplace
            ],
            "created_date" => date("Y-m-d H:i:s", strtotime($orderResp->createdAt))
        ];

        // Adicionando informações específicas se existirem
        $payload["additional_info"] = $additional_info;
        $payload["assigned_offer"] = $assigned_offer;

        if ($channel != 'elcorteingles') {
            $resulValidate = $this->flowOrderService->comparaComissoes($payload, $order);
            if ($resulValidate) {
                return $payload;
            } else {
                \Log::warning("A comissão BigHub não é maior que a comissão Marketplace.");
                return false;
            }
        } else {
            return $payload;
        }
    }


}
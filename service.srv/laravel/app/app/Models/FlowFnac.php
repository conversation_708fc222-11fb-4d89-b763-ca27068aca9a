<?php

namespace App\Models;
use App\Services\OrderFlowService;

use Illuminate\Database\Eloquent\Model;

class FlowFnac extends Model
{

    protected $flowService;

    public function __construct(OrderFlowService $flowService)
    {
        $this->flowOrderService = $flowService;
    }


     /**
     * Gera o payload padronizado para qualquer marketplace Mirakl
     *
     * @param string $marketplace Nome do marketplace
     * @param object $order Objeto contendo os dados do pedido
     * @param int $user_id ID do usuário
     * @param string $channel Canal de origem
     * @param bool $yes Flag para cálculo de comissão alternativo
     * @param mixed $found_seller Dados do vendedor (opcional)
     * @return array Payload formatado
     */
    public function generatePayloadFnac($channel, $order, $user_id, $country_mkt, $found_seller = null, $charge_commission_bighub = true)
    {
        $itens_line = [];

        // Valor padrão da comissão
        $percent_commission_bighub = 15;

        // Obtendo os objetos de resposta
        $orderResp = $order->response;
        $orderFirst = $order->response_first;

        $valor_total_dos_portes_da_ordem = 0;
        $valor_portes_cobrado_bighub = 0;
        $valor_comissao_portes_cobrado_bighub = 0;
        $total_sum_commision = 0;


        // Processamento dos itens do pedido
        foreach ($orderResp->order_lines as $item) {

            // Determinar o EAN
            $ean = $this->flowOrderService->getEanCode($item->offer_sku, $order, $channel);

            // Obtendo a imagem do produto
            $image = $this->flowOrderService->getImgProduct($ean, (int)$user_id);

            // Cálculo das comissões
            $percent_commission_bighub = $this->flowOrderService->porcentagemaCobrar($item->total_price, $item->commission_fee);

            $total_commission_item = floatval($item->commission_fee) ?? 0;
            if ($charge_commission_bighub) {
                $total_commission_item = round(($percent_commission_bighub / 100) * $item->total_price, 2);
            }

            $total_commission_bighub_item = floatval($total_commission_item);
            $percent_commission_marketplace_item = $this->flowOrderService->calcularPorcentagemProporcional(floatval($item->commission_fee), $item->total_price);
            $total_commission_marketplace_item = floatval($item->commission_fee) ?? 0;

            $valor_portes_cobrado_bighub_item = 0;
            $valor_comissao_portes_cobrado_bighub_item = 0;

            // Construção do item para o array de itens
            $itens_line[] = [
                "title" => $item->product_title,
                "ean" => $ean,
                "sku" => $item->offer_sku,
                "image" => $image,
                "state" => 'SHIPPING',
                "shipping_price" => floatval($item->shipping_price),
                "shipping_taxes" => [],
                "taxes" => [],
                "commission_fee" => floatval($total_commission_item) ?? 0,
                "commission_rate_vat" => $item->commission_rate_vat ?? 0,
                "commission_taxes" => [
                    [
                        "amount" => floatval($total_commission_item) ?? 0,
                        "rate" => $item->commission_taxes[0]->rate ?? 0
                    ]
                ],
                "commission_vat" => $item->commission_vat ?? 0,
                "total_commission" => floatval($total_commission_item),
                "total_price" => floatval($item->total_price),
                "price_unit" => floatval($item->price),
                "price" => floatval($item->price),
                "quantity" => floatval($item->quantity),
                "leadtime_to_ship" => $orderFirst->leadtime_to_ship ?? 7,
                "finance" => [
                    "percent_commission_bighub" => $percent_commission_bighub,
                    "total_commission_bighub" => $total_commission_bighub_item,
                    "total_shipping_bighub" => $valor_portes_cobrado_bighub_item ?? 0,
                    "total_shipping_comission_bighub" => $valor_comissao_portes_cobrado_bighub_item ?? 0,
                    "total_sold_marketplace" => floatval($item->total_price),
                    "percent_commission_marketplace" => round($percent_commission_marketplace_item, 2),
                    "total_commission_marketplace" => $total_commission_marketplace_item
                ]
            ];

            $total_sum_commision = $total_sum_commision + $total_commission_item;
        }

        // Cálculo da comissão total para o pedido
        $order_price = isset($order->price) ? floatval($order->price) : floatval($order->price);
        $percent_commission_bighub = $this->flowOrderService->porcentagemaCobrar($order_price, $total_sum_commision);

        // Calculo do total da comissão bighub
        $total_commission_bighub = floatval($total_sum_commision);
        if ($charge_commission_bighub) {
            $total_commission_bighub = round(($percent_commission_bighub / 100) * $order_price, 2);
        }

        // Cálculo da comissão do marketplace
        $percent_commission_marketplace = $this->flowOrderService->calcularPorcentagemProporcional(floatval($total_sum_commision), $order_price);
        $total_commission_marketplace = floatval($total_sum_commision);


        // Obter país do cliente
        if (isset($orderResp->response->customer->shipping_address->country)) {
            $customer_country = $this->flowOrderService->getCountry($orderResp->response->customer->shipping_address->country);
        } else {
            echo 'error country not found. OrderId: '.$order->order_id;
            \Log::warning("Error country not found");
            die();
        }

        // Informações adicionais (para casos com account > 1)
        $additional_info = [];
        if (isset($order->marketplace->account) && $order->marketplace->account > 1) {
            $additional_info[] = [
                'key' => $channel,
                'value' => 'channel-' . $order->marketplace->account
            ];
        }
        $assigned_offer = [];
        if (!empty($found_seller)) {
            $assigned_offer[] = $found_seller;
        }

        // echo json_encode($orderFirst);
        // die();

        // Construção do payload
        $payload = [
            "user_id" => (int)$user_id,
            "reference" => $orderFirst->order_id,
            "channel" => $channel,
            "country" => $country_mkt,
            "state" => 'SHIPPING',
            "total_price" => $order_price,
            "total_commission" => floatval($total_commission_bighub),
            "shipping_time" => $orderFirst->leadtime_to_ship ?? 7,
            "customer" => [
                "first_name" => $orderFirst->client_firstname ? $orderFirst->client_firstname : $orderFirst->shipping_address->firstname,
                "last_name" => $orderFirst->client_lastname ? $orderFirst->client_lastname: $orderFirst->shipping_address->lastname,
                "nif" => $orderFirst->billing_address->nif ?? $orderFirst->shipping_address->nif ?? null,
                "billing_address" => [
                    "city" => $orderFirst->billing_address->city,
                    "company" => $orderFirst->billing_address->company,
                    "country" => $customer_country,
                    "country_iso_code" => $orderFirst->billing_address->country_iso_code ?? '',
                    "first_name" => $orderFirst->billing_address->firstname,
                    "last_name" => $orderFirst->billing_address->lastname,
                    "phone" => $orderFirst->billing_address->phone ?? null,
                    "phone_secondary" => $orderFirst->billing_address->mobile ?? null,
                    "street_1" => $orderFirst->billing_address->address1 ?? null,
                    "street_2" => $orderFirst->billing_address->address2 ?? null,
                    "zip_code" => $orderFirst->billing_address->zipcode,
                ],
                "shipping_address" => [
                    "city" => $orderFirst->shipping_address->city,
                    "company" => $orderFirst->shipping_address->company,
                    "country" => $customer_country,
                    "country_iso_code" => $orderFirst->shipping_address->country_iso_code ?? '',
                    "first_name" => $orderFirst->shipping_address->firstname,
                    "last_name" => $orderFirst->shipping_address->lastname,
                    "phone" => isset($orderFirst->shipping_address->phone) ? $orderResp->shipping_address->phone : (isset($orderResp->billing_address->phone) ? $orderResp->billing_address->phone : null),
                    "phone_secondary" => isset($orderFirst->shipping_address->phone) ? $orderResp->shipping_address->phone : (isset($orderResp->billing_address->mobile) ? $orderResp->billing_address->mobile : null),
                    "street_1" => $orderFirst->shipping_address->address1 ?? null,
                    "street_2" => $orderFirst->shipping_address->address2 ?? null,
                    "zip_code" => $orderFirst->shipping_address->zipcode,
                ]
            ],
            "payment" => [
                "type" => $orderResp->payment_type ?? ""
            ],
            "items" => $itens_line,
            "finance" => [
                "percent_commission_bighub" => $percent_commission_bighub,
                "total_commission_bighub" => floatval($total_commission_bighub),
                "total_shipping_bighub" => $valor_portes_cobrado_bighub ?? 0,
                "total_shipping_comission_bighub" => $valor_comissao_portes_cobrado_bighub ?? 0,
                "total_sold_marketplace" => $order_price,
                "percent_commission_marketplace" => round($percent_commission_marketplace, 2),
                "total_commission_marketplace" => $total_commission_marketplace
            ],
            "created_date" => date("Y-m-d H:i:s", strtotime($orderResp->created_at))
        ];

        // Adicionando informações específicas se existirem
        $payload["additional_info"] = $additional_info;
        $payload["assigned_offer"] = $assigned_offer;

        if ($channel != 'elcorteingles') {
            $resulValidate = $this->flowOrderService->comparaComissoes($payload, $order);
            if ($resulValidate) {
                return $payload;
            } else {
                \Log::warning("A comissão BigHub não é maior que a comissão Marketplace.");
                return false;
            }
        } else {
            return $payload;
        }
    }


}

<?php

namespace App\Models;
use App\Services\OrderFlowService;

use Illuminate\Database\Eloquent\Model;

class FlowManoMano extends Model
{

    protected $flowService;

    public function __construct(OrderFlowService $flowService)
    {
        $this->flowOrderService = $flowService;
    }


     /**
     * Gera o payload padronizado para ManoMano
     *
     * @param string $marketplace Nome do marketplace
     * @param object $order Objeto contendo os dados do pedido
     * @param int $user_id ID do usuário
     * @param string $channel Canal de origem
     * @param mixed $found_seller Dados do vendedor (opcional)
     * @param bool $charge_commission_bighub Flag para cálculo de comissão alternativo
     * @return array Payload formatado
     */
    public function generatePayloadManoMano($channel, $order, $user_id, $country_mkt, $found_seller = null, $charge_commission_bighub = true)
    {
        $itens_line = [];

        // Valor padrão da comissão
        $percent_commission_bighub = 15;

        // Obtendo os objetos de resposta
        $orderResp = $order->response;
        $orderFirst = $order->response_first;

        $valor_total_dos_portes_da_ordem = 0;
        $valor_portes_cobrado_bighub = 0;
        $valor_comissao_portes_cobrado_bighub = 0;
        $total_sum_commision = 0;

        // Processamento dos itens do pedido ManoMano
        foreach ($orderResp->response->content->products as $product) {

            // Determinar o EAN
            $ean = isset($product->seller_sku) ? $product->seller_sku : $this->flowOrderService->getEanCode($product->seller_sku, $order, $channel);

            // Obtendo a imagem do produto
            $image = $this->flowOrderService->getImgProduct($ean, (int)$user_id);

            // Cálculo do preço total do item
            $item_total_price = $product->product_price->amount * $product->quantity;

            // Cálculo das comissões
            $percent_commission_bighub = 15; // Padrão para ManoMano

            $total_commission_item = 0;
            if ($charge_commission_bighub) {
                $total_commission_item = round(($percent_commission_bighub / 100) * $item_total_price, 2);
            }

            $total_commission_bighub_item = floatval($total_commission_item);
            $percent_commission_marketplace_item = 0; // ManoMano não fornece essa informação
            $total_commission_marketplace_item = 0;

            $valor_portes_cobrado_bighub_item = 0;
            $valor_comissao_portes_cobrado_bighub_item = 0;

            // Cálculo do VAT
            $vat_rate = floatval($product->vat_rate);
            $vat_amount = ($item_total_price * $vat_rate / 100);

            // Construção do item para o array de itens
            $itens_line[] = [
                "title" => $product->product_title,
                "ean" => $ean,
                "sku" => $product->seller_sku,
                "image" => $image,
                "state" => 'SHIPPING',
                "shipping_price" => floatval($product->shipping_price->amount ?? 0),
                "shipping_taxes" => [
                    [
                        "amount" => floatval($product->shipping_price_excluding_vat->amount ?? 0),
                        "rate" => floatval($product->shipping_vat_rate ?? 0)
                    ]
                ],
                "taxes" => [
                    [
                        "amount" => $vat_amount,
                        "rate" => $vat_rate
                    ]
                ],
                "commission_fee" => floatval($total_commission_item) ?? 0,
                "commission_rate_vat" => $vat_rate ?? 0,
                "commission_taxes" => [
                    [
                        "amount" => floatval($total_commission_item) ?? 0,
                        "rate" => $vat_rate ?? 0
                    ]
                ],
                "commission_vat" => ($total_commission_item * $vat_rate / 100) ?? 0,
                "total_commission" => floatval($total_commission_item),
                "total_price" => floatval($item_total_price),
                "price_unit" => floatval($product->product_price->amount),
                "price" => floatval($product->product_price->amount),
                "quantity" => floatval($product->quantity),
                "leadtime_to_ship" => 7, // Padrão para ManoMano
                "finance" => [
                    "percent_commission_bighub" => $percent_commission_bighub,
                    "total_commission_bighub" => $total_commission_bighub_item,
                    "total_shipping_bighub" => $valor_portes_cobrado_bighub_item ?? 0,
                    "total_shipping_comission_bighub" => $valor_comissao_portes_cobrado_bighub_item ?? 0,
                    "total_sold_marketplace" => floatval($item_total_price),
                    "percent_commission_marketplace" => round($percent_commission_marketplace_item, 2),
                    "total_commission_marketplace" => $total_commission_marketplace_item
                ]
            ];

            $total_sum_commision = $total_sum_commision + $total_commission_item;
        }

        // Cálculo da comissão total para o pedido
        $order_price = isset($order->price) ? floatval($order->price) : floatval($orderResp->response->content->total_price->amount);
        $percent_commission_bighub = 15; // Padrão para ManoMano

        // Calculo do total da comissão bighub
        $total_commission_bighub = floatval($total_sum_commision);
        if ($charge_commission_bighub) {
            $total_commission_bighub = round(($percent_commission_bighub / 100) * $order_price, 2);
        }

        // Cálculo da comissão do marketplace
        $percent_commission_marketplace = $this->flowOrderService->calcularPorcentagemProporcional(floatval($total_sum_commision), $order_price);
        $total_commission_marketplace = floatval($total_sum_commision);

        // Obter país do cliente
        $customer_country = $this->flowOrderService->getCountry($orderResp->response->content->addresses->shipping->country);

        // Informações adicionais (para casos com account > 1)
        $additional_info = [];
        if (isset($order->marketplace->account) && $order->marketplace->account > 1) {
            $additional_info[] = [
                'key' => $channel,
                'value' => 'channel-' . $order->marketplace->account
            ];
        }

        // Adicionar marketplace_id
        if (isset($order->marketplace->id)) {
            $additional_info[] = [
                'key' => 'marketplace_id',
                'value' => (string) $order->marketplace->id
            ];
        }
        $assigned_offer = [];
        if (!empty($found_seller)) {
            $assigned_offer[] = $found_seller;
        }

        // Separar nome completo do cliente
        $billing_names = explode(' ', $orderResp->response->content->addresses->billing->firstname . ' ' . $orderResp->response->content->addresses->billing->lastname);
        $shipping_names = explode(' ', $orderResp->response->content->addresses->shipping->firstname . ' ' . $orderResp->response->content->addresses->shipping->lastname);

        // Construção do payload adaptado para ManoMano
        $payload = [
            "user_id" => (int)$user_id,
            "reference" => $orderResp->response->content->order_reference,
            "channel" => $channel,
            "country" => $country_mkt,
            "state" => 'SHIPPING',
            "total_price" => $order_price,
            "total_commission" => floatval($total_commission_bighub),
            "shipping_time" => 7, // Padrão para ManoMano
            "customer" => [
                "first_name" => $billing_names[0] ?? '',
                "last_name" => $billing_names[1] ?? '',
                "billing_address" => [
                    "city" => $orderResp->response->content->addresses->billing->city,
                    "company" => $orderResp->response->content->addresses->billing->company ?? "",
                    "country" => $customer_country,
                    "country_iso_code" => $orderResp->response->content->addresses->billing->country_iso ?? '',
                    "first_name" => $orderResp->response->content->addresses->billing->firstname,
                    "last_name" => $orderResp->response->content->addresses->billing->lastname,
                    "phone" => $orderResp->response->content->addresses->billing->phone ?? null,
                    "phone_secondary" => null,
                    "street_1" => $orderResp->response->content->addresses->billing->address_line1,
                    "street_2" => null,
                    "zip_code" => $orderResp->response->content->addresses->billing->zipcode,
                ],
                "shipping_address" => [
                    "city" => $orderResp->response->content->addresses->shipping->city,
                    "company" => $orderResp->response->content->addresses->shipping->company ?? "",
                    "country" => $customer_country,
                    "country_iso_code" => $orderResp->response->content->addresses->shipping->country_iso ?? '',
                    "first_name" => $orderResp->response->content->addresses->shipping->firstname,
                    "last_name" => $orderResp->response->content->addresses->shipping->lastname,
                    "phone" => $orderResp->response->content->addresses->shipping->phone ?? null,
                    "phone_secondary" => null,
                    "street_1" => $orderResp->response->content->addresses->shipping->address_line1,
                    "street_2" => null,
                    "zip_code" => $orderResp->response->content->addresses->shipping->zipcode,
                ]
            ],
            "payment" => [
                "type" => "" // ManoMano não fornece tipo de pagamento
            ],
            "items" => $itens_line,
            "finance" => [
                "percent_commission_bighub" => $percent_commission_bighub,
                "total_commission_bighub" => floatval($total_commission_bighub),
                "total_shipping_bighub" => $valor_portes_cobrado_bighub ?? 0,
                "total_shipping_comission_bighub" => $valor_comissao_portes_cobrado_bighub ?? 0,
                "total_sold_marketplace" => $order_price,
                "percent_commission_marketplace" => round($percent_commission_marketplace, 2),
                "total_commission_marketplace" => $total_commission_marketplace
            ],
            "created_date" => date("Y-m-d H:i:s", strtotime($orderResp->response->content->created_at))
        ];

        // Adicionando informações específicas se existirem
        $payload["additional_info"] = $additional_info;
        $payload["assigned_offer"] = $assigned_offer;

        if ($channel != 'elcorteingles') {
            $resulValidate = $this->flowOrderService->comparaComissoes($payload, $order);
            if ($resulValidate) {
                return $payload;
            } else {
                \Log::warning("A comissão BigHub não é maior que a comissão Marketplace.");
                return false;
            }
        } else {
            return $payload;
        }
    }


}

<?php

namespace App\Models;
use App\Services\OrderFlowService;

use Illuminate\Database\Eloquent\Model;

class FlowStockly extends Model
{

    protected $flowOrderService;

    public function __construct(OrderFlowService $flowService)
    {
        $this->flowOrderService = $flowService;
    }


    /**
     * Gera o payload padronizado para Stockly
     *
     * @param string $channel Canal de origem
     * @param object $order Objeto contendo os dados do pedido
     * @param int $user_id ID do usuário
     * @param string $country_mkt País do marketplace
     * @param mixed $found_seller Dados do vendedor (opcional)
     * @param bool $charge_commission_bighub Flag para cálculo de comissão alternativo
     * @return array Payload formatado
     */
    public function generatePayloadStockly($channel, $order, $user_id, $country_mkt, $found_seller = null, $charge_commission_bighub = true)
    {
        $channel = 'mktbighub';

        $itens_line = [];

        // Valor padrão da comissão
        $percent_commission_bighub = 15;

        // Obtendo os objetos de resposta
        $orderFirst = $order->response_first;

        $valor_portes_cobrado_bighub = 0;
        $valor_comissao_portes_cobrado_bighub = 0;
        $total_sum_commision = 0;

        // Processamento dos itens do pedido Stockly
        foreach ($orderFirst->items_line as $item) {

            // Determinar o EAN
            $ean = $item->ean;

            // Obtendo a imagem do produto
            $image = $item->image ?? $this->flowOrderService->getImgProduct($ean, (int)$user_id);

            // Cálculo do preço total do item
            $item_total_price = floatval($item->total_price);
            $price_unit = floatval($item->price_unit);
            $quantity = floatval($item->quantity);

            // Cálculo das comissões
            $percent_commission_bighub = 15; // Padrão para Stockly

            $total_commission_item = 0;
            if ($charge_commission_bighub) {
                $total_commission_item = round(($percent_commission_bighub / 100) * $item_total_price, 2);
            }

            $total_commission_bighub_item = floatval($total_commission_item);
            $percent_commission_marketplace_item = 0;
            $total_commission_marketplace_item = 0;

            $valor_portes_cobrado_bighub_item = 0;
            $valor_comissao_portes_cobrado_bighub_item = 0;

            // Construção do item para o array de itens
            $itens_line[] = [
                "title" => $item->title,
                "ean" => $ean,
                "sku" => $ean,
                "image" => $image,
                "state" => 'SHIPPING',
                "shipping_price" => 0,
                "shipping_taxes" => [],
                "taxes" => [],
                "commission_fee" => floatval($total_commission_item) ?? 0,
                "commission_rate_vat" => 0,
                "commission_taxes" => [
                    [
                        "amount" => floatval($total_commission_item) ?? 0,
                        "rate" => 0
                    ]
                ],
                "commission_vat" => 0,
                "total_commission" => floatval($total_commission_item),
                "total_price" => $item_total_price,
                "price_unit" => $price_unit,
                "price" => $price_unit,
                "quantity" => $quantity,
                "leadtime_to_ship" => $item->leadtime_to_ship ?? 7,
                "description" => $item->description ?? "",
                "finance" => [
                    "percent_commission_bighub" => $percent_commission_bighub,
                    "total_commission_bighub" => $total_commission_bighub_item,
                    "total_shipping_bighub" => $valor_portes_cobrado_bighub_item ?? 0,
                    "total_shipping_comission_bighub" => $valor_comissao_portes_cobrado_bighub_item ?? 0,
                    "total_sold_marketplace" => $item_total_price,
                    "percent_commission_marketplace" => round($percent_commission_marketplace_item, 2),
                    "total_commission_marketplace" => $total_commission_marketplace_item
                ]
            ];

            $total_sum_commision = $total_sum_commision + $total_commission_item;
        }

        // Cálculo do preço total do pedido
        $order_price = floatval($order->price);

        // Calculo do total da comissão bighub
        $total_commission_bighub = floatval($total_sum_commision);
        if ($charge_commission_bighub) {
            $total_commission_bighub = round(($percent_commission_bighub / 100) * $order_price, 2);
        }

        // Cálculo da comissão do marketplace
        $percent_commission_marketplace = $this->flowOrderService->calcularPorcentagemProporcional(floatval($total_sum_commision), $order_price);
        $total_commission_marketplace = floatval($total_sum_commision);

        // Obter país do cliente
        $customer_country = $this->flowOrderService->getCountry($orderFirst->customer->destination_country);

        // Informações adicionais
        $additional_info = [];
        if (isset($order->marketplace->account) && $order->marketplace->account > 1) {
            $additional_info[] = [
                'key' => $channel,
                'value' => 'channel-' . $order->marketplace->account
            ];
        }

        // Adicionar marketplace_id
        if (isset($order->marketplace->id)) {
            $additional_info[] = [
                'key' => 'marketplace_id',
                'value' => (string) $order->marketplace->id
            ];
        }

        $assigned_offer = [];
        if (!empty($found_seller)) {
            $assigned_offer[] = $found_seller;
        }

        // Construção do payload adaptado para Stockly
        $payload = [
            "user_id" => (int)$user_id,
            "reference" => $orderFirst->order_ref,
            "channel" => $channel,
            "country" => $country_mkt,
            "state" => 'SHIPPING',
            "total_price" => $order_price,
            "total_commission" => floatval($total_commission_bighub),
            "shipping_time" => !empty($orderFirst->items_line) ? ($orderFirst->items_line[0]->leadtime_to_ship ?? 7) : 7,
            "customer" => [
                "first_name" => $orderFirst->customer->first_name,
                "last_name" => $orderFirst->customer->last_name,
                "billing_address" => [
                    "city" => $orderFirst->customer->billing_address->city,
                    "company" => $orderFirst->customer->billing_address->company ?? "",
                    "country" => $this->flowOrderService->getCountry($orderFirst->customer->billing_address->country),
                    "country_iso_code" => $orderFirst->customer->billing_address->country ?? '',
                    "first_name" => $orderFirst->customer->billing_address->first_name,
                    "last_name" => $orderFirst->customer->billing_address->last_name,
                    "phone" => $orderFirst->customer->billing_address->phone ?? null,
                    "phone_secondary" => null,
                    "street_1" => $orderFirst->customer->billing_address->street_1,
                    "street_2" => $orderFirst->customer->billing_address->street_2 ?? null,
                    "zip_code" => $orderFirst->customer->billing_address->zip_code,
                ],
                "shipping_address" => [
                    "city" => $orderFirst->customer->shipping_address->city,
                    "company" => $orderFirst->customer->shipping_address->company ?? "",
                    "country" => $this->flowOrderService->getCountry($orderFirst->customer->shipping_address->country),
                    "country_iso_code" => $orderFirst->customer->shipping_address->country ?? '',
                    "first_name" => $orderFirst->customer->shipping_address->first_name,
                    "last_name" => $orderFirst->customer->shipping_address->last_name,
                    "phone" => $orderFirst->customer->shipping_address->phone ?? null,
                    "phone_secondary" => null,
                    "street_1" => $orderFirst->customer->shipping_address->street_1,
                    "street_2" => $orderFirst->customer->shipping_address->street_2 ?? null,
                    "zip_code" => $orderFirst->customer->shipping_address->zip_code,
                ]
            ],
            "payment" => [
                "type" => ""
            ],
            "items" => $itens_line,
            "finance" => [
                "percent_commission_bighub" => $percent_commission_bighub,
                "total_commission_bighub" => floatval($total_commission_bighub),
                "total_shipping_bighub" => $valor_portes_cobrado_bighub ?? 0,
                "total_shipping_comission_bighub" => $valor_comissao_portes_cobrado_bighub ?? 0,
                "total_sold_marketplace" => $order_price,
                "percent_commission_marketplace" => round($percent_commission_marketplace, 2),
                "total_commission_marketplace" => $total_commission_marketplace
            ],
            "created_date" => $orderFirst->date
        ];

        // Adicionando informações específicas se existirem
        $payload["additional_info"] = $additional_info;
        $payload["assigned_offer"] = $assigned_offer;

        return $payload;
    }

}

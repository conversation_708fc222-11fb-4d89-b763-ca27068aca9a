# Função getSeller

## Descrição
A função getSeller localiza um vendedor adequado para um produto baseado no código EAN, canal de venda e preço. Implementa uma estratégia de busca em três etapas para encontrar o melhor vendedor disponível no sistema.

## Parâmetros
- $ean: Código EAN do produto
- $channel: Canal de venda/marketplace
- $price: Preço do produto
- $order_id: ID do pedido (opcional)
- $tbl_id: ID da tabela de pedidos (opcional)

## Retorno
- Objeto com informações do vendedor encontrado
- null se nenhum vendedor adequado for encontrado

## Estratégia de Busca
1. BUSCA EXATA:
   - Procura vendedor com preço exatamente igual ao preço do pedido
   - Filtros: mesmo EAN, mesmo canal, usuário ativo, estoque > 0
   - Ordenação: preço decrescente

2. BUSCA POR PREÇO MENOR OU IGUAL:
   - Procura vendedor com preço menor ou igual ao preço do pedido
   - Mesmos filtros da busca exata, mas com preço <= preço do pedido
   - Ordenação: preço decrescente (para encontrar o melhor preço mais próximo do original)

3. BUSCA COM TOLERÂNCIA DE 5%:
   - Procura vendedor com preço até 5% maior que o preço do pedido
   - Mesmos filtros, mas com preço <= preço do pedido * 1.05
   - Ordenação: preço crescente

## Caso Especial
Se nenhum vendedor for encontrado e o modo debug estiver ativo, o pedido é encaminhado para o suporte (ID 25).

## Dependências
- Modelo ProductChannel
- Modelo UserClient
- Método getSellerDetails

## Observações
- Em todas as buscas, verifica-se se o produto tem estoque disponível (stock > 0)
- Considera apenas vendedores ativos (is_active = 1)
- O método getSellerDetails adiciona informações como delivery_by e user_active
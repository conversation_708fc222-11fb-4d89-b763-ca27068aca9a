<?php
require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed', 'method' => $_SERVER['REQUEST_METHOD']]);
    exit;
}

// Ler o JSON do input
$jsonData = file_get_contents('php://input');
Log::info('Forward.php - Raw JSON received', ['raw_json' => $jsonData]);

$postData = json_decode($jsonData, true);

// Verificar se o JSON é válido
if (json_last_error() !== JSON_ERROR_NONE) {
    Log::error('Forward.php - JSON inválido', [
        'error' => json_last_error_msg(),
        'raw_json' => $jsonData
    ]);
    http_response_code(400);
    echo json_encode(['error' => 'Invalid JSON', 'details' => json_last_error_msg()]);
    exit;
}

Log::info('Forward.php - JSON decodificado', ['post_data' => $postData]);

// Validar parâmetros necessários
$orderId = $postData['order_id'] ?? null;
$clientId = $postData['client_id'] ?? null;

if (!$orderId || !$clientId) {
    Log::error('Repasse - Parâmetros obrigatórios ausentes', [
        'order_id' => $orderId,
        'client_id' => $clientId,
        'received_data' => $postData
    ]);
    http_response_code(400);
    echo json_encode(['error' => 'Missing required parameters', 'order_id' => $orderId, 'client_id' => $clientId]);
    exit;
}

// Criar um objeto Request com os dados do POST
$requestData = [
    'channel' => $postData['channel'] ?? null,
    'id_usuario' => $postData['id_usuario'] ?? null,
    'order_type' => $postData['order_type'] ?? null,
    'order_id' => $orderId,
    'client_id' => $clientId
];

Log::info('Forward.php - Criando request para controller', ['request_data' => $requestData]);

try {
    $request = Request::create(
        $_SERVER['REQUEST_URI'],
        'POST',
        $requestData
    );

    Log::info('Forward.php - Request criado, instanciando controller');

    // Instanciar o controller e chamar o método
    $controller = app()->make(\App\Http\Controllers\OrderController::class);

    Log::info('Forward.php - Controller instanciado, processando order');

    $response = $controller->processOrdersRequestedBySupport($request);

    Log::info('Forward.php - Processamento concluído', [
        'status_code' => $response->getStatusCode()
    ]);

    // Obter e enviar a resposta
    http_response_code($response->getStatusCode());
    echo $response->getContent();
    exit;

} catch (\Exception $e) {
    Log::error('Forward.php - Exception capturada', [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ]);

    http_response_code(500);
    echo json_encode([
        'error' => 'Internal server error',
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
    exit;
} catch (\Throwable $e) {
    Log::error('Forward.php - Throwable capturado', [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ]);

    http_response_code(500);
    echo json_encode([
        'error' => 'Fatal error',
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
    exit;
}
<?php
require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo 'Access denied.';
    exit;
}

// Ler o JSON do input
$jsonData = file_get_contents('php://input');
$postData = json_decode($jsonData, true);

// Validar parâmetros necessários
$orderId = $postData['order_id'] ?? null;
$clientId = $postData['client_id'] ?? null;

// var_dump($orderId, $clientId);
// die();

if (!$orderId || !$clientId) {
    Log::error('Repasse - Parâmetros obrigatórios ausentes');
    http_response_code(400);
    echo "Access denied..";
    exit;
}

// Criar um objeto Request com os dados do POST
$request = Request::create(
    $_SERVER['REQUEST_URI'],
    'POST',
    [
        'channel' => $postData['channel'] ?? null,
        'id_usuario' => $postData['id_usuario'] ?? null,
        'order_type' => $postData['order_type'] ?? null,
        'order_id' => $orderId,
        'client_id' => $clientId
    ]
);

// Instanciar o controller e chamar o método
$controller = app()->make(\App\Http\Controllers\OrderController::class);
$response = $controller->processOrdersRequestedBySupport($request);

// Obter e enviar a resposta
echo $response->getContent();
exit;
<?php
require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\Log;

// Permitir apenas POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed. Use POST.'
    ]);
    exit;
}

// Ler o JSON do input
$jsonData = file_get_contents('php://input');
$postData = json_decode($jsonData, true);

// Validar JSON
if (json_last_error() !== JSON_ERROR_NONE) {
    Log::error('FTP Tracking - Invalid JSON', ['error' => json_last_error_msg()]);
    http_response_code(400);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Invalid JSON format',
        'error' => json_last_error_msg()
    ]);
    exit;
}

// Validar campos obrigatórios
$order = $postData['order'] ?? null;
$trackingNr = $postData['tracking_nr'] ?? null;
$carrier = $postData['carrier'] ?? null;
$carrierUrl = $postData['carrier_url'] ?? null;
$date = $postData['date'] ?? date('d/m/Y');

// Validação
$errors = [];
if (!$order) $errors[] = 'Field "order" is required';
if (!$trackingNr) $errors[] = 'Field "tracking_nr" is required';
if (!$carrier) $errors[] = 'Field "carrier" is required';
if ($carrierUrl && !filter_var($carrierUrl, FILTER_VALIDATE_URL)) {
    $errors[] = 'Field "carrier_url" must be a valid URL';
}

if (!empty($errors)) {
    Log::error('FTP Tracking - Validation failed', [
        'errors' => $errors,
        'payload' => $postData
    ]);
    http_response_code(422);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Validation failed',
        'errors' => $errors
    ]);
    exit;
}

try {
    // Gerar conteúdo CSV
    $csvContent = "order,tracking_nr,carrier,carrier_url,date\n";
    $csvContent .= sprintf(
        "%s,%s,%s,%s,%s\n",
        $order,
        $trackingNr,
        $carrier,
        $carrierUrl,
        $date
    );

    // Gerar nome do arquivo: tracking-{order}-{ddmmyyyy}.csv
    $dateParts = explode('/', $date);
    $dateFormatted = $dateParts[0] . $dateParts[1] . $dateParts[2]; // DDMMYYYY
    $filename = "tracking-{$order}-{$dateFormatted}.csv";

    // Criar arquivo temporário
    $tempFile = tempnam(sys_get_temp_dir(), 'ftp_tracking_');
    file_put_contents($tempFile, $csvContent);

    // Obter credenciais FTP do .env
    $ftpHost = env('FTP_HOST');
    $ftpPort = env('FTP_PORT', 21);
    $ftpUser = env('FTP_USER');
    $ftpPassword = env('FTP_PASSWORD');

    // Validar configuração FTP
    if (!$ftpHost || !$ftpUser || !$ftpPassword) {
        throw new Exception('FTP configuration is incomplete. Please check .env file.');
    }

    // Conectar ao FTP
    $ftpConnection = ftp_connect($ftpHost, $ftpPort, 30);
    if (!$ftpConnection) {
        throw new Exception("Failed to connect to FTP server: {$ftpHost}:{$ftpPort}");
    }

    // Login FTP
    $login = ftp_login($ftpConnection, $ftpUser, $ftpPassword);
    if (!$login) {
        ftp_close($ftpConnection);
        throw new Exception('FTP login failed. Please check credentials.');
    }

    // Ativar modo passivo
    ftp_pasv($ftpConnection, true);

    // Criar diretórios se não existirem
    $targetDir = '/orders/tracking';

    // Criar /orders se não existir
    if (!@ftp_chdir($ftpConnection, '/orders')) {
        @ftp_mkdir($ftpConnection, '/orders');
    }
    @ftp_chdir($ftpConnection, '/');

    // Criar /orders/tracking se não existir
    if (!@ftp_chdir($ftpConnection, '/orders/tracking')) {
        @ftp_mkdir($ftpConnection, '/orders/tracking');
    }
    @ftp_chdir($ftpConnection, '/');

    // Mudar para diretório de destino
    if (!@ftp_chdir($ftpConnection, $targetDir)) {
        ftp_close($ftpConnection);
        throw new Exception("Failed to change to directory: {$targetDir}");
    }

    // Upload do arquivo
    $upload = ftp_put($ftpConnection, $filename, $tempFile, FTP_ASCII);

    // Fechar conexão e remover arquivo temporário
    ftp_close($ftpConnection);
    unlink($tempFile);

    if (!$upload) {
        throw new Exception("Failed to upload file: {$filename}");
    }

    // Log de sucesso
    Log::info('FTP Tracking Upload - Success', [
        'order' => $order,
        'tracking_nr' => $trackingNr,
        'carrier' => $carrier,
        'filename' => $filename,
        'ftp_host' => $ftpHost
    ]);

    // Resposta de sucesso
    http_response_code(200);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'message' => 'Tracking information uploaded successfully',
        'data' => [
            'order' => $order,
            'tracking_nr' => $trackingNr,
            'carrier' => $carrier,
            'carrier_url' => $carrierUrl,
            'date' => $date,
            'filename' => $filename
        ]
    ]);

} catch (Exception $e) {
    // Log de erro
    Log::error('FTP Tracking Upload - Error', [
        'message' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'payload' => $postData
    ]);

    // Resposta de erro
    http_response_code(500);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Failed to upload tracking information',
        'error' => $e->getMessage()
    ]);
}

exit;

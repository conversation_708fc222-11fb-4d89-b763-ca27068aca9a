<?php
require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\Log;
use App\Models\Invoice;

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo "Method Not Allowed";
    exit;
}

$transactionId = $_POST['transaction_id'] ?? null;
$notificationId = $_POST['notification_id'] ?? null;

if (!$transactionId || !$notificationId) {
    Log::error('PagHiper webhook - Missing parameters');
    http_response_code(400);
    echo "Missing parameters";
    exit;
}

$apiKey = env('PAGHIPER_API_KEY');
$token = env('PAGHIPER_TOKEN');

$data = [
    'token' => $token,
    'apiKey' => $apiKey,
    'transaction_id' => $transactionId,
    'notification_id' => $notificationId
];

$result = sendNotificationRequest($data);
processResult($result);

http_response_code(200);
echo "OK";
exit;

function sendNotificationRequest($data)
{
    $dataPost = json_encode($data);
    $url = "https://pix.paghiper.com/invoice/notification/";
    $mediaType = "application/json";
    $charSet = "UTF-8";

    $headers = [
        "Accept: " . $mediaType,
        "Accept-Charset: " . $charSet,
        "Accept-Encoding: " . $mediaType,
        "Content-Type: " . $mediaType . ";charset=" . $charSet
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $dataPost);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    curl_close($ch);

    return [
        'http_code' => $httpCode,
        'body' => $result
    ];
}

function processResult($response)
{
    if ($response['http_code'] != 201) {
        Log::error('PagHiper API error', [
            'http_code' => $response['http_code'],
            'body' => $response['body']
        ]);
        return;
    }

    $json = json_decode($response['body'], true);
    if (!$json) {
        Log::error('Invalid JSON response from PagHiper', [
            'body' => $response['body']
        ]);
        return;
    }

    foreach ($json as $data) {
        $orderId = $data['order_id'] ?? null;
        $status = $data['status'] ?? null;

        if (!$orderId || !$status) {
            Log::error('Missing data in PagHiper response', ['data' => $data]);
            continue;
        }

        try {
            $invoice = Invoice::where('order_id', $orderId)->first();
            if (!$invoice) {
                Log::error('Invoice not found', ['order_id' => $orderId]);
                continue;
            }

            $invoice->status = $status;

            if ($status === 'paid') {
                $invoice->status = 'approved';
            }

            $invoice->save();
        } catch (\Exception $e) {
            Log::error('Error processing PagHiper notification', [
                'error' => $e->getMessage(),
                'order_id' => $orderId
            ]);
        }
    }
}
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $data->name }} - {{ $data->subtitle_landing_page }}</title>
    <style>
        :root {
            --main-color: {{ $data->color1 }};
            --dark-color: {{ $data->color2 }};
        }
    </style>
    <link rel="stylesheet" href="{{ asset('css/lp_styles.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="top-bar">
        <div class="container">
            <div class="top-contact">
                @if($data->show_phone) <!-- Exibe o telefone somente se show_phone = 1 -->
                    <span><i class="fas fa-phone"></i> {{ $data->phone }}</span>
                @endif
            </div>
        </div>
        <div class="language-selector">
            <a href="?lang=pt"><img src="/path/to/flag_pt.png" alt="Português"></a>
            <a href="?lang=en"><img src="/path/to/flag_en.png" alt="English"></a>
            <a href="?lang=fr"><img src="/path/to/flag_fr.png" alt="Français"></a>
            <a href="?lang=es"><img src="/path/to/flag_es.png" alt="Español"></a>
        </div>
    </div>

    <header class="header">
        <div class="profile-container">
            <div class="profile-image">
                @if($data->show_agent_image) <!-- Exibe a imagem do agente somente se show_agent_image = 1 -->
                    <img src="{{ $data->url_img }}" alt="Foto do Corretor">
                @endif
                @if($data->show_company) <!-- Exibe o nome da empresa somente se show_company = 1 -->
                    <div class="profile-badge">{{ $data->company }}</div>
                @endif
            </div>
            <div class="profile-info">
                @if($data->show_name) <!-- Exibe o nome somente se show_name = 1 -->
                    <h1>{{ $data->name }}</h1>
                @endif
                @if($data->show_subtitle) <!-- Exibe o subtítulo somente se show_subtitle = 1 -->
                    <p class="subtitle">{{ $data->subtitle_landing_page }}</p>
                @endif
            </div>
        </div>
    </header>

    <main>
        @if($data->show_properties) <!-- Exibe os imóveis somente se show_properties = 1 -->
            <section class="properties">
                <h2 class="section-title">Imóveis em Destaque</h2>
                <div class="properties-grid">
                    <!-- Imóvel 1 -->
                    <article class="property-card">
                        <div class="property-image">
                            <img src="https://images.egorealestate.com/Z1280x1024/OAYES/S5/C5287/P25102806/Tphoto/IDd6097f01-0000-0500-0000-00001372387d.JPG" alt="Imóvel 1">
                            <div class="property-badge">Luxo</div>
                        </div>
                        <div class="property-info">
                            <h3>Apartamento de Luxo</h3>
                            <p class="location"><i class="fas fa-map-marker-alt"></i> Jardins, São Paulo</p>
                            <p class="price">€ 2.900.000</p>
                            <button class="cta-button">Detalhes</button>
                        </div>
                    </article>

                    <!-- Imóvel 2 -->
                    <article class="property-card">
                        <div class="property-image">
                            <img src="https://images.egorealestate.com/Z1280x1024/OAYES/S5/C1521/P25245356/Tphoto/IDac368101-0000-0500-0000-000013ebe451.jpg" alt="Imóvel 2">
                            <div class="property-badge">Condomínio</div>
                        </div>
                        <div class="property-info">
                            <h3>Casa em Condomínio</h3>
                            <p class="location"><i class="fas fa-map-marker-alt"></i> Alphaville, SP</p>
                            <p class="price">€ 4.500.000</p>
                            <button class="cta-button">Detalhes</button>
                        </div>
                    </article>
                </div>
            </section>
        @endif
    </main>

    <footer class="footer">
        <div class="footer-content">
            <div class="footer-info">
                <h3>Entre em Contato</h3>
                @if($data->show_phone) <!-- Exibe o telefone somente se show_phone = 1 -->
                    <p><i class="fas fa-phone"></i> {{ $data->phone }}</p>
                @endif
                @if($data->show_email) <!-- Exibe o email somente se show_email = 1 -->
                    <p><i class="fas fa-envelope"></i> {{ $data->email }}</p>
                @endif
            </div>
            <div class="footer-social">
                <h3>Redes Sociais</h3>
                <div class="social-links">
                    @if($data->show_instagram) <!-- Exibe o Instagram somente se show_instagram = 1 -->
                        <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
                    @endif
                    @if($data->show_whatsapp) <!-- Exibe o WhatsApp somente se show_whatsapp = 1 -->
                        <a href="#" class="social-icon"><i class="fab fa-whatsapp"></i></a>
                    @endif
                    @if($data->show_facebook) <!-- Exibe o Facebook somente se show_facebook = 1 -->
                        <a href="#" class="social-icon"><i class="fab fa-facebook"></i></a>
                    @endif
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p class="copyright">{{ __('messages.footer_copyright') }}</p>
        </div>
    </footer>
</body>
</html>
